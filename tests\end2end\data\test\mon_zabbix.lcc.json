{"extra_env": {"ZABBIX_CC": "lcc", "OLYMPUS_AGENT_ID": "28"}, "clear_agent_for_next_run": true, "agent_ids": [28], "data": [{"input": {"eventid": "0", "name": "Zabbix Server on iictyizblv031 Heartbeat Message", "value": 1, "severity": 1, "clock": 1739447665, "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "floc_id": "N/A", "ci_id": "Zabbix Server on iictyizblv031", "os": "N/A", "object_1": "N/A", "object_2": "N/A", "object_3": "N/A", "object_4": "N/A", "hostname": ["Zabbix Server on iictyizblv031"], "hostid": ["N/A"], "trigger_comments": "N/A", "triggerid": "N/A"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "Zabbix Server on iictyizblv031", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "Zabbix Server on iictyizblv031 Heartbeat Message", "clear_time": null, "severity": 1}]}}, {"input": {"eventid": "235153214", "name": "Free disk space is less than 10% on volume /var/opt", "value": "1", "severity": "2", "clock": "1739450725", "metric_name": "N/A", "metric_type": "N/A", "floc_id": "N/A", "ci_id": "N/A", "os": ["RedHat 8.10"], "object_1": "SYS", "object_2": "FS", "object_3": "PFREE", "object_4": "/var/opt", "hostname": ["iictniaplv056"], "hostid": ["19501"], "trigger_comments": "SYS::FS::PFREE::/var/opt", "triggerid": "913712"}, "output": {"s2110_alarm": [{"ci_id": "iictniaplv056", "metric_name": "/var/opt", "metric_type": "/FileSystem/FileSystemFreeSpace/", "event_id": "235153214", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "iictniaplv056", "metric_name": "/var/opt", "metric_type": "/FileSystem/FileSystemFreeSpace/", "summary": "Free disk space is less than 10% on volume /var/opt", "clear_time": null, "severity": 2}]}}, {"input": {"eventid": "235156819", "name": "Free disk space is less than 1% on volume /var/log", "value": "1", "severity": "4", "clock": "1739457628", "metric_name": "N/A", "metric_type": "N/A", "floc_id": "N/A", "ci_id": "N/A", "os": ["RedHat 9.5"], "object_1": "SYS", "object_2": "FS", "object_3": "PFREE", "object_4": "/var/log", "hostname": ["iictzizblv020"], "hostid": ["18702"], "trigger_comments": "SYS::FS::PFREE::/var/log", "triggerid": "784958"}, "output": {"s2110_alarm": [{"ci_id": "iictzizblv020", "metric_name": "/var/log", "metric_type": "/FileSystem/FileSystemFreeSpace/", "event_id": "235156819", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "iictzizblv020", "metric_name": "/var/log", "metric_type": "/FileSystem/FileSystemFreeSpace/", "summary": "Free disk space is less than 1% on volume /var/log", "clear_time": null, "severity": 4, "additional_data": {"object_1": "SYS", "object_2": "FS", "object_3": "PFREE", "object_4": "/var/log", "trigger_comments": "SYS::FS::PFREE::/var/log"}}]}}, {"input": {"eventid": "235157653", "name": "Zabbix agent on idiroiorlr012 is unreachable for 5 minutes", "value": "1", "severity": "3", "clock": "1739459267", "metric_name": "N/A", "metric_type": "N/A", "floc_id": "N/A", "ci_id": "N/A", "os": ["OracleLinux 8.10"], "object_1": "SYS", "object_2": "SVC", "object_3": "ZAB", "object_4": "N/A", "hostname": ["idiroiorlr012"], "hostid": ["18772"], "trigger_comments": "SYS::SVC::ZAB::", "triggerid": "799517"}, "output": {"s2110_alarm": [{"ci_id": "idiroiorlr012", "metric_name": "zab", "metric_type": "/System/ProcessAvailability/", "event_id": "235157653", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "idiroiorlr012", "metric_name": "zab", "metric_type": "/System/ProcessAvailability/", "summary": "Zabbix agent on idiroiorlr012 is unreachable for 5 minutes", "clear_time": null, "severity": 3}]}}, {"input": {"eventid": "235157692", "name": "Zabbix agent on idiroiorlr012 is unreachable for 5 minutes", "value": "0", "severity": "0", "clock": "1739459307", "metric_name": "N/A", "metric_type": "N/A", "floc_id": "N/A", "ci_id": "N/A", "os": ["OracleLinux 8.10"], "object_1": "SYS", "object_2": "SVC", "object_3": "ZAB", "object_4": "N/A", "hostname": ["idiroiorlr012"], "hostid": ["18772"], "trigger_comments": "SYS::SVC::ZAB::", "triggerid": "799517"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "idiroiorlr012", "metric_name": "zab", "metric_type": "/System/ProcessAvailability/", "summary": "Zabbix agent on idiroiorlr012 is unreachable for 5 minutes", "clear_time": "2025-02-13T15:08:27", "severity": 0, "raise_time": "2025-02-13T15:08:27", "additional_data": {"object_1": "SYS", "object_2": "SVC", "object_3": "ZAB", "object_4": "", "trigger_comments": "SYS::SVC::ZAB::"}}]}}]}