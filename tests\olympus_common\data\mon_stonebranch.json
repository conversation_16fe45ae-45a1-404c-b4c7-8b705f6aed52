{"data": [{"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T02:00:11.689605574Z", "message": "#<SNMP::SNMPv1_Trap:0x4bccec87 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x3487cfe0 @value=1277437062>, @varbind_list=[#<SNMP::VarBind:0x3f881fa4 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x130c7406 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JAW1662D006\">, #<SNMP::VarBind:0x197e5148 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Windows\">, #<SNMP::VarBind:0x35b9b0c2 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720144702135002430IKIIDV7YE6XRS\">, #<SNMP::VarBind:0x4a33ecee @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x3a6f0dea @name=[*******.4.1.88898.*******.300.1.6], @value=\"IICTZIWFWS003\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x5170fffe @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "538bc476-40e4-450b-9ed8-34673b263ff9", "event.kafka.offset": 654379, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T02:00:11.794Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.opsTaskAgent": "IICTZIWFWS003", "snmptrap.opsTaskName": "JAW1662D006", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Windows", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskExecId": "1720144702135002430IKIIDV7YE6XRS", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Windows", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JAW1662D006", "additional_data": "{\"exec_id\": \"1720144702135002430IKIIDV7YE6XRS\"}", "platform": "ACC", "raise_time": "2024-07-05 02:00:11.689605", "summary": "JAW1662D006 type Windows\nSUCCESS on IICTZIWFWS003", "severity": 1, "clear_time": "2024-07-05 02:00:11.689605", "event_type": "clear", "wake_up_time": "2024-07-05 02:00:11.689605"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-10T06:54:37.593753425Z", "message": "#<SNMP::SNMPv1_Trap:0x6a7b7fca @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x2f318d2f @value=1719065008>, @varbind_list=[#<SNMP::VarBind:0x2072e810 @name=[*******.4.1.88898.*******.300.2.1], @value=\"1\">, #<SNMP::VarBind:0x114646fb @name=[*******.4.1.88898.*******.300.2.2], @value=\"iictziaplv284:7878,iictyiaplv286:7878\">, #<SNMP::VarBind:0x32bdf909 @name=[*******.4.1.88898.*******.300.2.3], @value=\"OMS Server\">, #<SNMP::VarBind:0x38280347 @name=[*******.4.1.88898.*******.300.2.4], @value=\"Connected\">], @specific_trap=2, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0xfad5e3a @value=\"\\n\\xFEJ&\">, @generic_trap=6>", "tags": ["opsConnectorObjects"], "event.uuid": "d0a8dfd7-ea98-4ee3-8748-6b68a010b211", "event.kafka.offset": 664919, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-10T06:54:37.694Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.opsTaskAgent": null, "snmptrap.opsTaskName": null, "snmptrap.opsTaskSeverity": null, "snmptrap.opsTaskType": null, "snmptrap.opsTaskStatusCode": null, "snmptrap.opsTaskExecId": null, "snmptrap.opsConnectorSeverity": "1", "snmptrap.opsConnectorMode": "Connected", "snmptrap.opsConnectorName": "iictziaplv284:7878,iictyiaplv286:7878", "snmptrap.opsConnectorType": "OMS Server"}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Remote File Monitor", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JTU129D001", "additional_data": "{\"exec_id\": \"1720142900012802430JQGJEDMZPM3IE\"}", "platform": "ACC", "raise_time": "2024-07-05 02:01:42.221714", "summary": "JTU129D001 type Remote File Monitor\nFINISHED on IICTCIAPWV405", "severity": 1, "clear_time": "2024-07-05 02:01:42.221714", "event_type": "clear", "wake_up_time": "2024-07-05 02:01:42.221714"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T02:14:20.063486653Z", "message": "#<SNMP::SNMPv1_Trap:0x73c0309e @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x6233d9aa @value=1278285433>, @varbind_list=[#<SNMP::VarBind:0x2698cc16 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x542e5c9b @name=[*******.4.1.88898.*******.300.1.2], @value=\"JDU1617D001\">, #<SNMP::VarBind:0x282c213d @name=[*******.4.1.88898.*******.300.1.3], @value=\"Timer\">, #<SNMP::VarBind:0x423d3c03 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720145546149200430VYMBCYVAKHB3R\">, #<SNMP::VarBind:0x22a5b02e @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x449fa65 @name=[*******.4.1.88898.*******.300.1.6], @value=\"n/a\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x4da51618 @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "ff0cae3e-b6e6-4c64-94a8-790b950de8d5", "event.kafka.offset": 651694, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T02:14:20.163Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.opsTaskAgent": "n/a", "snmptrap.opsTaskName": "JDU1617D001", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Timer", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskExecId": "1720145546149200430VYMBCYVAKHB3R", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Workflow", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "TTU129D00", "additional_data": "{\"exec_id\": \"1720142900012793430PK0LNZKJ4PXBF\"}", "platform": "ACC", "raise_time": "2024-07-05 02:01:42.332900", "summary": "TTU129D00 type Workflow SUCCESS", "severity": 1, "clear_time": "2024-07-05 02:01:42.332900", "event_type": "clear", "wake_up_time": "2024-07-05 02:01:42.332900"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T03:06:58.285153750Z", "message": "#<SNMP::SNMPv1_Trap:0x4c77e5bd @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x2ee00d41 @value=1281443659>, @varbind_list=[#<SNMP::VarBind:0x1a3b1b64 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x69925f93 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JTU734D010\">, #<SNMP::VarBind:0x47b6dde5 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Windows\">, #<SNMP::VarBind:0xb04bc18 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720148700022510430BBRTOG7HGZF24\">, #<SNMP::VarBind:0x49a66627 @name=[*******.4.1.88898.*******.300.1.5], @value=\"FINISHED\">, #<SNMP::VarBind:0x3233c647 @name=[*******.4.1.88898.*******.300.1.6], @value=\"IICTCIAPWV404\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x431b7c11 @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "70402b58-15da-4f3e-a80e-40570b3dafbd", "event.kafka.offset": 649968, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T03:06:58.385Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.opsTaskAgent": "IICTCIAPWV404", "snmptrap.opsTaskName": "JTU734D010", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Windows", "snmptrap.opsTaskStatusCode": "FINISHED", "snmptrap.opsTaskExecId": "1720148700022510430BBRTOG7HGZF24", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Timer", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JDU1617D001", "additional_data": "{\"exec_id\": \"1720144836020851430LS0QK3GA49JSV\"}", "platform": "ACC", "raise_time": "2024-07-05 02:02:20.073760", "summary": "JDU1617D001 type Timer SUCCESS", "severity": 1, "clear_time": "2024-07-05 02:02:20.073760", "event_type": "clear", "wake_up_time": "2024-07-05 02:02:20.073760"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T02:03:01.284517978Z", "message": "#<SNMP::SNMPv1_Trap:0x28772679 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x47bbb2c4 @value=1277606658>, @varbind_list=[#<SNMP::VarBind:0x55b6fa3c @name=[*******.4.1.88898.*******.300.1.1], @value=\"2\">, #<SNMP::VarBind:0x44635a1d @name=[*******.4.1.88898.*******.300.1.2], @value=\"JEU081D000_00_SPLIT\">, #<SNMP::VarBind:0x5e54fdc7 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Workflow\">, #<SNMP::VarBind:0x1df9cd69 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720130400148050430QQZAY8PHK1UN2\">, #<SNMP::VarBind:0x7f7cb3f0 @name=[*******.4.1.88898.*******.300.1.5], @value=\"RUNNING/PROBLEMS\">, #<SNMP::VarBind:0x2111adb0 @name=[*******.4.1.88898.*******.300.1.6], @value=\"n/a\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x58d2d690 @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "1986d85d-018b-40e3-8115-5b0687266055", "event.kafka.offset": 654390, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T02:03:01.385Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.opsTaskAgent": "n/a", "snmptrap.opsTaskName": "JEU081D000_00_SPLIT", "snmptrap.opsTaskSeverity": "2", "snmptrap.opsTaskType": "Workflow", "snmptrap.opsTaskStatusCode": "RUNNING/PROBLEMS", "snmptrap.opsTaskExecId": "1720130400148050430QQZAY8PHK1UN2", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Workflow", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JEU081D000_00_SPLIT", "additional_data": "{\"exec_id\": \"1720130400148050430QQZAY8PHK1UN2\"}", "platform": "ACC", "raise_time": "2024-07-05 02:03:01.284517", "summary": "JEU081D000_00_SPLIT type Workflow RUNNING/PROBLEMS", "severity": 2, "clear_time": null, "event_type": "problem", "wake_up_time": "2024-07-05 02:03:01.284517"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T04:43:52.696661514Z", "message": "#<SNMP::SNMPv1_Trap:0x54c652e5 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x3a9c868c @value=1287258070>, @varbind_list=[#<SNMP::VarBind:0x1889513e @name=[*******.4.1.88898.*******.300.1.1], @value=\"3\">, #<SNMP::VarBind:0x7ebe69b0 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JAM589D731\">, #<SNMP::VarBind:0x20c8380d @name=[*******.4.1.88898.*******.300.1.3], @value=\"Universal\">, #<SNMP::VarBind:0x60badc6d @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720154455044446430UKPVS5DPNYRBU\">, #<SNMP::VarBind:0x326871e5 @name=[*******.4.1.88898.*******.300.1.5], @value=\"RUNNING\">, #<SNMP::VarBind:0x3f967df1 @name=[*******.4.1.88898.*******.300.1.6], @value=\"hrrlyiaplv006-a1445\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x5eff325e @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "7c972eba-bc0f-466a-8107-d2809bec413f", "event.kafka.offset": 650086, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T04:43:52.797Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.opsTaskAgent": "hrrlyiaplv006-a1445", "snmptrap.opsTaskName": "JAM589D731", "snmptrap.opsTaskSeverity": "3", "snmptrap.opsTaskType": "Universal", "snmptrap.opsTaskStatusCode": "RUNNING", "snmptrap.opsTaskExecId": "1720154455044446430UKPVS5DPNYRBU", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Timer", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JDU1617D001", "additional_data": "{\"exec_id\": \"1720145546149200430VYMBCYVAKHB3R\"}", "platform": "ACC", "raise_time": "2024-07-05 02:14:20.063486", "summary": "JDU1617D001 type Timer SUCCESS", "severity": 1, "clear_time": "2024-07-05 02:14:20.063486", "event_type": "clear", "wake_up_time": "2024-07-05 02:14:20.063486"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T02:01:42.221714189Z", "message": "#<SNMP::SNMPv1_Trap:0x504ee96c @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x71ab34df @value=1277527595>, @varbind_list=[#<SNMP::VarBind:0x5f4ec601 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x78ec410d @name=[*******.4.1.88898.*******.300.1.2], @value=\"JTU129D001\">, #<SNMP::VarBind:0x50499f03 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Remote File Monitor\">, #<SNMP::VarBind:0x4c6bcc0e @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720142900012802430JQGJEDMZPM3IE\">, #<SNMP::VarBind:0x653c654c @name=[*******.4.1.88898.*******.300.1.5], @value=\"FINISHED\">, #<SNMP::VarBind:0x7f42b4cf @name=[*******.4.1.88898.*******.300.1.6], @value=\"IICTCIAPWV405\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x63ef8da3 @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "7d448701-16d4-42d6-b9f2-abe6ac39f17e", "event.kafka.offset": 654382, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T02:01:42.325Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.opsTaskAgent": "IICTCIAPWV405", "snmptrap.opsTaskName": "JTU129D001", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Remote File Monitor", "snmptrap.opsTaskStatusCode": "FINISHED", "snmptrap.opsTaskExecId": "1720142900012802430JQGJEDMZPM3IE", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Linux/Unix", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "A2005_C0101_CheckDiskspaceUAC-opt", "additional_data": "{\"exec_id\": \"17201473550755638911LB2N9IEXKETX\"}", "platform": "ACC-RUN", "raise_time": "2024-07-05 02:57:00.588712", "summary": "A2005_C0101_CheckDiskspaceUAC-opt type Linux/Unix\nSUCCESS on iictziaplv284_A2005", "severity": 1, "clear_time": "2024-07-05 02:57:00.588712", "event_type": "clear", "wake_up_time": "2024-07-05 02:57:00.588712"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T02:01:42.332900729Z", "message": "#<SNMP::SNMPv1_Trap:0x2d8839d8 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x18b1e8df @value=1277527624>, @varbind_list=[#<SNMP::VarBind:0x39f7afe @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x6f35c5ac @name=[*******.4.1.88898.*******.300.1.2], @value=\"TTU129D00\">, #<SNMP::VarBind:0x78553f85 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Workflow\">, #<SNMP::VarBind:0x5eb3458b @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720142900012793430PK0LNZKJ4PXBF\">, #<SNMP::VarBind:0x26039c29 @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x675a25e0 @name=[*******.4.1.88898.*******.300.1.6], @value=\"n/a\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x36229799 @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "7c9dc8a5-9e54-46c3-a82c-66411b73b5f6", "event.kafka.offset": 654383, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T02:01:42.434Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.opsTaskAgent": "n/a", "snmptrap.opsTaskName": "TTU129D00", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Workflow", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskExecId": "1720142900012793430PK0LNZKJ4PXBF", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Windows", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JTU734D010", "additional_data": "{\"exec_id\": \"1720148700022510430BBRTOG7HGZF24\"}", "platform": "ACC", "raise_time": "2024-07-05 03:06:58.285153", "summary": "JTU734D010 type Windows\nFINISHED on IICTCIAPWV404", "severity": 1, "clear_time": "2024-07-05 03:06:58.285153", "event_type": "clear", "wake_up_time": "2024-07-05 03:06:58.285153"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T02:02:20.073760964Z", "message": "#<SNMP::SNMPv1_Trap:0x3d203784 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x7baa5548 @value=1277565448>, @varbind_list=[#<SNMP::VarBind:0x3e041b9 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x3fc0da56 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JDU1617D001\">, #<SNMP::VarBind:0x16e9c8f3 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Timer\">, #<SNMP::VarBind:0x6c25b0e2 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720144836020851430LS0QK3GA49JSV\">, #<SNMP::VarBind:0x6d49d956 @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x5462bcc3 @name=[*******.4.1.88898.*******.300.1.6], @value=\"n/a\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0xc9458ec @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "69a7c520-e9cf-4b08-a85c-36743b1ca8a2", "event.kafka.offset": 654386, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T02:02:20.178Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.opsTaskAgent": "n/a", "snmptrap.opsTaskName": "JDU1617D001", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Timer", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskExecId": "1720144836020851430LS0QK3GA49JSV", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Universal", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JAM500D994", "additional_data": "{\"exec_id\": \"1720149026045608430P9QGMYT5L5U4F\"}", "platform": "ACC", "raise_time": "2024-07-05 03:12:32.141720", "summary": "JAM500D994 type Universal\nFAILED on hrrlyiaplv006-a1445", "severity": 2, "clear_time": null, "event_type": "problem", "wake_up_time": "2024-07-05 03:12:32.141720"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T02:57:00.588712998Z", "message": "#<SNMP::SNMPv1_Trap:0x55050e0d @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x32b710da @value=1272807917>, @varbind_list=[#<SNMP::VarBind:0x80eab6b @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x7506b901 @name=[*******.4.1.88898.*******.300.1.2], @value=\"A2005_C0101_CheckDiskspaceUAC-opt\">, #<SNMP::VarBind:0x7fbd46bb @name=[*******.4.1.88898.*******.300.1.3], @value=\"Linux/Unix\">, #<SNMP::VarBind:0x220096a7 @name=[*******.4.1.88898.*******.300.1.4], @value=\"17201473550755638911LB2N9IEXKETX\">, #<SNMP::VarBind:0x5f28247f @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x1fa6402a @name=[*******.4.1.88898.*******.300.1.6], @value=\"iictziaplv284_A2005\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0xa3b45fb @value=\"\\n\\xFEJ&\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "6de5eebd-126b-4e04-af5c-c2b8e3320c85", "event.kafka.offset": 654446, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T02:57:00.689Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.opsTaskAgent": "iictziaplv284_A2005", "snmptrap.opsTaskName": "A2005_C0101_CheckDiskspaceUAC-opt", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Linux/Unix", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskExecId": "17201473550755638911LB2N9IEXKETX", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Universal", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JAM589D731", "additional_data": "{\"exec_id\": \"1720154455044446430UKPVS5DPNYRBU\"}", "platform": "ACC", "raise_time": "2024-07-05 04:43:52.696661", "summary": "JAM589D731 type Universal\nRUNNING on hrrlyiaplv006-a1445", "severity": 3, "clear_time": null, "event_type": "problem", "wake_up_time": "2024-07-05 04:48:52.696661"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T03:12:32.141720682Z", "message": "#<SNMP::SNMPv1_Trap:0x5ecbca13 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x15017f18 @value=1281777514>, @varbind_list=[#<SNMP::VarBind:0x360c0884 @name=[*******.4.1.88898.*******.300.1.1], @value=\"2\">, #<SNMP::VarBind:0x3697c704 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JAM500D994\">, #<SNMP::VarBind:0x28f08771 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Universal\">, #<SNMP::VarBind:0x327495eb @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720149026045608430P9QGMYT5L5U4F\">, #<SNMP::VarBind:0x646ae309 @name=[*******.4.1.88898.*******.300.1.5], @value=\"FAILED\">, #<SNMP::VarBind:0x6c4db767 @name=[*******.4.1.88898.*******.300.1.6], @value=\"hrrlyiaplv006-a1445\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x40aa7e90 @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "45ff3849-5c98-45b9-95c3-c4939f8f769b", "event.kafka.offset": 649974, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T03:12:32.242Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.opsTaskAgent": "hrrlyiaplv006-a1445", "snmptrap.opsTaskName": "JAM500D994", "snmptrap.opsTaskSeverity": "2", "snmptrap.opsTaskType": "Universal", "snmptrap.opsTaskStatusCode": "FAILED", "snmptrap.opsTaskExecId": "1720149026045608430P9QGMYT5L5U4F", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Web Service", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "A2005_D0304_CheckClusterUAC", "additional_data": "{\"exec_id\": \"1720154255012368891KHKO9QFPJZWVH\"}", "platform": "ACC-RUN", "raise_time": "2024-07-05 04:47:00.898617", "summary": "A2005_D0304_CheckClusterUAC type Web Service SUCCESS", "severity": 1, "clear_time": "2024-07-05 04:47:00.898617", "event_type": "clear", "wake_up_time": "2024-07-05 04:47:00.898617"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T04:47:00.898617600Z", "message": "#<SNMP::SNMPv1_Trap:0x46a9073f @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x304167ec @value=1279408150>, @varbind_list=[#<SNMP::VarBind:0x3d0b48eb @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x3395208a @name=[*******.4.1.88898.*******.300.1.2], @value=\"A2005_D0304_CheckClusterUAC\">, #<SNMP::VarBind:0x8d05f13 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Web Service\">, #<SNMP::VarBind:0xac5d161 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720154255012368891KHKO9QFPJZWVH\">, #<SNMP::VarBind:0x6dee9cc2 @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x77e56085 @name=[*******.4.1.88898.*******.300.1.6], @value=\"n/a\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x7d4781ad @value=\"\\n\\xFEJ&\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "7b011550-b6d5-4fb6-838b-8ffbc051342b", "event.kafka.offset": 650095, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T04:47:00.999Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.opsTaskAgent": "n/a", "snmptrap.opsTaskName": "A2005_D0304_CheckClusterUAC", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Web Service", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskExecId": "1720154255012368891KHKO9QFPJZWVH", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "File Transfer", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JAU716D005", "additional_data": "{\"exec_id\": \"1720155436043451430WMLT49JLFZ9BZ\"}", "platform": "ACC", "raise_time": "2024-07-05 06:31:09.030057", "summary": "JAU716D005 type File Transfer\nSUCCESS on IICTCIAPWV405", "severity": 1, "clear_time": "2024-07-05 06:31:09.030057", "event_type": "clear", "wake_up_time": "2024-07-05 06:31:09.030057"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T06:31:09.030057913Z", "message": "#<SNMP::SNMPv1_Trap:0x142d3ae1 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x9834fcd @value=1293694404>, @varbind_list=[#<SNMP::VarBind:0x26ffd782 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x5bf81b8e @name=[*******.4.1.88898.*******.300.1.2], @value=\"JAU716D005\">, #<SNMP::VarBind:0x4896da1b @name=[*******.4.1.88898.*******.300.1.3], @value=\"File Transfer\">, #<SNMP::VarBind:0x4bc797c1 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720155436043451430WMLT49JLFZ9BZ\">, #<SNMP::VarBind:0x135c1afc @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x8634772 @name=[*******.4.1.88898.*******.300.1.6], @value=\"IICTCIAPWV405\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x76db1267 @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "83b15ece-c35d-426c-af72-b5ffd132d3f8", "event.kafka.offset": 652112, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T06:31:09.130Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.opsTaskAgent": "IICTCIAPWV405", "snmptrap.opsTaskName": "JAU716D005", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "File Transfer", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskExecId": "1720155436043451430WMLT49JLFZ9BZ", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Windows", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JAW2160D001", "additional_data": "{\"exec_id\": \"1720161126066235430KGU8UD80OB46C\"}", "platform": "ACC", "raise_time": "2024-07-05 06:33:36.179567", "summary": "JAW2160D001 type Windows\nSTARTED on IICTBIWFWS007", "severity": 1, "clear_time": "2024-07-05 06:33:36.179567", "event_type": "clear", "wake_up_time": "2024-07-05 06:33:36.179567"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T11:01:03.558921638Z", "message": "#<SNMP::SNMPv1_Trap:0x72e47195 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x273a9c36 @value=1309888931>, @varbind_list=[#<SNMP::VarBind:0xcf4ca30 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x5dcd6ae2 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JAU1105D007\">, #<SNMP::VarBind:0x2e658641 @name=[*******.4.1.88898.*******.300.1.3], @value=\"SQL\">, #<SNMP::VarBind:0x535a4dd7 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720169856122797430U7CUZ06QJEBKE\">, #<SNMP::VarBind:0x79d5b8c6 @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x4ff94764 @name=[*******.4.1.88898.*******.300.1.6], @value=\"n/a\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x4d2c9754 @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "ad4580d7-1255-441b-94e9-fb6fb9f5bad3", "event.kafka.offset": 655158, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T11:01:03.659Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.opsTaskAgent": "n/a", "snmptrap.opsTaskName": "JAU1105D007", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "SQL", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskExecId": "1720169856122797430U7CUZ06QJEBKE", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "SQL", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JAU1105D007", "additional_data": "{\"exec_id\": \"1720169856122797430U7CUZ06QJEBKE\"}", "platform": "ACC", "raise_time": "2024-07-05 11:01:03.558921", "summary": "JAU1105D007 type SQL SUCCESS", "severity": 1, "clear_time": "2024-07-05 11:01:03.558921", "event_type": "clear", "wake_up_time": "2024-07-05 11:01:03.558921"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T16:25:05.683590168Z", "message": "#<SNMP::SNMPv1_Trap:0x73ee0150 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x560708b9 @value=1329331058>, @varbind_list=[#<SNMP::VarBind:0x71e3dffb @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x7204b416 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JNU369D001\">, #<SNMP::VarBind:0x1ca8382f @name=[*******.4.1.88898.*******.300.1.3], @value=\"Stored Procedure\">, #<SNMP::VarBind:0x11243dc3 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720151940483803430OMQZLWAH392SC\">, #<SNMP::VarBind:0x7e57341b @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x29a48bc3 @name=[*******.4.1.88898.*******.300.1.6], @value=\"n/a\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x7410b798 @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "280af379-be8b-4f75-b27b-0a16c3b6d9a2", "event.kafka.offset": 652901, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T16:25:05.786Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.opsTaskAgent": "n/a", "snmptrap.opsTaskName": "JNU369D001", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Stored Procedure", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskExecId": "1720151940483803430OMQZLWAH392SC", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Stored Procedure", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "JNU369D001", "additional_data": "{\"exec_id\": \"1720151940483803430OMQZLWAH392SC\"}", "platform": "ACC", "raise_time": "2024-07-05 16:25:05.683590", "summary": "JNU369D001 type Stored Procedure SUCCESS", "severity": 1, "clear_time": "2024-07-05 16:25:05.683590", "event_type": "clear", "wake_up_time": "2024-07-05 16:25:05.683590"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-05T06:33:36.179567690Z", "message": "#<SNMP::SNMPv1_Trap:0x5602f042 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x6ec4f5e2 @value=1293841553>, @varbind_list=[#<SNMP::VarBind:0x34a51f0a @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x7564693c @name=[*******.4.1.88898.*******.300.1.2], @value=\"JAW2160D001\">, #<SNMP::VarBind:0x3cc6f997 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Windows\">, #<SNMP::VarBind:0x7cbc25d0 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1720161126066235430KGU8UD80OB46C\">, #<SNMP::VarBind:0x607d3802 @name=[*******.4.1.88898.*******.300.1.5], @value=\"STARTED\">, #<SNMP::VarBind:0x368680b8 @name=[*******.4.1.88898.*******.300.1.6], @value=\"IICTBIWFWS007\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x3d87cdbe @value=\"\\n\\xFEJ5\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "2fcf9b93-47f6-4bbd-a94b-8ecbbd5311dd", "event.kafka.offset": 652116, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-05T06:33:36.279Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.opsTaskAgent": "IICTBIWFWS007", "snmptrap.opsTaskName": "JAW2160D001", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskType": "Windows", "snmptrap.opsTaskStatusCode": "STARTED", "snmptrap.opsTaskExecId": "1720161126066235430KGU8UD80OB46C", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorName": null, "snmptrap.opsConnectorType": null}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": null, "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": true, "ci_id": "IICTSIAPWV051", "additional_data": "{\"exec_id\": \"Not use\"}", "platform": "ACC-RUN", "raise_time": "2024-07-10 06:54:36.830257", "summary": "IICTSIAPWV051:7878 type OMS Server Connected", "severity": 1, "clear_time": null, "event_type": "problem", "wake_up_time": "2024-07-10 06:54:36.830257"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-07-10T06:54:36.830257072Z", "message": "#<SNMP::SNMPv1_Trap:0x10a338e6 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x20047bff @value=1719064245>, @varbind_list=[#<SNMP::VarBind:0x3f3c89bd @name=[*******.4.1.88898.*******.300.2.1], @value=\"1\">, #<SNMP::VarBind:0x36d1f2b4 @name=[*******.4.1.88898.*******.300.2.2], @value=\"IICTSIAPWV051:7878\">, #<SNMP::VarBind:0x3589ac12 @name=[*******.4.1.88898.*******.300.2.3], @value=\"OMS Server\">, #<SNMP::VarBind:0xb183500 @name=[*******.4.1.88898.*******.300.2.4], @value=\"Connected\">], @specific_trap=2, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x503234bf @value=\"\\n\\xFEJ&\">, @generic_trap=6>", "tags": ["opsConnectorObjects"], "event.uuid": "4d3f3a3a-9fc3-4efa-84e2-7c63b1316f6e", "event.kafka.offset": 661849, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-acc", "event.kafka.topic": "a2005-stonebranch_monitoring-events-acc", "event.kafka.timestamp": "2024-07-10T06:54:36.931Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.opsTaskAgent": null, "snmptrap.opsTaskName": null, "snmptrap.opsTaskSeverity": null, "snmptrap.opsTaskType": null, "snmptrap.opsTaskStatusCode": null, "snmptrap.opsTaskExecId": null, "snmptrap.opsConnectorSeverity": "1", "snmptrap.opsConnectorMode": "Connected", "snmptrap.opsConnectorName": "IICTSIAPWV051:7878", "snmptrap.opsConnectorType": "OMS Server"}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": null, "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": true, "ci_id": "iictziaplv284", "additional_data": "{\"exec_id\": \"Not use\"}", "platform": "ACC-RUN", "raise_time": "2024-07-10 06:54:37.593753", "summary": "iictziaplv284:7878,iictyiaplv286:7878 type OMS Server Connected", "severity": 1, "clear_time": null, "event_type": "problem", "wake_up_time": "2024-07-10 06:54:37.593753"}}, {"input": {"host": "**************", "@version": "1", "type": "snmp_trap", "@timestamp": "2024-08-20T20:02:13.668Z", "message": "#<SNMP::SNMPv1_Trap:0x79b1338e @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x25f76ee6 @value=398912678>, @varbind_list=[#<SNMP::VarBind:0x11143a68 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x61494687 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JPU129D001\">, #<SNMP::VarBind:0x7fd78416 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Remote File Monitor\">, #<SNMP::VarBind:0x64a23ffd @name=[*******.4.1.88898.*******.300.1.4], @value=\"172418227806021335401AFXX8OBWTSE\">, #<SNMP::VarBind:0x54559e7b @name=[*******.4.1.88898.*******.300.1.5], @value=\"FINISHED\">, #<SNMP::VarBind:0x7f838708 @name=[*******.4.1.88898.*******.300.1.6], @value=\"IICTCIAPWV404\">], @specific_trap=1, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x3c374bda @value=\"\\n\\xFEJ/\">, @generic_trap=6>", "tags": ["opsTaskObjects"], "event.uuid": "1a2f86dd-26e7-4fb5-8a1f-9545cb904f21", "event.kafka.offset": 2085681, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "event.kafka.topic": "a2005-stonebranch_monitoring-events-prd", "event.kafka.timestamp": "2024-08-20T20:02:13.768Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.opsTaskExecId": "172418227806021335401AFXX8OBWTSE", "snmptrap.opsTaskType": "Remote File Monitor", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskName": "JPU129D001", "snmptrap.opsTaskStatusCode": "FINISHED", "snmptrap.opsTaskAgent": "IICTCIAPWV404"}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/Scheduling/", "metric_name": "Remote File Monitor", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": true, "ci_id": "JPU129D001", "additional_data": "{\"exec_id\": \"172418227806021335401AFXX8OBWTSE\"}", "platform": "PRD", "raise_time": "2024-08-20 20:02:13.668000", "summary": "JPU129D001 type Remote File Monitor\nFINISHED on IICTCIAPWV404", "severity": 1, "clear_time": "2024-08-20 20:02:13.668000", "event_type": "clear", "wake_up_time": "2024-08-20 20:02:13.668000"}}, {"input": {"message": "{\"agent_addr\":\"************\",\"generic_trap\":6,\"specific_trap\":1,\"enterprise\":\"*******.4.1.88898.*******.301\",\"variable_bindings\":{\"*******.4.1.88898.*******.300.1.5\":\"SUCCESS\",\"*******.4.1.88898.*******.300.1.6\":\"idirziapls005-a944\",\"*******.4.1.88898.*******.300.1.3\":\"Linux/Unix\",\"*******.4.1.88898.*******.300.1.4\":\"1743094800532503052J0U5B5CXONVOW\",\"*******.4.1.88898.*******.300.1.1\":\"1\",\"*******.4.1.88898.*******.300.1.2\":\"JPL944D05A\"},\"type\":\"V1TRAP\",\"community\":\"public\",\"version\":\"1\",\"timestamp\":21450876}", "snmptrap": {"opsTaskStatusCode": "SUCCESS", "opsTaskAgent": "idirziapls005-a944", "opsTaskSeverity": "1", "opsTaskName": "JPL944D05A", "opsTaskExecId": "1743094800532503052J0U5B5CXONVOW", "opsTaskType": "Linux/Unix"}, "tags": ["opsTaskObjects"], "type": "snmp_trap", "event": {"uuid": "5aa5c0ed-1936-4d4d-af9c-8258084181dd", "logstash": {"instance_name": "iictniapls016"}, "kafka": {"consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "offset": 13490304, "topic": "a2005-stonebranch_monitoring-events-prd", "timestamp": "2025-03-27T17:00:56.185Z", "partition": 2, "key": null}}, "@timestamp": "2025-03-27T17:00:56.084944454Z", "host": "***********", "@version": "1"}, "output": {"node": "************"}}, {"input": {"snmptrap": {"opsTaskAgent": "n/a", "opsTaskStatusCode": "SUCCESS", "opsTaskExecId": "1743141303284596544O0506N4AJTDVC", "opsTaskType": "Timer", "opsTaskSeverity": "1", "opsTaskName": "A2005_C0000_HeartbeatUAC"}, "tags": ["opsTaskObjects"], "host": "***********", "type": "snmp_trap", "event": {"uuid": "d47f89dd-2419-4706-9eae-28894f040f39", "kafka": {"timestamp": "2025-03-28T09:05:21.311Z", "offset": 13563938, "partition": 0, "topic": "a2005-stonebranch_monitoring-events-prd", "key": null, "consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd"}, "logstash": {"instance_name": "iictmiapls016"}}, "@timestamp": "2025-03-28T09:05:21.211085908Z", "message": "{\"agent_addr\":\"************\",\"generic_trap\":6,\"specific_trap\":1,\"enterprise\":\"*******.4.1.88898.*******.301\",\"variable_bindings\":{\"*******.4.1.88898.*******.300.1.5\":\"SUCCESS\",\"*******.4.1.88898.*******.300.1.6\":\"n/a\",\"*******.4.1.88898.*******.300.1.3\":\"Timer\",\"*******.4.1.88898.*******.300.1.4\":\"1743141303284596544O0506N4AJTDVC\",\"*******.4.1.88898.*******.300.1.1\":\"1\",\"*******.4.1.88898.*******.300.1.2\":\"A2005_C0000_HeartbeatUAC\"},\"type\":\"V1TRAP\",\"community\":\"public\",\"version\":\"1\",\"timestamp\":79289026}", "@version": "1"}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/ApplicationEvent/", "metric_name": "Heartbeat", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "A2005_C0000_HeartbeatUAC_PRD-BUILD", "additional_data": "{\"exec_id\": \"1743141303284596544O0506N4AJTDVC\"}", "platform": "PRD-BUILD", "raise_time": "2025-03-28 09:05:21.211085", "summary": "A2005_C0000_HeartbeatUAC type Timer SUCCESS", "severity": 1, "clear_time": null, "event_type": "heartbeat", "wake_up_time": "2025-03-28 09:05:21.211085"}}, {"input": {"snmptrap": {"opsTaskAgent": "n/a", "opsTaskStatusCode": "SUCCESS", "opsTaskExecId": "1743152726886571524IMYB6UMK2WDTB", "opsTaskType": "Timer", "opsTaskSeverity": "1", "opsTaskName": "A2005_C0000_HeartbeatUAC"}, "tags": ["opsTaskObjects"], "host": "***********", "type": "snmp_trap", "event": {"uuid": "f9139af4-26f0-4a1a-9ab7-d0b52f6638c4", "kafka": {"timestamp": "2025-03-28T09:05:41.421Z", "offset": 13563977, "partition": 0, "topic": "a2005-stonebranch_monitoring-events-prd", "key": null, "consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd"}, "logstash": {"instance_name": "iictmiapls016"}}, "@timestamp": "2025-03-28T09:05:41.316826968Z", "message": "{\"agent_addr\":\"************\",\"generic_trap\":6,\"specific_trap\":1,\"enterprise\":\"*******.4.1.88898.*******.301\",\"variable_bindings\":{\"*******.4.1.88898.*******.300.1.5\":\"SUCCESS\",\"*******.4.1.88898.*******.300.1.6\":\"n/a\",\"*******.4.1.88898.*******.300.1.3\":\"Timer\",\"*******.4.1.88898.*******.300.1.4\":\"1743152726886571524IMYB6UMK2WDTB\",\"*******.4.1.88898.*******.300.1.1\":\"1\",\"*******.4.1.88898.*******.300.1.2\":\"A2005_C0000_HeartbeatUAC\"},\"type\":\"V1TRAP\",\"community\":\"public\",\"version\":\"1\",\"timestamp\":79323594}", "@version": "1"}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/ApplicationEvent/", "metric_name": "Heartbeat", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "A2005_C0000_HeartbeatUAC_PRD-RUN", "additional_data": "{\"exec_id\": \"1743152726886571524IMYB6UMK2WDTB\"}", "platform": "PRD-RUN", "raise_time": "2025-03-28 09:05:41.316826", "summary": "A2005_C0000_HeartbeatUAC type Timer SUCCESS", "severity": 1, "clear_time": null, "event_type": "heartbeat", "wake_up_time": "2025-03-28 09:05:41.316826"}}, {"input": {"@timestamp": "2025-03-28T09:09:35.321576442Z", "type": "snmp_trap", "event": {"logstash": {"instance_name": "iictniapls015"}, "uuid": "ee970804-5e26-445c-af3f-18ea2515a338", "kafka": {"topic": "a2005-stonebranch_monitoring-events-prd", "consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "timestamp": "2025-03-28T09:09:35.423Z", "offset": 13537358, "key": null, "partition": 1}}, "host": "***********", "tags": ["opsTaskObjects"], "@version": "1", "message": "{\"agent_addr\":\"************\",\"generic_trap\":6,\"specific_trap\":1,\"enterprise\":\"*******.4.1.88898.*******.301\",\"variable_bindings\":{\"*******.4.1.88898.*******.300.1.5\":\"SUCCESS\",\"*******.4.1.88898.*******.300.1.6\":\"n/a\",\"*******.4.1.88898.*******.300.1.3\":\"Timer\",\"*******.4.1.88898.*******.300.1.4\":\"1743152880166589052DNBW42X9T1J4Z\",\"*******.4.1.88898.*******.300.1.1\":\"1\",\"*******.4.1.88898.*******.300.1.2\":\"DPL2005D000\"},\"type\":\"V1TRAP\",\"community\":\"public\",\"version\":\"1\",\"timestamp\":79573963}", "snmptrap": {"opsTaskAgent": "n/a", "opsTaskSeverity": "1", "opsTaskStatusCode": "SUCCESS", "opsTaskType": "Timer", "opsTaskExecId": "1743152880166589052DNBW42X9T1J4Z", "opsTaskName": "DPL2005D000"}}, "output": {"agent_id": 0, "manager": "mon-stonebranch", "action_class": "IT", "clear_type": "automatic", "metric_type": "/ApplicationEvent/", "metric_name": "Heartbeat", "handle_time": "2024-01-01 12:00:01", "node": "************", "node_alias": "************", "actionable": false, "ci_id": "DPL2005D000", "additional_data": "{\"exec_id\": \"1743152880166589052DNBW42X9T1J4Z\"}", "platform": "PRD", "raise_time": "2025-03-28 09:09:35.321576", "summary": "StoneBranch HeartBeat Alarm", "severity": 1, "clear_time": null, "event_type": "heartbeat", "wake_up_time": "2025-03-28 09:09:35.321576"}}]}