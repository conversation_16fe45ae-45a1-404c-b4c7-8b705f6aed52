{"data": [{"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::InformRequest:0x23295c7c @request_id=645282854, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0x24fb8e64 @name=[*******.*******.0], @value=#<SNMP::Integer:0x5c8f4d81 @value=0>>, #<SNMP::VarBind:0x35ac30d0 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x45bece9d @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x3dcef0e0 @name=[*******.4.1.20306.*******.2], @value=\"683138\">, #<SNMP::VarBind:0x1b0b519d @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/WINGcon/WSP\">, #<SNMP::VarBind:0x4ff5c89c @name=[*******.4.1.20306.*******.4], @value=\"de1smsc1b\">, #<SNMP::VarBind:0x7c0c3dd9 @name=[*******.4.1.20306.*******.5], @value=\"************\">, #<SNMP::VarBind:0x360f9b48 @name=[*******.4.1.20306.*******.6], @value=\"SPM-Snmp\">, #<SNMP::VarBind:0x77c9b3ad @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x161934e0 @name=[*******.4.1.20306.*******.8], @value=\"1\">, #<SNMP::VarBind:0xb66310f @name=[*******.4.1.20306.*******.9], @value=\"WARNING\">, #<SNMP::VarBind:0x41793ef0 @name=[*******.4.1.20306.*******.10], @value=\"2023-09-22 01:38:33.026\">, #<SNMP::VarBind:0x7b97a9a9 @name=[*******.4.1.20306.*******.11], @value=\"2023-09-22 01:38:33.026\">, #<SNMP::VarBind:0x24762c0f @name=[*******.4.1.20306.*******.12], @value=\"WSP MON_HW_ALERT Hardware issue detected: \\\"Power supply redundancy lost on de1smsc1b\\\" (/Host-de1smsc1b/mon:5:hw:power) other/other\">, #<SNMP::VarBind:0x1ea28fc7 @name=[*******.4.1.20306.*******.13], @value=\"<p style=\\\"margin-bottom: 5px;\\\">Event Time: 21.09.2023 23:38:22.231</p><p style=\\\"font-weight: bold; margin-bottom: 5px;\\\">Wingcon Alarm</p>Key: MON_HW_ALERT<br/>        Unique ID: f38bf628-58d7-11ee-9e36-98f2b310c9f4<br/>        Priority: warn (2)<br/>        Description: Hardware issue detected: \\\"Power supply redundancy lost on de1smsc1b\\\"<br/>        Time: 21.09.2023 23:38:22.231<br/>        Host: de1smsc1b<br/>        Source: mon:5:hw:power\">, #<SNMP::VarBind:0x1624bc5d @name=[*******.4.1.20306.*******.14], @value=\"This event indicates that a fault condition has occurred.\">]>", "@timestamp": "2023-09-21T23:38:34.239Z", "@version": "1", "snmptrap.description": "<p style=\"margin-bottom: 5px;\">Event Time: 21.09.2023 23:38:22.231</p><p style=\"font-weight: bold; margin-bottom: 5px;\">Wingcon Alarm</p>Key: MON_HW_ALERT<br/>        Unique ID: f38bf628-58d7-11ee-9e36-98f2b310c9f4<br/>        Priority: warn (2)<br/>        Description: Hardware issue detected: \"Power supply redundancy lost on de1smsc1b\"<br/>        Time: 21.09.2023 23:38:22.231<br/>        Host: de1smsc1b<br/>        Source: mon:5:hw:power", "snmptrap.eventuei": "uei/vnd/WINGcon/WSP", "snmptrap.servicename": "SPM-Snmp", "snmptrap.severityname": "WARNING", "snmptrap.hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "snmptrap.counter": "1", "snmptrap.operinstruct": "This event indicates that a fault condition has occurred.", "snmptrap.lasteventtime": "2023-09-22 01:38:33.026", "snmptrap.ipaddr": "************", "snmptrap.alarmtype": "1", "snmptrap.logmsg": "WSP MON_HW_ALERT Hardware issue detected: \"Power supply redundancy lost on de1smsc1b\" (/Host-de1smsc1b/mon:5:hw:power) other/other", "snmptrap.firsteventtime": "2023-09-22 01:38:33.026", "snmptrap.nodelabel": "de1smsc1b", "snmptrap.alarmid": "683138", "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-21T23:38:34.339Z", "event.kafka.offset": 8229, "event.kafka.key": null, "event.logstash.instance_name": "iictniaplv031", "event.uuid": "4b97e5e3-56a6-4c08-a351-cb40749e9e16"}, "output": {"summary": "WSP MON_HW_ALERT Hardware issue detected: \"Power supply redundancy lost on de1smsc1b\" (/Host-de1smsc1b/mon:5:hw:power) other/other", "severity": 2, "metric_name": "", "metric_type": "", "ci_id": "DE1SMSC1B", "manager": "mon-cnms", "node": "DE1SMSC1B", "event_type": "problem", "raise_time": "2024-01-01 12:00:01", "clear_time": null, "clear_type": "automatic", "action_class": "TE", "top_level": "A1926", "event_id": "uei/vnd/WINGcon/WSP", "node_alias": "************", "actionable": false, "agent_id": 0, "additional_data": "{\"alarm_id\": \"MON_HW_ALERT\"}"}}, {"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::InformRequest:0x20c517b1 @request_id=645283259, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0x535af72e @name=[*******.*******.0], @value=#<SNMP::Integer:0x23dcff68 @value=0>>, #<SNMP::VarBind:0x2bac0c3d @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x629b0330 @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x66fc6bca @name=[*******.4.1.20306.*******.2], @value=\"683153\">, #<SNMP::VarBind:0x229fefc2 @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x6999d248 @name=[*******.4.1.20306.*******.4], @value=\"DE1BEIFBH01\">, #<SNMP::VarBind:0x53a1dec8 @name=[*******.4.1.20306.*******.5], @value=\"*************\">, #<SNMP::VarBind:0x56065d4d @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0xbba59b0 @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x2c16d11d @name=[*******.4.1.20306.*******.8], @value=\"1\">, #<SNMP::VarBind:0x719c1dc9 @name=[*******.4.1.20306.*******.9], @value=\"MINOR\">, #<SNMP::VarBind:0x1594da82 @name=[*******.4.1.20306.*******.10], @value=\"2023-09-22 02:00:05.023\">, #<SNMP::VarBind:0x4cba7659 @name=[*******.4.1.20306.*******.11], @value=\"2023-09-22 02:00:05.023\">, #<SNMP::VarBind:0x56bed8ea @name=[*******.4.1.20306.*******.12], @value=\"HLRS HLR1307 HLR Appl OOS (EM:DE1BEIFBH01 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) processingErrorAlarm/other\">, #<SNMP::VarBind:0x47dde5a1 @name=[*******.4.1.20306.*******.13], @value=\"DE1BEIFBH01 HLR1307 2023-09-22T02:00:01+02:00 5583950 alarm-notification minor<br/>GU_Home_Location_Registry 0:0:8:0 hlr 2003-1-13-/=/v=1/sg-name=HLR_SG1--8-0<br/><br/>HLR Appl OOS. HLR Applicaton Out of Service<br/>Application state change: Running -> Restarting\">, #<SNMP::VarBind:0x742e7712 @name=[*******.4.1.20306.*******.14], @value=\"The alarm clears once the call processing application is in service.\">]>", "@timestamp": "2023-09-22T00:00:09.318Z", "@version": "1", "snmptrap.description": "DE1BEIFBH01 HLR1307 2023-09-22T02:00:01+02:00 5583950 alarm-notification minor<br/>GU_Home_Location_Registry 0:0:8:0 hlr 2003-1-13-/=/v=1/sg-name=HLR_SG1--8-0<br/><br/>HLR Appl OOS. HLR Applicaton Out of Service<br/>Application state change: Running -> Restarting", "snmptrap.eventuei": "uei/vnd/Kapsch/KSC", "snmptrap.servicename": "EMA-Ksc", "snmptrap.severityname": "MINOR", "snmptrap.hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "snmptrap.counter": "1", "snmptrap.operinstruct": "The alarm clears once the call processing application is in service.", "snmptrap.lasteventtime": "2023-09-22 02:00:05.023", "snmptrap.ipaddr": "*************", "snmptrap.alarmtype": "1", "snmptrap.logmsg": "HLRS HLR1307 HLR Appl OOS (EM:DE1BEIFBH01 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) processingErrorAlarm/other", "snmptrap.firsteventtime": "2023-09-22 02:00:05.023", "snmptrap.nodelabel": "DE1BEIFBH01", "snmptrap.alarmid": "683153", "event.kafka.partition": 1, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-22T00:00:09.433Z", "event.kafka.offset": 8219, "event.kafka.key": null, "event.logstash.instance_name": "iictnibflv003", "event.uuid": "ba9645ee-33ab-44b3-894e-e8d37a8aae31"}, "output": {"summary": "HLRS HLR1307 HLR Appl OOS (EM:DE1BEIFBH01 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) processingErrorAlarm/other", "severity": 3, "metric_name": "", "metric_type": "", "ci_id": "DE1BEIFBH01", "manager": "mon-cnms", "node": "DE1BEIFBH01", "event_type": "problem", "raise_time": "2024-01-01 12:00:01", "clear_time": null, "clear_type": "automatic", "action_class": "TE", "top_level": "A1926", "event_id": "uei/vnd/Kapsch/KSC", "node_alias": "*************", "actionable": false, "agent_id": 0, "additional_data": "{\"alarm_id\": \"HLR1307\"}"}}, {"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::InformRequest:0x43c95dd8 @request_id=645283253, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0x5dcb8caf @name=[*******.*******.0], @value=#<SNMP::Integer:0x7b93ba6c @value=0>>, #<SNMP::VarBind:0x723d0f5b @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x7f98170b @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x7eb752a3 @name=[*******.4.1.20306.*******.2], @value=\"683151\">, #<SNMP::VarBind:0x1811c7c3 @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x24c444d @name=[*******.4.1.20306.*******.4], @value=\"DE1BEIFBH01\">, #<SNMP::VarBind:0x3d533b40 @name=[*******.4.1.20306.*******.5], @value=\"*************\">, #<SNMP::VarBind:0x79b54403 @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0x6f8cbb29 @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0xf02f4b9 @name=[*******.4.1.20306.*******.8], @value=\"1\">, #<SNMP::VarBind:0x2baf5722 @name=[*******.4.1.20306.*******.9], @value=\"MAJOR\">, #<SNMP::VarBind:0x78e98b08 @name=[*******.4.1.20306.*******.10], @value=\"2023-09-22 02:00:05.02\">, #<SNMP::VarBind:0x72b622cf @name=[*******.4.1.20306.*******.11], @value=\"2023-09-22 02:00:05.02\">, #<SNMP::VarBind:0x6a89b71c @name=[*******.4.1.20306.*******.12], @value=\"HLRS HLR1302 HLR RExTst (EM:DE1BEIFBH01 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) other/other\">, #<SNMP::VarBind:0x3b4efb28 @name=[*******.4.1.20306.*******.13], @value=\"DE1BEIFBH01 HLR1302 2023-09-22T02:00:00+02:00 5583948 alarm-notification major<br/>GU_Home_Location_Registry 0:0:12:0 hlr 2003-1-13-/=/v=1/sg-name=HLR_SG1--3-1<br/><br/>HLR RExTst. Routine Exercise Test (RExTst) in progress<br/>RExTst in progress\">, #<SNMP::VarBind:0xfcb8682 @name=[*******.4.1.20306.*******.14], @value=\"No repair action required.\">]>", "@timestamp": "2023-09-22T00:00:09.310Z", "@version": "1", "snmptrap.description": "DE1BEIFBH01 HLR1302 2023-09-22T02:00:00+02:00 5583948 alarm-notification major<br/>GU_Home_Location_Registry 0:0:12:0 hlr 2003-1-13-/=/v=1/sg-name=HLR_SG1--3-1<br/><br/>HLR RExTst. Routine Exercise Test (RExTst) in progress<br/>RExTst in progress", "snmptrap.eventuei": "uei/vnd/Kapsch/KSC", "snmptrap.servicename": "EMA-Ksc", "snmptrap.severityname": "MAJOR", "snmptrap.hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "snmptrap.counter": "1", "snmptrap.operinstruct": "No repair action required.", "snmptrap.lasteventtime": "2023-09-22 02:00:05.02", "snmptrap.ipaddr": "*************", "snmptrap.alarmtype": "1", "snmptrap.logmsg": "HLRS HLR1302 HLR RExTst (EM:DE1BEIFBH01 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) other/other", "snmptrap.firsteventtime": "2023-09-22 02:00:05.02", "snmptrap.nodelabel": "DE1BEIFBH01", "snmptrap.alarmid": "683151", "event.kafka.partition": 2, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-22T00:00:09.433Z", "event.kafka.offset": 8225, "event.kafka.key": null, "event.logstash.instance_name": "iictniaplv031", "event.uuid": "4982b6d6-e79b-41a0-8e10-8e54642bfada"}, "output": {"raise_time": "2024-01-01 12:00:01", "summary": "HLRS HLR1302 HLR RExTst (EM:DE1BEIFBH01 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) other/other", "severity": 4, "ci_id": "DE1BEIFBH01", "node": "DE1BEIFBH01", "event_type": "problem", "additional_data": "{\"alarm_id\": \"HLR1302\"}", "metric_name": "", "metric_type": "", "wake_up_time": "2024-01-01 12:30:01"}, "ignore_in_batch": "Ignored in batch because ci_id, metric_name, metric_type and raise_time are the same as a previous message."}, {"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::InformRequest:0x5b520a08 @request_id=645297552, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0x2df12f12 @name=[*******.*******.0], @value=#<SNMP::Integer:0x686e3554 @value=0>>, #<SNMP::VarBind:0x2327701b @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x106771bf @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x34ca451e @name=[*******.4.1.20306.*******.2], @value=\"682225\">, #<SNMP::VarBind:0x2d171246 @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/WINGcon/WSP\">, #<SNMP::VarBind:0x35a81857 @name=[*******.4.1.20306.*******.4], @value=\"de1smsc1b\">, #<SNMP::VarBind:0x1e697dd4 @name=[*******.4.1.20306.*******.5], @value=\"************\">, #<SNMP::VarBind:0x78ed29b2 @name=[*******.4.1.20306.*******.6], @value=\"SPM-Snmp\">, #<SNMP::VarBind:0x6dfd81cb @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x18e25683 @name=[*******.4.1.20306.*******.8], @value=\"10\">, #<SNMP::VarBind:0x4d301f96 @name=[*******.4.1.20306.*******.9], @value=\"INDETERMINATE\">, #<SNMP::VarBind:0x5c811f3b @name=[*******.4.1.20306.*******.10], @value=\"2023-09-19 22:46:19.874\">, #<SNMP::VarBind:0x5d732eb9 @name=[*******.4.1.20306.*******.11], @value=\"2023-09-22 18:03:04.227\">, #<SNMP::VarBind:0x57af2dda @name=[*******.4.1.20306.*******.12], @value=\"WSP MON_HW_ALERT noSuchInstance (/Host-noSuchInstance/noSuchInstance) other/other\">, #<SNMP::VarBind:0x1a2e4416 @name=[*******.4.1.20306.*******.13], @value=\"<p style=\\\"margin-bottom: 5px;\\\">Event Time: 19.09.2023 20:46:16.236</p><p style=\\\"font-weight: bold; margin-bottom: 5px;\\\">Wingcon Alarm</p>Key: MON_HW_ALERT<br/>        Unique ID: 93f2acbc-572d-11ee-8c50-98f2b310c9f4<br/>        Priority: noSuchInstance (noSuchInstance)<br/>        Description: noSuchInstance<br/>        Time: 19.09.2023 20:46:16.236<br/>        Host: noSuchInstance<br/>        Source: noSuchInstance\">, #<SNMP::VarBind:0x4e82f8af @name=[*******.4.1.20306.*******.14], @value=\"This event indicates that a fault condition has occurred.\">]>", "@timestamp": "2023-09-22T16:03:08.876Z", "@version": "1", "snmptrap.description": "<p style=\"margin-bottom: 5px;\">Event Time: 19.09.2023 20:46:16.236</p><p style=\"font-weight: bold; margin-bottom: 5px;\">Wingcon Alarm</p>Key: MON_HW_ALERT<br/>        Unique ID: 93f2acbc-572d-11ee-8c50-98f2b310c9f4<br/>        Priority: noSuchInstance (noSuchInstance)<br/>        Description: noSuchInstance<br/>        Time: 19.09.2023 20:46:16.236<br/>        Host: noSuchInstance<br/>        Source: noSuchInstance", "snmptrap.eventuei": "uei/vnd/WINGcon/WSP", "snmptrap.servicename": "SPM-Snmp", "snmptrap.severityname": "INDETERMINATE", "snmptrap.hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "snmptrap.counter": "10", "snmptrap.operinstruct": "This event indicates that a fault condition has occurred.", "snmptrap.lasteventtime": "2023-09-22 18:03:04.227", "snmptrap.ipaddr": "************", "snmptrap.alarmtype": "1", "snmptrap.logmsg": "WSP MON_HW_ALERT noSuchInstance (/Host-noSuchInstance/noSuchInstance) other/other", "snmptrap.firsteventtime": "2023-09-19 22:46:19.874", "snmptrap.nodelabel": "de1smsc1b", "snmptrap.alarmid": "682225", "event.kafka.partition": 1, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-22T16:03:08.991Z", "event.kafka.offset": 8496, "event.kafka.key": null, "event.logstash.instance_name": "iictnibflv003", "event.uuid": "19fb3cc0-50f6-4b70-ab19-dee942300a8e"}, "output": {"ci_id": "DE1SMSC1B", "metric_name": "", "metric_type": "", "raise_time": "2024-01-01 12:00:01"}, "ignore_in_batch": "Ignored in batch because ci_id, metric_name, metric_type and raise_time are the same as a previous message."}, {"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::InformRequest:0x2e6dc501 @request_id=645359294, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0x1a56d241 @name=[*******.*******.0], @value=#<SNMP::Integer:0x6fad7146 @value=0>>, #<SNMP::VarBind:0x2f28e47f @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x4ecfd190 @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x62a7c6d2 @name=[*******.4.1.20306.*******.2], @value=\"684340\">, #<SNMP::VarBind:0x6604bc81 @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/SOS\">, #<SNMP::VarBind:0xe67a722 @name=[*******.4.1.20306.*******.4], @value=\"DE1BEIFBM01\">, #<SNMP::VarBind:0x3e14b42f @name=[*******.4.1.20306.*******.5], @value=\"************\">, #<SNMP::VarBind:0x62597469 @name=[*******.4.1.20306.*******.6], @value=\"EMA-Kcbm\">, #<SNMP::VarBind:0x21fa3c17 @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x1297c1bd @name=[*******.4.1.20306.*******.8], @value=\"3\">, #<SNMP::VarBind:0x9b9aa0c @name=[*******.4.1.20306.*******.9], @value=\"CRITICAL\">, #<SNMP::VarBind:0x206a6d6e @name=[*******.4.1.20306.*******.10], @value=\"2023-09-25 15:00:47.117\">, #<SNMP::VarBind:0x5000f7f0 @name=[*******.4.1.20306.*******.11], @value=\"2023-09-25 15:00:47.121\">, #<SNMP::VarBind:0x7cb64e64 @name=[*******.4.1.20306.*******.12], @value=\"MSCS TRK103 GROUP_ALARM FTS_LABODICA1_1 77% BUSY (EM-DE1MSC1CA/SOS-TRK/TRK-FTS_LABODICA1_1) processingErrorAlarm/applicationSubsystemFailure\">, #<SNMP::VarBind:0x389b6234 @name=[*******.4.1.20306.*******.13], @value=\"DE1MSC1CA  ***  TRK103 SEP25 15:00:47 3479 FLT GROUP_ALARM   FTS_LABODICA1_1 77% BUSY<br/>\">, #<SNMP::VarBind:0x4be96954 @name=[*******.4.1.20306.*******.14], @value=\"\">]>", "@timestamp": "2023-09-25T13:00:50.462Z", "@version": "1", "snmptrap.description": "DE1MSC1CA  ***  TRK103 SEP25 15:00:47 3479 FLT GROUP_ALARM   FTS_LABODICA1_1 77% BUSY<br/>", "snmptrap.eventuei": "uei/vnd/Kapsch/SOS", "snmptrap.servicename": "EMA-Kcbm", "snmptrap.severityname": "CRITICAL", "snmptrap.hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "snmptrap.counter": "3", "snmptrap.operinstruct": "", "snmptrap.lasteventtime": "2023-09-25 15:00:47.121", "snmptrap.ipaddr": "************", "snmptrap.alarmtype": "1", "snmptrap.logmsg": "MSCS TRK103 GROUP_ALARM FTS_LABODICA1_1 77% BUSY (EM-DE1MSC1CA/SOS-TRK/TRK-FTS_LABODICA1_1) processingErrorAlarm/applicationSubsystemFailure", "snmptrap.firsteventtime": "2023-09-25 15:00:47.117", "snmptrap.nodelabel": "DE1BEIFBM01", "snmptrap.alarmid": "684340", "event.kafka.partition": 1, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-25T13:00:50.568Z", "event.kafka.offset": 9747, "event.kafka.key": null, "event.logstash.instance_name": "iictnibflv003", "event.uuid": "b7699885-a6f2-4844-ac04-8440a3ba7d0d"}, "output": {"summary": "MSCS TRK103 GROUP_ALARM FTS_LABODICA1_1 77% BUSY (EM-DE1MSC1CA/SOS-TRK/TRK-FTS_LABODICA1_1) processingErrorAlarm/applicationSubsystemFailure", "severity": 5, "metric_name": "", "metric_type": "", "ci_id": "DE1BEIFBM01", "manager": "mon-cnms", "node": "DE1BEIFBM01", "event_type": "problem", "raise_time": "2024-01-01 12:00:01", "clear_time": null, "clear_type": "automatic", "action_class": "TE", "top_level": "A1926", "event_id": "uei/vnd/Kapsch/SOS", "node_alias": "************", "actionable": false, "agent_id": 0, "additional_data": "{\"alarm_id\": \"TRK103\"}"}}, {"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::InformRequest:0x1119144e @request_id=645284789, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0x3b0e4175 @name=[*******.*******.0], @value=#<SNMP::Integer:0x686fcc0b @value=0>>, #<SNMP::VarBind:0x310d1e2c @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x32aea215 @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x7be258dc @name=[*******.4.1.20306.*******.2], @value=\"683173\">, #<SNMP::VarBind:0x6dfbdc3f @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kontron/FS-BACKUP\">, #<SNMP::VarBind:0x34de43d2 @name=[*******.4.1.20306.*******.4], @value=\"de1rpm1\">, #<SNMP::VarBind:0x73a7f156 @name=[*******.4.1.20306.*******.5], @value=\"************\">, #<SNMP::VarBind:0x1a6a7346 @name=[*******.4.1.20306.*******.6], @value=\"\">, #<SNMP::VarBind:0x29434e10 @name=[*******.4.1.20306.*******.7], @value=\"3\">, #<SNMP::VarBind:0x6f455839 @name=[*******.4.1.20306.*******.8], @value=\"1\">, #<SNMP::VarBind:0x23a429c6 @name=[*******.4.1.20306.*******.9], @value=\"NORMAL\">, #<SNMP::VarBind:0x6a1d9eb7 @name=[*******.4.1.20306.*******.10], @value=\"2023-09-22 03:40:03.446\">, #<SNMP::VarBind:0x591d6e2a @name=[*******.4.1.20306.*******.11], @value=\"2023-09-22 03:40:03.446\">, #<SNMP::VarBind:0x1c5c71a0 @name=[*******.4.1.20306.*******.12], @value=\"RPM 100-BackupExecSuccess Incremental backup success (linux-etc) other/other\">, #<SNMP::VarBind:0x1eb491f4 @name=[*******.4.1.20306.*******.13], @value=\"<p style=\\\"margin-bottom: 5px;\\\">Event Time: 22.09.2023 03:40:01.854</p><p style=\\\"font-weight: bold; margin-bottom: 5px;\\\">Fs Backup Alarm Notification</p><p style=\\\"margin-bottom: 3px;\\\">This notification describes an alarm condition<br/>occurred at the FS backup system.</p><b>Parameters</b><br/>      Node Address: ************<br/>      Node Name: de1rpm1.de1coam.gsmr.infrabel.be<br/>      Time: 22.09.2023T03:40:01.854+02:00<br/>      Severity: normal (3)<br/>      Fault Code: 100-BackupExecSuccess<br/>      Component Name: linux-etc<br/>      Backup Class: <br/>      Backup Type: INCR<br/>      Message: Incremental backup success<br/>      Description: Incremental backup was created successfully.<br/>      Operator Instructions:\">, #<SNMP::VarBind:0x18da4cb3 @name=[*******.4.1.20306.*******.14], @value=\"\">]>", "@timestamp": "2023-09-22T01:40:09.138Z", "@version": "1", "snmptrap.description": "<p style=\"margin-bottom: 5px;\">Event Time: 22.09.2023 03:40:01.854</p><p style=\"font-weight: bold; margin-bottom: 5px;\">Fs Backup Alarm Notification</p><p style=\"margin-bottom: 3px;\">This notification describes an alarm condition<br/>occurred at the FS backup system.</p><b>Parameters</b><br/>      Node Address: ************<br/>      Node Name: de1rpm1.de1coam.gsmr.infrabel.be<br/>      Time: 22.09.2023T03:40:01.854+02:00<br/>      Severity: normal (3)<br/>      Fault Code: 100-BackupExecSuccess<br/>      Component Name: linux-etc<br/>      Backup Class: <br/>      Backup Type: INCR<br/>      Message: Incremental backup success<br/>      Description: Incremental backup was created successfully.<br/>      Operator Instructions:", "snmptrap.eventuei": "uei/vnd/Kontron/FS-BACKUP", "snmptrap.servicename": "", "snmptrap.severityname": "NORMAL", "snmptrap.hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "snmptrap.counter": "1", "snmptrap.operinstruct": "", "snmptrap.lasteventtime": "2023-09-22 03:40:03.446", "snmptrap.ipaddr": "************", "snmptrap.alarmtype": "3", "snmptrap.logmsg": "RPM 100-BackupExecSuccess Incremental backup success (linux-etc) other/other", "snmptrap.firsteventtime": "2023-09-22 03:40:03.446", "snmptrap.nodelabel": "de1rpm1", "snmptrap.alarmid": "683173", "event.kafka.partition": 1, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-22T01:40:09.243Z", "event.kafka.offset": 8252, "event.kafka.key": null, "event.logstash.instance_name": "iictnibflv003", "event.uuid": "47b9ac54-536b-40c5-a664-dc7eacb7fc93"}, "output": {}}, {"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::InformRequest:0x480827b0 @request_id=645295605, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0x2dd35ace @name=[*******.*******.0], @value=#<SNMP::Integer:0x71fa7d67 @value=0>>, #<SNMP::VarBind:0x78c6e14f @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x1cdfde60 @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x2bede21f @name=[*******.4.1.20306.*******.2], @value=\"683322\">, #<SNMP::VarBind:0x95ea340 @name=[*******.4.1.20306.*******.3], @value=\"uei/RDNSM/CORE/MCP\">, #<SNMP::VarBind:0x46ddf7bd @name=[*******.4.1.20306.*******.4], @value=\"de1mgw1a\">, #<SNMP::VarBind:0x24930dd8 @name=[*******.4.1.20306.*******.5], @value=\"\">, #<SNMP::VarBind:0x60aaee4f @name=[*******.4.1.20306.*******.6], @value=\"\">, #<SNMP::VarBind:0x1c1215b9 @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x35db6999 @name=[*******.4.1.20306.*******.8], @value=\"1\">, #<SNMP::VarBind:0x99a4f37 @name=[*******.4.1.20306.*******.9], @value=\"MAJOR\">, #<SNMP::VarBind:0x3ef500dd @name=[*******.4.1.20306.*******.10], @value=\"2023-09-22 15:54:51.825\">, #<SNMP::VarBind:0x32c67567 @name=[*******.4.1.20306.*******.11], @value=\"2023-09-22 15:54:51.825\">, #<SNMP::VarBind:0x5c3f4507 @name=[*******.4.1.20306.*******.12], @value=\"MGW-R PmNoData The node 'de1mgw1a' [38] provides no reliable measurement samples since 22.09.2023 15:30:00 (/Measurements/Collection-de1mgw1a) other/other\">, #<SNMP::VarBind:0x7075a40 @name=[*******.4.1.20306.*******.13], @value=\"<p style=\\\"margin-bottom: 5px;\\\">Event Time: 22.09.2023 15:54:51.825</p><p style=\\\"font-weight: bold; margin-bottom: 5px;\\\">RDNSM MCP Fault</p><p style=\\\"margin-bottom: 3px;\\\">The measurement entity MeasurementCollection [NodeId=38, NodeLabel=de1mgw1a , StartTime=22.09.2023 15:30:00] provided no reliable samples.</p>\">, #<SNMP::VarBind:0x11e25195 @name=[*******.4.1.20306.*******.14], @value=\"Check if the affected node correctly provides the performance data and check the availability of the corresponding MDC service and its log.\">]>", "@timestamp": "2023-09-22T13:54:55.364Z", "@version": "1", "snmptrap.description": "<p style=\"margin-bottom: 5px;\">Event Time: 22.09.2023 15:54:51.825</p><p style=\"font-weight: bold; margin-bottom: 5px;\">RDNSM MCP Fault</p><p style=\"margin-bottom: 3px;\">The measurement entity MeasurementCollection [NodeId=38, NodeLabel=de1mgw1a , StartTime=22.09.2023 15:30:00] provided no reliable samples.</p>", "snmptrap.eventuei": "uei/RDNSM/CORE/MCP", "snmptrap.servicename": "", "snmptrap.severityname": "MAJOR", "snmptrap.hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "snmptrap.counter": "1", "snmptrap.operinstruct": "Check if the affected node correctly provides the performance data and check the availability of the corresponding MDC service and its log.", "snmptrap.lasteventtime": "2023-09-22 15:54:51.825", "snmptrap.ipaddr": "", "snmptrap.alarmtype": "1", "snmptrap.logmsg": "MGW-R PmNoData The node 'de1mgw1a' [38] provides no reliable measurement samples since 22.09.2023 15:30:00 (/Measurements/Collection-de1mgw1a) other/other", "snmptrap.firsteventtime": "2023-09-22 15:54:51.825", "snmptrap.nodelabel": "de1mgw1a", "snmptrap.alarmid": "683322", "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-22T13:54:55.465Z", "event.kafka.offset": 8495, "event.kafka.key": null, "event.logstash.instance_name": "iictniaplv031", "event.uuid": "bfda191a-97e8-4c12-bfde-d4aca3253584"}, "output": {"summary": "MGW-R PmNoData The node 'de1mgw1a' [38] provides no reliable measurement samples since 22.09.2023 15:30:00 (/Measurements/Collection-de1mgw1a) other/other", "severity": 4, "metric_name": "", "metric_type": "", "ci_id": "DE1MGW1A", "manager": "mon-cnms", "node": "DE1MGW1A", "event_type": "problem", "raise_time": "2024-01-01 12:00:01", "clear_time": null, "clear_type": "automatic", "action_class": "TE", "top_level": "A1926", "event_id": "uei/RDNSM/CORE/MCP", "node_alias": "", "actionable": false, "agent_id": 0, "additional_data": "{\"alarm_id\": \"PmNoData\"}"}}, {"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::SNMPv2_Trap:0x2fe18e14 @request_id=645282942, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0xb70970 @name=[*******.*******.0], @value=#<SNMP::Integer:0x37e97b16 @value=0>>, #<SNMP::VarBind:0x14dbd2d1 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>]>", "@timestamp": "2023-09-21T23:41:44.897Z", "@version": "1", "snmptrap.description": null, "snmptrap.eventuei": null, "snmptrap.servicename": null, "snmptrap.severityname": null, "snmptrap.hostname": null, "snmptrap.counter": null, "snmptrap.operinstruct": null, "snmptrap.lasteventtime": null, "snmptrap.ipaddr": null, "snmptrap.alarmtype": null, "snmptrap.logmsg": null, "snmptrap.firsteventtime": null, "snmptrap.nodelabel": null, "snmptrap.alarmid": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-21T23:41:44.998Z", "event.kafka.offset": 8232, "event.kafka.key": null, "event.logstash.instance_name": "iictniaplv031", "event.uuid": "545e8c35-08bf-461c-8e16-af8d06ab95f8"}, "output": {"summary": "C-NMS Heartbeat Message", "severity": 1, "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "ci_id": "C-NMS_HB", "manager": "mon-cnms", "node": "C-NMS", "event_type": "heartbeat", "raise_time": "2024-01-01 12:00:01", "wake_up_time": "2024-01-01 12:00:01", "clear_time": null, "clear_type": "manual", "action_class": "TE", "top_level": "A1926,A1927", "event_id": "", "node_alias": "C-NMS", "actionable": false, "agent_id": 0, "additional_data": null}}, {"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::InformRequest:0x5f942185 @request_id=645282821, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0x600f316c @name=[*******.*******.0], @value=#<SNMP::Integer:0x5fe236f8 @value=0>>, #<SNMP::VarBind:0x2104ac88 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x1743041f @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0xe5ec3ca @name=[*******.4.1.20306.*******.2], @value=\"683134\">, #<SNMP::VarBind:0x4045610f @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x4421e563 @name=[*******.4.1.20306.*******.4], @value=\"DE2BEIFBH02\">, #<SNMP::VarBind:0x4bcd7204 @name=[*******.4.1.20306.*******.5], @value=\"*************\">, #<SNMP::VarBind:0x3375b824 @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0x5853ea5c @name=[*******.4.1.20306.*******.7], @value=\"2\">, #<SNMP::VarBind:0x131216c8 @name=[*******.4.1.20306.*******.8], @value=\"2\">, #<SNMP::VarBind:0x33ef1bfc @name=[*******.4.1.20306.*******.9], @value=\"NORMAL\">, #<SNMP::VarBind:0x2dc5213d @name=[*******.4.1.20306.*******.10], @value=\"2023-09-22 01:38:21.599\">, #<SNMP::VarBind:0x6a032fd3 @name=[*******.4.1.20306.*******.11], @value=\"2023-09-22 01:38:21.6\">, #<SNMP::VarBind:0x6924c00d @name=[*******.4.1.20306.*******.12], @value=\"HLRS HLR1301 HLR sync (EM:DE2BEIFBH02 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) qualityOfServiceAlarm/other\">, #<SNMP::VarBind:0x22df062e @name=[*******.4.1.20306.*******.13], @value=\"DE2BEIFBH02 HLR1301 2023-09-22T01:38:19+02:00 6193637 alarm-notification cleared<br/>GU_Home_Location_Registry 0:0:12:0 hlr 2003-1-13-/=/v=1/sg-name=HLR_SG1--2-99<br/><br/>HLR sync. The active and standby instances of the HLR application are not in sync<br/>Alarm cleared\">, #<SNMP::VarBind:0x26171781 @name=[*******.4.1.20306.*******.14], @value=\"Investigate the cause of the out-of-sync condition. Then, use the cli 'sync' command to sync the HLR application.\">]>", "@timestamp": "2023-09-21T23:38:29.271Z", "@version": "1", "snmptrap.description": "DE2BEIFBH02 HLR1301 2023-09-22T01:38:19+02:00 6193637 alarm-notification cleared<br/>GU_Home_Location_Registry 0:0:12:0 hlr 2003-1-13-/=/v=1/sg-name=HLR_SG1--2-99<br/><br/>HLR sync. The active and standby instances of the HLR application are not in sync<br/>Alarm cleared", "snmptrap.eventuei": "uei/vnd/Kapsch/KSC", "snmptrap.servicename": "EMA-Ksc", "snmptrap.severityname": "NORMAL", "snmptrap.hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "snmptrap.counter": "2", "snmptrap.operinstruct": "Investigate the cause of the out-of-sync condition. Then, use the cli 'sync' command to sync the HLR application.", "snmptrap.lasteventtime": "2023-09-22 01:38:21.6", "snmptrap.ipaddr": "*************", "snmptrap.alarmtype": "2", "snmptrap.logmsg": "HLRS HLR1301 HLR sync (EM:DE2BEIFBH02 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) qualityOfServiceAlarm/other", "snmptrap.firsteventtime": "2023-09-22 01:38:21.599", "snmptrap.nodelabel": "DE2BEIFBH02", "snmptrap.alarmid": "683134", "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-21T23:38:29.400Z", "event.kafka.offset": 8226, "event.kafka.key": null, "event.logstash.instance_name": "iictniaplv031", "event.uuid": "8e665895-baf1-485e-9bd6-8384e422781b"}, "output": {"summary": "HLRS HLR1301 HLR sync (EM:DE2BEIFBH02 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) qualityOfServiceAlarm/other", "severity": 0, "metric_name": "", "metric_type": "", "ci_id": "DE2BEIFBH02", "manager": "mon-cnms", "node": "DE2BEIFBH02", "event_type": "clear", "raise_time": "2024-01-01 12:00:01", "clear_time": "2024-01-01 12:00:01", "clear_type": "automatic", "action_class": "TE", "top_level": "A1926", "event_id": "uei/vnd/Kapsch/KSC", "node_alias": "*************", "actionable": false, "agent_id": 0, "additional_data": "{\"alarm_id\": \"HLR1301\"}"}}, {"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::InformRequest:0x73bbdb97 @request_id=645282831, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0x43c03b4c @name=[*******.*******.0], @value=#<SNMP::Integer:0x8d4cf7e @value=0>>, #<SNMP::VarBind:0x259eca90 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x5e6cc070 @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x1c349aaa @name=[*******.4.1.20306.*******.2], @value=\"683121\">, #<SNMP::VarBind:0x3235196d @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x612c9ff1 @name=[*******.4.1.20306.*******.4], @value=\"DE2BEIFBH02\">, #<SNMP::VarBind:0x59fbace4 @name=[*******.4.1.20306.*******.5], @value=\"*************\">, #<SNMP::VarBind:0x61228cbb @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0x7b460f83 @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x7e10d40d @name=[*******.4.1.20306.*******.8], @value=\"1\">, #<SNMP::VarBind:0x7a08cba7 @name=[*******.4.1.20306.*******.9], @value=\"CLEARED\">, #<SNMP::VarBind:0xf3ac709 @name=[*******.4.1.20306.*******.10], @value=\"2023-09-22 01:30:03.066\">, #<SNMP::VarBind:0x44cbfb7a @name=[*******.4.1.20306.*******.11], @value=\"2023-09-22 01:30:03.066\">, #<SNMP::VarBind:0x3c83ff52 @name=[*******.4.1.20306.*******.12], @value=\"HLRS HLR1301 HLR sync (EM:DE2BEIFBH02 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) qualityOfServiceAlarm/other\">, #<SNMP::VarBind:0x6a4e96 @name=[*******.4.1.20306.*******.13], @value=\"DE2BEIFBH02 HLR1301 2023-09-22T01:30:01+02:00 6193618 alarm-notification major<br/>GU_Home_Location_Registry 0:0:8:0 hlr 2003-1-13-/=/v=1/sg-name=HLR_SG1--2-99<br/><br/>HLR sync. The active and standby instances of the HLR application are not in sync<br/>Callp application out of sync\">, #<SNMP::VarBind:0x5931c31d @name=[*******.4.1.20306.*******.14], @value=\"Investigate the cause of the out-of-sync condition. Then, use the cli 'sync' command to sync the HLR application.\">]>", "@timestamp": "2023-09-21T23:38:29.296Z", "@version": "1", "snmptrap.description": "DE2BEIFBH02 HLR1301 2023-09-22T01:30:01+02:00 6193618 alarm-notification major<br/>GU_Home_Location_Registry 0:0:8:0 hlr 2003-1-13-/=/v=1/sg-name=HLR_SG1--2-99<br/><br/>HLR sync. The active and standby instances of the HLR application are not in sync<br/>Callp application out of sync", "snmptrap.eventuei": "uei/vnd/Kapsch/KSC", "snmptrap.servicename": "EMA-Ksc", "snmptrap.severityname": "CLEARED", "snmptrap.hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "snmptrap.counter": "1", "snmptrap.operinstruct": "Investigate the cause of the out-of-sync condition. Then, use the cli 'sync' command to sync the HLR application.", "snmptrap.lasteventtime": "2023-09-22 01:30:03.066", "snmptrap.ipaddr": "*************", "snmptrap.alarmtype": "1", "snmptrap.logmsg": "HLRS HLR1301 HLR sync (EM:DE2BEIFBH02 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) qualityOfServiceAlarm/other", "snmptrap.firsteventtime": "2023-09-22 01:30:03.066", "snmptrap.nodelabel": "DE2BEIFBH02", "snmptrap.alarmid": "683121", "event.kafka.partition": 2, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-21T23:38:29.400Z", "event.kafka.offset": 8212, "event.kafka.key": null, "event.logstash.instance_name": "iictniaplv031", "event.uuid": "839e64c3-4b05-422c-a218-ff63d9604a1e"}, "output": {"summary": "HLRS HLR1301 HLR sync (EM:DE2BEIFBH02 se:hlr entity:hlr app-info:2003-13 v:1 sg-name:HLR_SG1) qualityOfServiceAlarm/other", "severity": 0, "metric_name": "", "metric_type": "", "ci_id": "DE2BEIFBH02", "manager": "mon-cnms", "node": "DE2BEIFBH02", "event_type": "clear", "raise_time": "2024-01-01 12:00:01", "clear_time": "2024-01-01 12:00:01", "clear_type": "automatic", "action_class": "TE", "top_level": "A1926", "event_id": "uei/vnd/Kapsch/KSC", "node_alias": "*************", "actionable": false, "agent_id": 0, "additional_data": "{\"alarm_id\": \"HLR1301\"}"}, "ignore_in_batch": "Ignored in batch because it is the same as offset 8226."}]}