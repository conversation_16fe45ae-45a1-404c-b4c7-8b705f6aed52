kind: Secret
apiVersion: v1
metadata:
  name: a2110-optic-matcher-secret-#{appEnv}#
  namespace: a2110-olympus-monitoring
stringData:
  DB_HOST: "#{databaseHost}#"
  DB_PORT: "#{databasePort}#"
  DB_NAME: "#{databaseName}#"
  DB_USER: "#{databaseUser}#"
  DB_PASSWORD: "#{databasePassword}#"
  OPTIC_DB_PASSWORD: "#{opticDbPassword}#"

  GRAPH_CLIENT_ID: "#{clientId}#"
  GRAPH_CLIENT_SECRET: "#{clientSecret}#"
  GRAPH_TENANT_ID: "#{tenantId}#"

  ELASTIC_APM_SECRET_TOKEN: "#{elasticApmSecretToken}#"

type: Opaque
