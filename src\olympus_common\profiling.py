"""Profiling utilities.

The result of the profiling can be viewed using snakeviz, which is included as a dev dependency.
To view the results, install snakeviz and use it. (snakeviz <path_to_cprofile_file>)

The functions `start` and `stop` are not named after their cProfile counterparts (enable/disable) to avoid confusion
with the built-in functions.

Note that files will be written to the provided `destpath` with the name `destname_{timestamp}.cprofile`. It is not
recommended to enable profiling on deployed instances as it might flood the disk with cprofile files.
"""

import cProfile
import logging
import pstats
from pathlib import Path
from time import perf_counter

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware, DispatchFunction, RequestResponseEndpoint
from starlette.types import ASGIApp

from olympus_common.utils import now_timestamp


def start() -> cProfile.Profile:
    """Enable the profiler."""
    profiler = cProfile.Profile()
    profiler.enable()
    return profiler


def stop(profiler: cProfile.Profile, destpath: Path | None = None, destname: str = "profile") -> None:
    """Disable the profiler.

    If no destpath is provided, the profiler will not save the results.

    The destname is appended with a timestamp and saved as a file with a .cprofile extension.
    (ie: profile_1740142788.cprofile)
    """
    profiler.disable()
    if not destpath:
        return
    stats = pstats.Stats(profiler)
    now_ts = now_timestamp()
    stats.dump_stats(destpath / f"{now_ts}_{destname}.cprofile")


class ProfilingMiddleware(BaseHTTPMiddleware):
    """Represent HTTP middleware to profile requests.

    Add this middleware to an app using `app.add_middleware(ProfilingMiddleware)`.

    The advantage of using this middleware is that it will implicitly profile each request and write the result to a
    cprofile.
    """

    def __init__(self, app: ASGIApp, destpath: Path, dispatch: DispatchFunction | None = None) -> None:
        super().__init__(app, dispatch=dispatch)
        self.destpath = destpath

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        """Profile the request and write the result to a cprofile."""
        profile_name = "profile_request"

        profiler = start()
        response = await call_next(request)
        endpoint = request.scope.get("endpoint")  # only have access to this after calling next
        if endpoint:
            profile_name = f"profile_{endpoint.__name__}"
        stop(profiler, self.destpath, destname=profile_name)

        return response


def perf_check(log_level: int = logging.INFO, threshold_seconds: float = 0.0):
    """Check performance of the function.

    The execution time will only be logged if execution time exceeds the threshold_seconds (default: 0.0)
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            start = perf_counter()
            try:
                return func(*args, **kwargs)
            finally:
                end = perf_counter()
                execution_time = end - start
                if execution_time > threshold_seconds:
                    logging.log(
                        log_level,
                        f"Function {func.__module__}.{func.__name__} took {execution_time:.4f} seconds",
                    )

        return wrapper

    return decorator
