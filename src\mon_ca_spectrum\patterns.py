"""Patterns module for mon-ca-spectrum."""

import re

pool_name_regex1 = re.compile(r"PoolName=(.+)")
pool_name_regex2 = re.compile(r"LPname = (.+)")
pool_name_regex3 = re.compile(r"bsnAPName = (.+)")
syslog_event_regex = re.compile(r"Msg: {2}([A-Za-z\d\-\:\+\.]+\s){0,8}")
ci_id_regex1 = re.compile(r"^[A-Za-z]{6,7}[\d]{1,2}v-m$")
ci_id_regex2 = re.compile(r"^[A-Za-z]{6,7}[\d]{1,2}[iov]$")
ci_id_regex3 = re.compile(r"^(Dt\.R\.|St\.R\.).*")
severity_regex = re.compile(r"-(ACC|TEST)(-|$)")
summary_regex = re.compile(r"^(.+?)\n")
raisetime_regex = re.compile(r"\b(\d{2}) (\w{3}), (\d{4}) - (\d{2}:\d{2}:\d{2})\b")
