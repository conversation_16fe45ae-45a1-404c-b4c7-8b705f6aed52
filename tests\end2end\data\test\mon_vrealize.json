{"extra_env": {"OLYMPUS_AGENT_ID": "26"}, "agent_ids": [26], "data": [{"input": {"@version": "1", "@timestamp": "2024-07-09T16:02:50.042786345Z", "message": "#<SNMP::SNMPv2_Trap:0x5f8de925 @varbind_list=[#<SNMP::VarBind:0x32b425a5 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x2eaf5dd9 @value=2554052038>>, #<SNMP::VarBind:0x1e5b4de8 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.47]>, #<SNMP::VarBind:0x35dfeef2 @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x26d57ce1 @name=[*******.4.1.6876.********.2.0], @value=\"ACI_V<PERSON>_<PERSON>\">, #<SNMP::VarBind:0x7240bc77 @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x36308bda @name=[*******.4.1.6876.********.4.0], @value=\"1720540951826\">, #<SNMP::VarBind:0x7c525b90 @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x716d028f @name=[*******.4.1.6876.********.6.0], @value=\"Alert by id 1e841f6f-8c60-4512-9f15-0afbe0fef4e2 is cancelled at Tue Jul 09 18:02:31 CEST 2024:\">, #<SNMP::VarBind:0x6b9bbd1 @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/254ea96e-7f5b-4f61-a600-47a6ea7df889/alerts-and-symptoms/alerts/1e841f6f-8c60-4512-9f15-0afbe0fef4e2\">, #<SNMP::VarBind:0x34659e87 @name=[*******.4.1.6876.********.8.0], @value=\"1e841f6f-8c60-4512-9f15-0afbe0fef4e2\">, #<SNMP::VarBind:0x5a85cd40 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: d7b3e608-8266-4650-9d08-eed829c67ad4\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: Network traffic is blocked for one or more ports\\nactive: true\\nobj.1.name: ACI_VDS_Wallace\\nobj.1.id: 254ea96e-7f5b-4f61-a600-47a6ea7df889\\nobj.1.metric: Summary|Number of Blocked Ports\\nobj.1.info: HT above 1 > 0\\n\">, #<SNMP::VarBind:0x4365c5e5 @name=[*******.4.1.6876.********.10.0], @value=\"Network\">, #<SNMP::VarBind:0x849896b @name=[*******.4.1.6876.********.11.0], @value=\"Performance\">, #<SNMP::VarBind:0x10c0af43 @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x645b9fd6 @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x9fc2455 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x4d3af6d0 @name=[*******.4.1.6876.********.15.0], @value=\"Summary|Number of Blocked Ports\">, #<SNMP::VarBind:0x5815e283 @name=[*******.4.1.6876.********.16.0], @value=\"VmwareDistributedVirtualSwitch\">, #<SNMP::VarBind:0x60886378 @name=[*******.4.1.6876.********.17.0], @value=\"Network traffic is blocked for one or more ports\">, #<SNMP::VarBind:0x8be3db0 @name=[*******.4.1.6876.********.18.0], @value=\"Network traffic is blocked for one or more ports on the vSphere Distributed Switch\">, #<SNMP::VarBind:0x1228d601 @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x65de3f3d @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x466e035d @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x3f44e59d @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x6b08a4aa @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "05ba98c1-28a3-4cb1-b942-3cd4c58f0f05", "event.kafka.offset": 4645, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-09T16:02:50.144Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.vmwareAlertEntityName": "ACI_VDS_Wallace", "snmptrap.vmwareAlertTimestamp": "1720540951826", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "Summary|Number of Blocked Ports", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.47", "snmptrap.vmwareAlertDefinitionName": "Network traffic is blocked for one or more ports", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "Network traffic is blocked for one or more ports on the vSphere Distributed Switch", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "Alert by id 1e841f6f-8c60-4512-9f15-0afbe0fef4e2 is cancelled at Tue Jul 09 18:02:31 CEST 2024:", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "295 days, 14:35:20.38", "snmptrap.vmwareAlertType": "Network", "snmptrap.vmwareAlertSubtype": "Performance", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "VmwareDistributedVirtualSwitch", "snmptrap.vmwareAlertMessage": "symptomSet: d7b3e608-8266-4650-9d08-eed829c67ad4\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: Network traffic is blocked for one or more ports\nactive: true\nobj.1.name: ACI_VDS_Wallace\nobj.1.id: 254ea96e-7f5b-4f61-a600-47a6ea7df889\nobj.1.metric: Summary|Number of Blocked Ports\nobj.1.info: HT above 1 > 0\n", "snmptrap.vmwareAlertID": "1e841f6f-8c60-4512-9f15-0afbe0fef4e2", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/254ea96e-7f5b-4f61-a600-47a6ea7df889/alerts-and-symptoms/alerts/1e841f6f-8c60-4512-9f15-0afbe0fef4e2", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-12T09:30:47.438627892Z", "message": "#<SNMP::SNMPv2_Trap:0x7edcc0d3 @varbind_list=[#<SNMP::VarBind:0x3fe1cae8 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x16d67384 @value=**********>>, #<SNMP::VarBind:0xe14f3d5 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x6ec9141c @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv019.msnet.railb.be\">, #<SNMP::VarBind:0x5fce35a7 @name=[*******.4.1.6876.********.2.0], @value=\"iictliesxs101.msnet.railb.be\">, #<SNMP::VarBind:0x7a212789 @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x5302e943 @name=[*******.4.1.6876.********.4.0], @value=\"1720776646153\">, #<SNMP::VarBind:0x75cd7ecb @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x5b800ed8 @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id f5a6c48c-9eac-41b8-8679-71c75f3aa44b is generated at Fri Jul 12 11:30:46 CEST 2024;\">, #<SNMP::VarBind:0x286e674e @name=[*******.4.1.6876.********.7.0], @value=\"https://************/ui/index.action#environment/object-browser/hierarchy/50e0b28a-d90a-43e7-9c29-23410e6e2a74/alerts-and-symptoms/alerts/f5a6c48c-9eac-41b8-8679-71c75f3aa44b\">, #<SNMP::VarBind:0x6c62dbf1 @name=[*******.4.1.6876.********.8.0], @value=\"f5a6c48c-9eac-41b8-8679-71c75f3aa44b\">, #<SNMP::VarBind:0x72c7cb90 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: ff617352-e842-4fe1-9b11-048fae5ef278\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: Lost connection to NFS server\\nactive: true\\nobj.1.name: iictliesxs101.msnet.railb.be\\nobj.1.id: 50e0b28a-d90a-43e7-9c29-23410e6e2a74\\nobj.1.metric: \\nobj.1.info: Lost connection to NFS server\\n\">, #<SNMP::VarBind:0x29eaedde @name=[*******.4.1.6876.********.10.0], @value=\"Network\">, #<SNMP::VarBind:0x72e5abba @name=[*******.4.1.6876.********.11.0], @value=\"Availability\">, #<SNMP::VarBind:0x8224b44 @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x44759a9b @name=[*******.4.1.6876.********.13.0], @value=\"warning\">, #<SNMP::VarBind:0x3a8f1bd6 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x6568cf46 @name=[*******.4.1.6876.********.15.0], @value=\"\">, #<SNMP::VarBind:0x7f0ac0c4 @name=[*******.4.1.6876.********.16.0], @value=\"HostSystem\">, #<SNMP::VarBind:0x17c33cfc @name=[*******.4.1.6876.********.17.0], @value=\"The host lost connectivity to a Network File System (NFS) server\">, #<SNMP::VarBind:0x5550d479 @name=[*******.4.1.6876.********.18.0], @value=\"The host lost connectivity to one or more NFS servers. The NFS volumes and the virtual machines on the affected volumes might be unavailable to the server. This fault typically occurs in an environment where NFS was previously working and NFS connectivity was lost.\">, #<SNMP::VarBind:0x76e1336a @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x5f44c192 @name=[*******.4.1.6876.********.20.0], @value=\"SNMP Olympus Prd\">, #<SNMP::VarBind:0x51ee325a @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x2cec702 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x7d4bde52 @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=2195136, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "b28d493a-b609-4aef-81c9-9509a54f387c", "event.kafka.offset": 846, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_monceau-events-prd", "event.kafka.topic": "a1232-vserver_monceau-events-prd", "event.kafka.timestamp": "2024-07-12T09:30:47.539Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.vmwareAlertEntityName": "iictliesxs101.msnet.railb.be", "snmptrap.vmwareAlertTimestamp": "1720776646153", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "The host lost connectivity to a Network File System (NFS) server", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "The host lost connectivity to one or more NFS servers. The NFS volumes and the virtual machines on the affected volumes might be unavailable to the server. This fault typically occurs in an environment where NFS was previously working and NFS connectivity was lost.", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id f5a6c48c-9eac-41b8-8679-71c75f3aa44b is generated at Fri Jul 12 11:30:46 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "322 days, 21:14:54.31", "snmptrap.vmwareAlertType": "Network", "snmptrap.vmwareAlertSubtype": "Availability", "snmptrap.vmwareAlertAliveServerName": "iictcismwv019.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "SNMP Olympus Prd", "snmptrap.vmwareAlertResourceKind": "HostSystem", "snmptrap.vmwareAlertMessage": "symptomSet: ff617352-e842-4fe1-9b11-048fae5ef278\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: Lost connection to NFS server\nactive: true\nobj.1.name: iictliesxs101.msnet.railb.be\nobj.1.id: 50e0b28a-d90a-43e7-9c29-23410e6e2a74\nobj.1.metric: \nobj.1.info: Lost connection to NFS server\n", "snmptrap.vmwareAlertID": "f5a6c48c-9eac-41b8-8679-71c75f3aa44b", "snmptrap.vmwareAlertURL": "https://************/ui/index.action#environment/object-browser/hierarchy/50e0b28a-d90a-43e7-9c29-23410e6e2a74/alerts-and-symptoms/alerts/f5a6c48c-9eac-41b8-8679-71c75f3aa44b", "snmptrap.vmwareAlertRisk": "warning", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-09T19:18:33.153853315Z", "message": "#<SNMP::SNMPv2_Trap:0x21bcdb18 @varbind_list=[#<SNMP::VarBind:0x19f76083 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x4fe71d90 @value=**********>>, #<SNMP::VarBind:0x8c47b6f @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.47]>, #<SNMP::VarBind:0x34ff968f @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x162e1beb @name=[*******.4.1.6876.********.2.0], @value=\"<PERSON>bethStore052_Hitachi\">, #<SNMP::VarBind:0x61d28f23 @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x4a668f75 @name=[*******.4.1.6876.********.4.0], @value=\"1720552688625\">, #<SNMP::VarBind:0x2e52358 @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x647281ac @name=[*******.4.1.6876.********.6.0], @value=\"Alert by id eeb47d4e-d85f-4a91-b256-5c1a6c585cfe is cancelled at Tue Jul 09 21:18:08 CEST 2024:\">, #<SNMP::VarBind:0x11aca196 @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/da5c58f6-2dc9-4d85-b28d-dd6a7c80871c/alerts-and-symptoms/alerts/eeb47d4e-d85f-4a91-b256-5c1a6c585cfe\">, #<SNMP::VarBind:0x6fcf89f1 @name=[*******.4.1.6876.********.8.0], @value=\"eeb47d4e-d85f-4a91-b256-5c1a6c585cfe\">, #<SNMP::VarBind:0x449b88b3 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: c0b34ce1-a6b8-454d-bd2d-871c6aa3bc3a\\nrelation: parent\\ntotalObjects: 4\\nviolatingObjects: 2\\nsymptom: Virtual machine disk write latency at Critical level\\nactive: true\\nobj.1.name: rts-nnv8t-worker-upm-stmmp\\nobj.1.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.1.info: HT above 56.666666666666664 > 30\\nobj.2.name: rts-nnv8t-worker-upm-594g2\\nobj.2.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.2.info: HT above 84.8 > 30\\nsymptom: Virtual machine disk write latency at Immediate level\\nactive: true\\nobj.1.name: rts-nnv8t-worker-upm-594g2\\nobj.1.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.1.info: HT above 84.8 > 25\\nobj.2.name: rts-nnv8t-worker-upm-stmmp\\nobj.2.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.2.info: HT above 56.666666666666664 > 25\\nsymptom: Virtual machine disk write latency at Warning level\\nactive: true\\nobj.1.name: rts-nnv8t-worker-upm-594g2\\nobj.1.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.1.info: HT above 84.8 > 15\\nobj.2.name: rts-nnv8t-worker-upm-stmmp\\nobj.2.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.2.info: HT above 42 > 15\\n\">, #<SNMP::VarBind:0x34941e6d @name=[*******.4.1.6876.********.10.0], @value=\"Storage\">, #<SNMP::VarBind:0x40c0a978 @name=[*******.4.1.6876.********.11.0], @value=\"Performance\">, #<SNMP::VarBind:0x53d33090 @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x4b95f449 @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x5f66fc20 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x1a2815dd @name=[*******.4.1.6876.********.15.0], @value=\"Virtual Disk:Aggregate of all instances|Write Latency\">, #<SNMP::VarBind:0x50b5e1b8 @name=[*******.4.1.6876.********.16.0], @value=\"Datastore\">, #<SNMP::VarBind:0x34e6605b @name=[*******.4.1.6876.********.17.0], @value=\"More than half of the virtual machines served by this datastore are facing disk write latency\">, #<SNMP::VarBind:0x5c091d1b @name=[*******.4.1.6876.********.18.0], @value=\"More than half of the virtual machines served by this datastore are facing disk write latency\">, #<SNMP::VarBind:0x6556dba @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x7912e4e9 @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x7ef1b728 @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x6a049c28 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x4711146f @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "6d8fcb3d-eac1-40d7-a80b-547661887aa9", "event.kafka.offset": 4700, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-09T19:18:33.255Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.vmwareAlertEntityName": "LiesbethStore052_Hitachi", "snmptrap.vmwareAlertTimestamp": "1720552688625", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "Virtual Disk:Aggregate of all instances|Write Latency", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.47", "snmptrap.vmwareAlertDefinitionName": "More than half of the virtual machines served by this datastore are facing disk write latency", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "More than half of the virtual machines served by this datastore are facing disk write latency", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "Alert by id eeb47d4e-d85f-4a91-b256-5c1a6c585cfe is cancelled at Tue Jul 09 21:18:08 CEST 2024:", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "296 days, 23:12:31.49", "snmptrap.vmwareAlertType": "Storage", "snmptrap.vmwareAlertSubtype": "Performance", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "Datastore", "snmptrap.vmwareAlertMessage": "symptomSet: c0b34ce1-a6b8-454d-bd2d-871c6aa3bc3a\nrelation: parent\ntotalObjects: 4\nviolatingObjects: 2\nsymptom: Virtual machine disk write latency at Critical level\nactive: true\nobj.1.name: rts-nnv8t-worker-upm-stmmp\nobj.1.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.1.info: HT above 56.666666666666664 > 30\nobj.2.name: rts-nnv8t-worker-upm-594g2\nobj.2.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.2.info: HT above 84.8 > 30\nsymptom: Virtual machine disk write latency at Immediate level\nactive: true\nobj.1.name: rts-nnv8t-worker-upm-594g2\nobj.1.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.1.info: HT above 84.8 > 25\nobj.2.name: rts-nnv8t-worker-upm-stmmp\nobj.2.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.2.info: HT above 56.666666666666664 > 25\nsymptom: Virtual machine disk write latency at Warning level\nactive: true\nobj.1.name: rts-nnv8t-worker-upm-594g2\nobj.1.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.1.info: HT above 84.8 > 15\nobj.2.name: rts-nnv8t-worker-upm-stmmp\nobj.2.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.2.info: HT above 42 > 15\n", "snmptrap.vmwareAlertID": "eeb47d4e-d85f-4a91-b256-5c1a6c585cfe", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/da5c58f6-2dc9-4d85-b28d-dd6a7c80871c/alerts-and-symptoms/alerts/eeb47d4e-d85f-4a91-b256-5c1a6c585cfe", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-09T19:03:25.025080717Z", "message": "#<SNMP::SNMPv2_Trap:0x49f9d960 @varbind_list=[#<SNMP::VarBind:0x7f0a66a0 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x5a5dca21 @value=**********>>, #<SNMP::VarBind:0x5de82ad3 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x41d3db6c @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x6d409a90 @name=[*******.4.1.6876.********.2.0], @value=\"<PERSON>bethStore052_Hitachi\">, #<SNMP::VarBind:0x59f859db @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x45da4e85 @name=[*******.4.1.6876.********.4.0], @value=\"1720551785936\">, #<SNMP::VarBind:0x1a8df5e9 @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x73707832 @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id eeb47d4e-d85f-4a91-b256-5c1a6c585cfe is generated at Tue Jul 09 21:03:05 CEST 2024;\">, #<SNMP::VarBind:0x685dc106 @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/da5c58f6-2dc9-4d85-b28d-dd6a7c80871c/alerts-and-symptoms/alerts/eeb47d4e-d85f-4a91-b256-5c1a6c585cfe\">, #<SNMP::VarBind:0x7f6404ee @name=[*******.4.1.6876.********.8.0], @value=\"eeb47d4e-d85f-4a91-b256-5c1a6c585cfe\">, #<SNMP::VarBind:0xa338456 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: c0b34ce1-a6b8-454d-bd2d-871c6aa3bc3a\\nrelation: parent\\ntotalObjects: 4\\nviolatingObjects: 2\\nsymptom: Virtual machine disk write latency at Critical level\\nactive: true\\nobj.1.name: rts-nnv8t-worker-upm-stmmp\\nobj.1.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.1.info: HT above 56.666666666666664 > 30\\nobj.2.name: rts-nnv8t-worker-upm-594g2\\nobj.2.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.2.info: HT above 84.8 > 30\\nsymptom: Virtual machine disk write latency at Immediate level\\nactive: true\\nobj.1.name: rts-nnv8t-worker-upm-stmmp\\nobj.1.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.1.info: HT above 42 > 25\\nobj.2.name: rts-nnv8t-worker-upm-594g2\\nobj.2.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.2.info: HT above 84.8 > 25\\nsymptom: Virtual machine disk write latency at Warning level\\nactive: true\\nobj.1.name: rts-nnv8t-worker-upm-594g2\\nobj.1.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.1.info: HT above 84.8 > 15\\nobj.2.name: rts-nnv8t-worker-upm-stmmp\\nobj.2.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\\nobj.2.info: HT above 42 > 15\\n\">, #<SNMP::VarBind:0x7a72ccca @name=[*******.4.1.6876.********.10.0], @value=\"Storage\">, #<SNMP::VarBind:0x7ca16773 @name=[*******.4.1.6876.********.11.0], @value=\"Performance\">, #<SNMP::VarBind:0x233d4f7d @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x353a6e78 @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x59c2e089 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x2eaf9e3d @name=[*******.4.1.6876.********.15.0], @value=\"Virtual Disk:Aggregate of all instances|Write Latency\">, #<SNMP::VarBind:0x67207a32 @name=[*******.4.1.6876.********.16.0], @value=\"Datastore\">, #<SNMP::VarBind:0x18f8100 @name=[*******.4.1.6876.********.17.0], @value=\"More than half of the virtual machines served by this datastore are facing disk write latency\">, #<SNMP::VarBind:0x4f87ef10 @name=[*******.4.1.6876.********.18.0], @value=\"More than half of the virtual machines served by this datastore are facing disk write latency\">, #<SNMP::VarBind:0x1d8bdeab @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x799ed6c2 @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x464df70 @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x746b4f83 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x37d6a923 @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "18ac46fc-4734-45e0-957a-82958285f9f0", "event.kafka.offset": 4646, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-09T19:03:25.126Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.vmwareAlertEntityName": "LiesbethStore052_Hitachi", "snmptrap.vmwareAlertTimestamp": "1720551785936", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "Virtual Disk:Aggregate of all instances|Write Latency", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "More than half of the virtual machines served by this datastore are facing disk write latency", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "More than half of the virtual machines served by this datastore are facing disk write latency", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id eeb47d4e-d85f-4a91-b256-5c1a6c585cfe is generated at Tue Jul 09 21:03:05 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "296 days, 20:41:10.20", "snmptrap.vmwareAlertType": "Storage", "snmptrap.vmwareAlertSubtype": "Performance", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "Datastore", "snmptrap.vmwareAlertMessage": "symptomSet: c0b34ce1-a6b8-454d-bd2d-871c6aa3bc3a\nrelation: parent\ntotalObjects: 4\nviolatingObjects: 2\nsymptom: Virtual machine disk write latency at Critical level\nactive: true\nobj.1.name: rts-nnv8t-worker-upm-stmmp\nobj.1.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.1.info: HT above 56.666666666666664 > 30\nobj.2.name: rts-nnv8t-worker-upm-594g2\nobj.2.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.2.info: HT above 84.8 > 30\nsymptom: Virtual machine disk write latency at Immediate level\nactive: true\nobj.1.name: rts-nnv8t-worker-upm-stmmp\nobj.1.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.1.info: HT above 42 > 25\nobj.2.name: rts-nnv8t-worker-upm-594g2\nobj.2.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.2.info: HT above 84.8 > 25\nsymptom: Virtual machine disk write latency at Warning level\nactive: true\nobj.1.name: rts-nnv8t-worker-upm-594g2\nobj.1.id: aa52fcdf-22d4-4e6c-96fc-7dbdb43f2409\nobj.1.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.1.info: HT above 84.8 > 15\nobj.2.name: rts-nnv8t-worker-upm-stmmp\nobj.2.id: 98479efe-b629-440f-98d2-aeb9f9ab6a97\nobj.2.metric: virtualDisk:Aggregate of all instances|totalWriteLatency_average\nobj.2.info: HT above 42 > 15\n", "snmptrap.vmwareAlertID": "eeb47d4e-d85f-4a91-b256-5c1a6c585cfe", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/da5c58f6-2dc9-4d85-b28d-dd6a7c80871c/alerts-and-symptoms/alerts/eeb47d4e-d85f-4a91-b256-5c1a6c585cfe", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-14T17:45:26.420864750Z", "message": "#<SNMP::SNMPv2_Trap:0x73c0fc07 @varbind_list=[#<SNMP::VarBind:0x32fdf63c @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x77d1bc11 @value=**********>>, #<SNMP::VarBind:0x3854628 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0xf4352b1 @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x52d20973 @name=[*******.4.1.6876.********.2.0], @value=\"iictziesxs557.msnet.railb.be\">, #<SNMP::VarBind:0x2899160c @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x184b131 @name=[*******.4.1.6876.********.4.0], @value=\"1720979110838\">, #<SNMP::VarBind:0x1447bd6a @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x569cb700 @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id 83f9c173-5f10-4cbd-84d9-404d4466d6c6 is generated at Sun Jul 14 19:45:10 CEST 2024;\">, #<SNMP::VarBind:0x36d5e2a9 @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/66e7fe71-b071-4629-a4ec-f71601ec61b7/alerts-and-symptoms/alerts/83f9c173-5f10-4cbd-84d9-404d4466d6c6\">, #<SNMP::VarBind:0x12fcd07b @name=[*******.4.1.6876.********.8.0], @value=\"83f9c173-5f10-4cbd-84d9-404d4466d6c6\">, #<SNMP::VarBind:0x3b7993a3 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: 23bc686f-1a8e-4b4e-a119-d6ba5b00b4bd\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: vSphere HA detected a host failure\\nactive: true\\nobj.1.name: iictziesxs557.msnet.railb.be\\nobj.1.id: 66e7fe71-b071-4629-a4ec-f71601ec61b7\\nobj.1.metric: \\nobj.1.info: vSphere HA detected a possible host failure of host iictziesxs557.msnet.railb.be in cluster Liesje in datacenter Muizen\\n\">, #<SNMP::VarBind:0x8ee077b @name=[*******.4.1.6876.********.10.0], @value=\"Virtualization/Hypervisor\">, #<SNMP::VarBind:0x11b77c65 @name=[*******.4.1.6876.********.11.0], @value=\"Availability\">, #<SNMP::VarBind:0x653c0920 @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x122f7aaf @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x6a02724e @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x40fc6f3f @name=[*******.4.1.6876.********.15.0], @value=\"\">, #<SNMP::VarBind:0x458c1b09 @name=[*******.4.1.6876.********.16.0], @value=\"HostSystem\">, #<SNMP::VarBind:0x2c1feb15 @name=[*******.4.1.6876.********.17.0], @value=\"vSphere High Availability (HA) has detected a possible host failure\">, #<SNMP::VarBind:0x55767cda @name=[*******.4.1.6876.********.18.0], @value=\"A vSphere HA master agent considers a host to have failed if it loses contact with the vSphere HA agent on the host, the host does not respond to pings on any of the management interfaces, and the master does not observe any datastore heartbeats. This problem can occur when a computer on the network is configured to have the same IP address as one of the ESX/ESXi hosts in a HA cluster. In this situation, the HA agent receives invalid data and generates errors. The HA agent does not function properly until it is reconfigured. The frequency of this problem depends on how often the IP address conflict occurs.\">, #<SNMP::VarBind:0x765074dc @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x53217d4c @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0xb5737e @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x66435f1e @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x1340219e @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "5713f38e-3420-4bef-b5e6-a9487e914043", "event.kafka.offset": 4701, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-14T17:45:26.522Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.vmwareAlertEntityName": "iictziesxs557.msnet.railb.be", "snmptrap.vmwareAlertTimestamp": "1720979110838", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "vSphere High Availability (HA) has detected a possible host failure", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "A vSphere HA master agent considers a host to have failed if it loses contact with the vSphere HA agent on the host, the host does not respond to pings on any of the management interfaces, and the master does not observe any datastore heartbeats. This problem can occur when a computer on the network is configured to have the same IP address as one of the ESX/ESXi hosts in a HA cluster. In this situation, the HA agent receives invalid data and generates errors. The HA agent does not function properly until it is reconfigured. The frequency of this problem depends on how often the IP address conflict occurs.", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id 83f9c173-5f10-4cbd-84d9-404d4466d6c6 is generated at Sun Jul 14 19:45:10 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "346 days, 07:41:24.16", "snmptrap.vmwareAlertType": "Virtualization/Hypervisor", "snmptrap.vmwareAlertSubtype": "Availability", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "HostSystem", "snmptrap.vmwareAlertMessage": "symptomSet: 23bc686f-1a8e-4b4e-a119-d6ba5b00b4bd\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: vSphere HA detected a host failure\nactive: true\nobj.1.name: iictziesxs557.msnet.railb.be\nobj.1.id: 66e7fe71-b071-4629-a4ec-f71601ec61b7\nobj.1.metric: \nobj.1.info: vSphere HA detected a possible host failure of host iictziesxs557.msnet.railb.be in cluster Liesje in datacenter Muizen\n", "snmptrap.vmwareAlertID": "83f9c173-5f10-4cbd-84d9-404d4466d6c6", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/66e7fe71-b071-4629-a4ec-f71601ec61b7/alerts-and-symptoms/alerts/83f9c173-5f10-4cbd-84d9-404d4466d6c6", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-14T03:09:22.519808262Z", "message": "#<SNMP::SNMPv2_Trap:0x3de7d89e @varbind_list=[#<SNMP::VarBind:0x7cdad2a5 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x4ecc19d4 @value=**********>>, #<SNMP::VarBind:0x4dec0bbb @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x36109e77 @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv019.msnet.railb.be\">, #<SNMP::VarBind:0x1cf52dea @name=[*******.4.1.6876.********.2.0], @value=\"Louisa\">, #<SNMP::VarBind:0x32598519 @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x1f45e13a @name=[*******.4.1.6876.********.4.0], @value=\"1720926558099\">, #<SNMP::VarBind:0x2d143783 @name=[*******.4.1.6876.********.5.0], @value=\"warning\">, #<SNMP::VarBind:0x18bbd5b6 @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id a1e76deb-15d7-42be-a369-2146e6abae6b is generated at Sun Jul 14 05:09:18 CEST 2024;\">, #<SNMP::VarBind:0x6634eaf5 @name=[*******.4.1.6876.********.7.0], @value=\"https://************/ui/index.action#environment/object-browser/hierarchy/3264ef12-da89-4cf3-bfc8-1d8572b63c96/alerts-and-symptoms/alerts/a1e76deb-15d7-42be-a369-2146e6abae6b\">, #<SNMP::VarBind:0x56c88b38 @name=[*******.4.1.6876.********.8.0], @value=\"a1e76deb-15d7-42be-a369-2146e6abae6b\">, #<SNMP::VarBind:0x5aec064d @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: a32f3a6e-2495-4cf1-a816-eb978f41d71e\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: Cluster DRS settings are tunable to improve cluster workload balance\\nactive: true\\nobj.1.name: Louisa\\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\\nobj.1.metric: \\nobj.1.info: Property equals true = true\\nsymptom: At least one host in the cluster has critically high CPU \\\"demand\\\" workload\\nactive: true\\nobj.1.name: Louisa\\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\\nobj.1.metric: CPU|Demand|Highest Host Workload\\nobj.1.info: HT above 95.12033416835487 > 95\\nsymptom: SymptomDefinition-VMWARE-ClusterCPUDemandWorkloadWarning\\nactive: false\\nobj.1.name: Louisa\\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\\n\">, #<SNMP::VarBind:0x1c3c6ec3 @name=[*******.4.1.6876.********.10.0], @value=\"Virtualization/Hypervisor\">, #<SNMP::VarBind:0x5d1e4cf3 @name=[*******.4.1.6876.********.11.0], @value=\"Performance\">, #<SNMP::VarBind:0x4f9d9d23 @name=[*******.4.1.6876.********.12.0], @value=\"info\">, #<SNMP::VarBind:0x75f924f3 @name=[*******.4.1.6876.********.13.0], @value=\"warning\">, #<SNMP::VarBind:0xa4c06f9 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x259aaa97 @name=[*******.4.1.6876.********.15.0], @value=\"CPU|Demand|Highest Host Workload\">, #<SNMP::VarBind:0x64d4f82 @name=[*******.4.1.6876.********.16.0], @value=\"ClusterComputeResource\">, #<SNMP::VarBind:0x3aaf2d04 @name=[*******.4.1.6876.********.17.0], @value=\"Cluster has unbalanced workload\">, #<SNMP::VarBind:0x50be9f66 @name=[*******.4.1.6876.********.18.0], @value=\"CPU or memory workload is spread unevenly among the hosts in the cluster\">, #<SNMP::VarBind:0x200049f4 @name=[*******.4.1.6876.********.19.0], @value=\"risk\">, #<SNMP::VarBind:0x4c5d2329 @name=[*******.4.1.6876.********.20.0], @value=\"SNMP Olympus Prd\">, #<SNMP::VarBind:0xfd9e0ab @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x2b380b99 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x955b8e3 @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=2195148, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "81acf1e8-e7be-43f2-b496-a6c3c582bc52", "event.kafka.offset": 847, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_monceau-events-prd", "event.kafka.topic": "a1232-vserver_monceau-events-prd", "event.kafka.timestamp": "2024-07-14T03:09:22.622Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.vmwareAlertEntityName": "<PERSON>", "snmptrap.vmwareAlertTimestamp": "1720926558099", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "CPU|Demand|Highest Host Workload", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "Cluster has unbalanced workload", "snmptrap.vmwareAlertCriticality": "warning", "snmptrap.vmwareAlertDefinitionDesc": "CPU or memory workload is spread unevenly among the hosts in the cluster", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id a1e76deb-15d7-42be-a369-2146e6abae6b is generated at Sun Jul 14 05:09:18 CEST 2024;", "snmptrap.vmwareAlertHealth": "info", "snmptrap.SNMPv2-MIB::sysUpTime.0": "340 days, 05:40:45.08", "snmptrap.vmwareAlertType": "Virtualization/Hypervisor", "snmptrap.vmwareAlertSubtype": "Performance", "snmptrap.vmwareAlertAliveServerName": "iictcismwv019.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "SNMP Olympus Prd", "snmptrap.vmwareAlertResourceKind": "ClusterComputeResource", "snmptrap.vmwareAlertMessage": "symptomSet: a32f3a6e-2495-4cf1-a816-eb978f41d71e\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: Cluster DRS settings are tunable to improve cluster workload balance\nactive: true\nobj.1.name: <PERSON>\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\nobj.1.metric: \nobj.1.info: Property equals true = true\nsymptom: At least one host in the cluster has critically high CPU \"demand\" workload\nactive: true\nobj.1.name: <PERSON>\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\nobj.1.metric: CPU|Demand|Highest Host Workload\nobj.1.info: HT above 95.12033416835487 > 95\nsymptom: SymptomDefinition-VMWARE-ClusterCPUDemandWorkloadWarning\nactive: false\nobj.1.name: <PERSON>\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\n", "snmptrap.vmwareAlertID": "a1e76deb-15d7-42be-a369-2146e6abae6b", "snmptrap.vmwareAlertURL": "https://************/ui/index.action#environment/object-browser/hierarchy/3264ef12-da89-4cf3-bfc8-1d8572b63c96/alerts-and-symptoms/alerts/a1e76deb-15d7-42be-a369-2146e6abae6b", "snmptrap.vmwareAlertRisk": "warning", "snmptrap.vmwareAlertImpact": "risk", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-16T05:45:01.737911750Z", "message": "#<SNMP::SNMPv2_Trap:0x4c3809eb @varbind_list=[#<SNMP::VarBind:0x52fc8d3e @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x656cc6f1 @value=3121783732>>, #<SNMP::VarBind:0x6997ece6 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x5b50b933 @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x6d735a1a @name=[*******.4.1.6876.********.2.0], @value=\"Linea\">, #<SNMP::VarBind:0x6e2b520b @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x388d63f4 @name=[*******.4.1.6876.********.4.0], @value=\"1721108696517\">, #<SNMP::VarBind:0x66caccf7 @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x111ee9be @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id ca918df8-6e5f-478d-9d50-ba22d5df2aa8 is generated at Tue Jul 16 07:44:56 CEST 2024;\">, #<SNMP::VarBind:0x25115a9c @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/f3eecef6-8e03-4416-989b-ed4e81b9cedc/alerts-and-symptoms/alerts/ca918df8-6e5f-478d-9d50-ba22d5df2aa8\">, #<SNMP::VarBind:0x76501f09 @name=[*******.4.1.6876.********.8.0], @value=\"ca918df8-6e5f-478d-9d50-ba22d5df2aa8\">, #<SNMP::VarBind:0x46cdc840 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: dbe22866-4549-42c3-b64b-194dbe7952fc\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: vCenter Server is unable to find a master vSphere HA agent\\nactive: true\\nobj.1.name: Linea\\nobj.1.id: f3eecef6-8e03-4416-989b-ed4e81b9cedc\\nobj.1.metric: \\nobj.1.info: vCenter Server is unable to find a master vSphere HA agent in cluster Linea in VMWare\\n\">, #<SNMP::VarBind:0x20788a3e @name=[*******.4.1.6876.********.10.0], @value=\"Hardware (OSI)\">, #<SNMP::VarBind:0xf92e117 @name=[*******.4.1.6876.********.11.0], @value=\"Availability\">, #<SNMP::VarBind:0x79cc0fbb @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x516b470a @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x35261e63 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x2da4db89 @name=[*******.4.1.6876.********.15.0], @value=\"\">, #<SNMP::VarBind:0x340cb851 @name=[*******.4.1.6876.********.16.0], @value=\"ClusterComputeResource\">, #<SNMP::VarBind:0x3f54facd @name=[*******.4.1.6876.********.17.0], @value=\"vSphere HA master missing\">, #<SNMP::VarBind:0x3a842e42 @name=[*******.4.1.6876.********.18.0], @value=\"vSphere HA master missing\">, #<SNMP::VarBind:0x31f7c208 @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x60683a57 @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x78722605 @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x639ff177 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x6cf7888e @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "edd48291-36a3-4429-ac05-2c3dc8673c35", "event.kafka.offset": 4703, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-16T05:45:01.839Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.vmwareAlertEntityName": "Linea", "snmptrap.vmwareAlertTimestamp": "1721108696517", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "vSphere HA master missing", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "vSphere HA master missing", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id ca918df8-6e5f-478d-9d50-ba22d5df2aa8 is generated at Tue Jul 16 07:44:56 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "361 days, 07:37:17.32", "snmptrap.vmwareAlertType": "Hardware (OSI)", "snmptrap.vmwareAlertSubtype": "Availability", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "ClusterComputeResource", "snmptrap.vmwareAlertMessage": "symptomSet: dbe22866-4549-42c3-b64b-194dbe7952fc\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: vCenter Server is unable to find a master vSphere HA agent\nactive: true\nobj.1.name: Linea\nobj.1.id: f3eecef6-8e03-4416-989b-ed4e81b9cedc\nobj.1.metric: \nobj.1.info: vCenter Server is unable to find a master vSphere HA agent in cluster Linea in VMWare\n", "snmptrap.vmwareAlertID": "ca918df8-6e5f-478d-9d50-ba22d5df2aa8", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/f3eecef6-8e03-4416-989b-ed4e81b9cedc/alerts-and-symptoms/alerts/ca918df8-6e5f-478d-9d50-ba22d5df2aa8", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-16T06:14:54.423549180Z", "message": "#<SNMP::SNMPv2_Trap:0x60d65120 @varbind_list=[#<SNMP::VarBind:0xecfc756 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x5a6565e5 @value=**********>>, #<SNMP::VarBind:0x8e860d8 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x1e23bd5f @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x6e262cba @name=[*******.4.1.6876.********.2.0], @value=\"itmsbiesxs261.msnet.railb.be\">, #<SNMP::VarBind:0x1f1d26d6 @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x54386b4d @name=[*******.4.1.6876.********.4.0], @value=\"1721110494076\">, #<SNMP::VarBind:0x5772fdd0 @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x86e4878 @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id fb76116a-4dd0-4f81-9222-21f8f259a811 is generated at Tue Jul 16 08:14:54 CEST 2024;\">, #<SNMP::VarBind:0x7eb3f435 @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/fb76116a-4dd0-4f81-9222-21f8f259a811\">, #<SNMP::VarBind:0x69c68aad @name=[*******.4.1.6876.********.8.0], @value=\"fb76116a-4dd0-4f81-9222-21f8f259a811\">, #<SNMP::VarBind:0x299a7638 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: e6a56d2c-bb77-4e65-8064-205f14445a8c\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: Host disconnected from vCenter\\nactive: true\\nobj.1.name: itmsbiesxs261.msnet.railb.be\\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\\nobj.1.metric: \\nobj.1.info: Property not equal notResponding != connected\\nsymptom: SymptomDefinition-VMWARE-HostIsPlacedInStandbyMode\\nactive: false\\nobj.1.name: itmsbiesxs261.msnet.railb.be\\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\\n\">, #<SNMP::VarBind:0x53cdd6fb @name=[*******.4.1.6876.********.10.0], @value=\"Virtualization/Hypervisor\">, #<SNMP::VarBind:0x32be92eb @name=[*******.4.1.6876.********.11.0], @value=\"Availability\">, #<SNMP::VarBind:0x6ff0e5f3 @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x15216583 @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x286613d2 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x6e89f5ff @name=[*******.4.1.6876.********.15.0], @value=\"\">, #<SNMP::VarBind:0x63e4aa0 @name=[*******.4.1.6876.********.16.0], @value=\"HostSystem\">, #<SNMP::VarBind:0x182d2a99 @name=[*******.4.1.6876.********.17.0], @value=\"Host has lost connection to vCenter Server\">, #<SNMP::VarBind:0xcd42cd @name=[*******.4.1.6876.********.18.0], @value=\"The host has been unexpectedly disconnected from vCenter Server. The virtual machines on the host have also lost connectivity to vCenter Server. The host might not have a problem and the virtual machines on the host might be running as expected, but any configuration, data, and events from the host and its virtual machines are lost.\">, #<SNMP::VarBind:0x214a7926 @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x55555ce5 @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x24ddcd34 @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x4eec2848 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x3c1debfa @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "22d6b74e-c717-413a-9cef-32b5ea45a440", "event.kafka.offset": 4704, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-16T06:14:54.525Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.vmwareAlertEntityName": "itmsbiesxs261.msnet.railb.be", "snmptrap.vmwareAlertTimestamp": "1721110494076", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "Host has lost connection to vCenter Server", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "The host has been unexpectedly disconnected from vCenter Server. The virtual machines on the host have also lost connectivity to vCenter Server. The host might not have a problem and the virtual machines on the host might be running as expected, but any configuration, data, and events from the host and its virtual machines are lost.", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id fb76116a-4dd0-4f81-9222-21f8f259a811 is generated at Tue Jul 16 08:14:54 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "361 days, 12:36:04.18", "snmptrap.vmwareAlertType": "Virtualization/Hypervisor", "snmptrap.vmwareAlertSubtype": "Availability", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "HostSystem", "snmptrap.vmwareAlertMessage": "symptomSet: e6a56d2c-bb77-4e65-8064-205f14445a8c\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: Host disconnected from vCenter\nactive: true\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\nobj.1.metric: \nobj.1.info: Property not equal notResponding != connected\nsymptom: SymptomDefinition-VMWARE-HostIsPlacedInStandbyMode\nactive: false\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\n", "snmptrap.vmwareAlertID": "fb76116a-4dd0-4f81-9222-21f8f259a811", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/fb76116a-4dd0-4f81-9222-21f8f259a811", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {"s2110_alarm": [{"ci_id": "itmsbiesxs261", "metric_name": "Host2vCenter", "metric_type": "/NetworkConnectivity/", "event_id": "fb76116a-4dd0-4f81-9222-21f8f259a811"}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "itmsbiesxs261", "metric_name": "Host2vCenter", "metric_type": "/NetworkConnectivity/", "additional_data": {"collection": "iictcismwv009.msnet.railb.be", "delay": 900, "alert_message": "symptomSet: e6a56d2c-bb77-4e65-8064-205f14445a8c\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: Host disconnected from vCenter\nactive: true\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\nobj.1.metric: \nobj.1.info: Property not equal notResponding != connected\nsymptom: SymptomDefinition-VMWARE-HostIsPlacedInStandbyMode\nactive: false\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\n"}, "raise_time": "2024-07-16 06:14:54", "clear_time": null, "severity": 5, "summary": "The host has been unexpectedly disconnected from vCenter Server. The virtual machines on the host have also lost connectivity to vCenter Server. The host might not have a problem and the virtual machines on the host might be running as expected, but any configuration, data, and events from the host and its virtual machines are lost."}]}}, {"input": {"@version": "1", "@timestamp": "2024-07-16T08:25:16.639730226Z", "message": "#<SNMP::SNMPv2_Trap:0x62da18c6 @varbind_list=[#<SNMP::VarBind:0x27383988 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0xbaf8cd5 @value=3131398635>>, #<SNMP::VarBind:0x3901a94e @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x7a33521a @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x1ca1d0ad @name=[*******.4.1.6876.********.2.0], @value=\"itmsbiesxs261.msnet.railb.be\">, #<SNMP::VarBind:0x6b9f3ec8 @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x5fe0b12c @name=[*******.4.1.6876.********.4.0], @value=\"1721118316370\">, #<SNMP::VarBind:0x3fa38b48 @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x2d9510a9 @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id d89b5209-6c24-44e1-931b-e9c4b3a2b325 is generated at Tue Jul 16 10:25:16 CEST 2024;\">, #<SNMP::VarBind:0x3fee02ad @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/d89b5209-6c24-44e1-931b-e9c4b3a2b325\">, #<SNMP::VarBind:0x655dbdad @name=[*******.4.1.6876.********.8.0], @value=\"d89b5209-6c24-44e1-931b-e9c4b3a2b325\">, #<SNMP::VarBind:0x46879385 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: d108a80b-af24-4875-8f98-94341ef2e4ba\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: vSphere HA detected a network isolated host\\nactive: true\\nobj.1.name: itmsbiesxs261.msnet.railb.be\\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\\nobj.1.metric: \\nobj.1.info: vSphere HA detected that host itmsbiesxs261.msnet.railb.be is isolated from cluster Linea in VMWare\\n\">, #<SNMP::VarBind:0x4312c3a4 @name=[*******.4.1.6876.********.10.0], @value=\"Virtualization/Hypervisor\">, #<SNMP::VarBind:0xca65a01 @name=[*******.4.1.6876.********.11.0], @value=\"Availability\">, #<SNMP::VarBind:0x41e3eed @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x7eff9e50 @name=[*******.4.1.6876.********.13.0], @value=\"\">, #<SNMP::VarBind:0x2b256dc8 @name=[*******.4.1.6876.********.14.0], @value=\"\">, #<SNMP::VarBind:0x156d4cff @name=[*******.4.1.6876.********.15.0], @value=\"\">, #<SNMP::VarBind:0x6071f1e9 @name=[*******.4.1.6876.********.16.0], @value=\"HostSystem\">, #<SNMP::VarBind:0x627efea9 @name=[*******.4.1.6876.********.17.0], @value=\"vSphere High Availability (HA) has detected a network-isolated host\">, #<SNMP::VarBind:0x62358403 @name=[*******.4.1.6876.********.18.0], @value=\"vSphere HA has detected that the host is network-isolated and has powered off or shut down virtual machines running on the host. A host is network isolated if both of the following conditions are met:\\n- Isolation addresses are configured and the host is unable to ping the addresses.\\n- The vSphere HA agent on the host cannot access any of the agents that are running on the other cluster hosts.\\n\\nWhen a host is network-isolated, it continues to monitor the virtual machines that remain powered-on. Host network isolation affects how vSphere HA restarts virtual machines after a failure. vSphere HA powers off or shuts down a virtual machine only if the agent on the host determines that a master host is responsible for the virtual machine.\">, #<SNMP::VarBind:0x1d40260e @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x72d172f6 @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x674f51b3 @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x2914c4c5 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x35f8f7bf @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "3b37a19b-df74-4edc-beff-616b44e2896e", "event.kafka.offset": 4652, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-16T08:25:16.740Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.vmwareAlertEntityName": "itmsbiesxs261.msnet.railb.be", "snmptrap.vmwareAlertTimestamp": "1721118316370", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "", "snmptrap.vmwareAlertEfficiency": "", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "vSphere High Availability (HA) has detected a network-isolated host", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "vSphere HA has detected that the host is network-isolated and has powered off or shut down virtual machines running on the host. A host is network isolated if both of the following conditions are met:\n- Isolation addresses are configured and the host is unable to ping the addresses.\n- The vSphere HA agent on the host cannot access any of the agents that are running on the other cluster hosts.\n\nWhen a host is network-isolated, it continues to monitor the virtual machines that remain powered-on. Host network isolation affects how vSphere HA restarts virtual machines after a failure. vSphere HA powers off or shuts down a virtual machine only if the agent on the host determines that a master host is responsible for the virtual machine.", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id d89b5209-6c24-44e1-931b-e9c4b3a2b325 is generated at Tue Jul 16 10:25:16 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "362 days, 10:19:46.35", "snmptrap.vmwareAlertType": "Virtualization/Hypervisor", "snmptrap.vmwareAlertSubtype": "Availability", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "HostSystem", "snmptrap.vmwareAlertMessage": "symptomSet: d108a80b-af24-4875-8f98-94341ef2e4ba\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: vSphere HA detected a network isolated host\nactive: true\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\nobj.1.metric: \nobj.1.info: vSphere HA detected that host itmsbiesxs261.msnet.railb.be is isolated from cluster Linea in VMWare\n", "snmptrap.vmwareAlertID": "d89b5209-6c24-44e1-931b-e9c4b3a2b325", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/d89b5209-6c24-44e1-931b-e9c4b3a2b325", "snmptrap.vmwareAlertRisk": "", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-16T06:29:57.030601576Z", "message": "#<SNMP::SNMPv2_Trap:0x5629b407 @varbind_list=[#<SNMP::VarBind:0x24f3b130 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x65daf435 @value=**********>>, #<SNMP::VarBind:0x7ec8fd05 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.48]>, #<SNMP::VarBind:0x4007ce3 @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x3dc33762 @name=[*******.4.1.6876.********.2.0], @value=\"itmsbiesxs261.msnet.railb.be\">, #<SNMP::VarBind:0xf0a07b3 @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x3693f382 @name=[*******.4.1.6876.********.4.0], @value=\"1721111396768\">, #<SNMP::VarBind:0x317366d7 @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x15af2fbc @name=[*******.4.1.6876.********.6.0], @value=\"Alert by id dd4626b0-ba86-4a37-b5e4-68ed2a5555b6 is updated at Tue Jul 16 08:29:56 CEST 2024;\">, #<SNMP::VarBind:0x4ac78525 @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/dd4626b0-ba86-4a37-b5e4-68ed2a5555b6\">, #<SNMP::VarBind:0x63e1601c @name=[*******.4.1.6876.********.8.0], @value=\"dd4626b0-ba86-4a37-b5e4-68ed2a5555b6\">, #<SNMP::VarBind:0x498ef769 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: d108a80b-af24-4875-8f98-94341ef2e4ba\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: vSphere HA detected a network isolated host\\nactive: true\\nobj.1.name: itmsbiesxs261.msnet.railb.be\\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\\nobj.1.metric: \\nobj.1.info: vSphere HA detected that host itmsbiesxs261.msnet.railb.be is isolated from cluster Linea in VMWare\\n\">, #<SNMP::VarBind:0x2530e32b @name=[*******.4.1.6876.********.10.0], @value=\"Virtualization/Hypervisor\">, #<SNMP::VarBind:0x53a8a405 @name=[*******.4.1.6876.********.11.0], @value=\"Availability\">, #<SNMP::VarBind:0x3b21cfec @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x52a425e @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x27a1d06b @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x313fc016 @name=[*******.4.1.6876.********.15.0], @value=\"\">, #<SNMP::VarBind:0x4542772 @name=[*******.4.1.6876.********.16.0], @value=\"HostSystem\">, #<SNMP::VarBind:0x533bbd14 @name=[*******.4.1.6876.********.17.0], @value=\"vSphere High Availability (HA) has detected a network-isolated host\">, #<SNMP::VarBind:0x7ab00d90 @name=[*******.4.1.6876.********.18.0], @value=\"vSphere HA has detected that the host is network-isolated and has powered off or shut down virtual machines running on the host. A host is network isolated if both of the following conditions are met:\\n- Isolation addresses are configured and the host is unable to ping the addresses.\\n- The vSphere HA agent on the host cannot access any of the agents that are running on the other cluster hosts.\\n\\nWhen a host is network-isolated, it continues to monitor the virtual machines that remain powered-on. Host network isolation affects how vSphere HA restarts virtual machines after a failure. vSphere HA powers off or shuts down a virtual machine only if the agent on the host determines that a master host is responsible for the virtual machine.\">, #<SNMP::VarBind:0x35c04da0 @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x108a93dd @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x1e575fc5 @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x64710d12 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x53ebf171 @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "c1940aeb-1a21-4c87-801d-a3604fcd3000", "event.kafka.offset": 4648, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-16T06:29:57.132Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.vmwareAlertEntityName": "itmsbiesxs261.msnet.railb.be", "snmptrap.vmwareAlertTimestamp": "1721111396768", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.48", "snmptrap.vmwareAlertDefinitionName": "vSphere High Availability (HA) has detected a network-isolated host", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "vSphere HA has detected that the host is network-isolated and has powered off or shut down virtual machines running on the host. A host is network isolated if both of the following conditions are met:\n- Isolation addresses are configured and the host is unable to ping the addresses.\n- The vSphere HA agent on the host cannot access any of the agents that are running on the other cluster hosts.\n\nWhen a host is network-isolated, it continues to monitor the virtual machines that remain powered-on. Host network isolation affects how vSphere HA restarts virtual machines after a failure. vSphere HA powers off or shuts down a virtual machine only if the agent on the host determines that a master host is responsible for the virtual machine.", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "Alert by id dd4626b0-ba86-4a37-b5e4-68ed2a5555b6 is updated at Tue Jul 16 08:29:56 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "361 days, 15:06:30.24", "snmptrap.vmwareAlertType": "Virtualization/Hypervisor", "snmptrap.vmwareAlertSubtype": "Availability", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "HostSystem", "snmptrap.vmwareAlertMessage": "symptomSet: d108a80b-af24-4875-8f98-94341ef2e4ba\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: vSphere HA detected a network isolated host\nactive: true\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\nobj.1.metric: \nobj.1.info: vSphere HA detected that host itmsbiesxs261.msnet.railb.be is isolated from cluster Linea in VMWare\n", "snmptrap.vmwareAlertID": "dd4626b0-ba86-4a37-b5e4-68ed2a5555b6", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/dd4626b0-ba86-4a37-b5e4-68ed2a5555b6", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-16T06:24:55.083803330Z", "message": "#<SNMP::SNMPv2_Trap:0x9aeebdb @varbind_list=[#<SNMP::VarBind:0x75be51ca @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x37fb32ff @value=**********>>, #<SNMP::VarBind:0x49f5f7e8 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x16e36b54 @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x11545843 @name=[*******.4.1.6876.********.2.0], @value=\"itmsbiesxs261.msnet.railb.be\">, #<SNMP::VarBind:0x7fd6ec5c @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x19fa1ef9 @name=[*******.4.1.6876.********.4.0], @value=\"1721111094795\">, #<SNMP::VarBind:0x144e6f59 @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x27ebbbd3 @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id dd4626b0-ba86-4a37-b5e4-68ed2a5555b6 is generated at Tue Jul 16 08:24:54 CEST 2024;\">, #<SNMP::VarBind:0x6f97077e @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/dd4626b0-ba86-4a37-b5e4-68ed2a5555b6\">, #<SNMP::VarBind:0x2653c1a3 @name=[*******.4.1.6876.********.8.0], @value=\"dd4626b0-ba86-4a37-b5e4-68ed2a5555b6\">, #<SNMP::VarBind:0x41005697 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: d108a80b-af24-4875-8f98-94341ef2e4ba\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: vSphere HA detected a network isolated host\\nactive: true\\nobj.1.name: itmsbiesxs261.msnet.railb.be\\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\\nobj.1.metric: \\nobj.1.info: vSphere HA detected that host itmsbiesxs261.msnet.railb.be is isolated from cluster Linea in VMWare\\n\">, #<SNMP::VarBind:0x3b4f4847 @name=[*******.4.1.6876.********.10.0], @value=\"Virtualization/Hypervisor\">, #<SNMP::VarBind:0x6cb414a7 @name=[*******.4.1.6876.********.11.0], @value=\"Availability\">, #<SNMP::VarBind:0x54529e4b @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x17a5deb8 @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x56e9b17b @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x1a34962d @name=[*******.4.1.6876.********.15.0], @value=\"\">, #<SNMP::VarBind:0x5ad4f3e6 @name=[*******.4.1.6876.********.16.0], @value=\"HostSystem\">, #<SNMP::VarBind:0x633db61a @name=[*******.4.1.6876.********.17.0], @value=\"vSphere High Availability (HA) has detected a network-isolated host\">, #<SNMP::VarBind:0x78c4a16f @name=[*******.4.1.6876.********.18.0], @value=\"vSphere HA has detected that the host is network-isolated and has powered off or shut down virtual machines running on the host. A host is network isolated if both of the following conditions are met:\\n- Isolation addresses are configured and the host is unable to ping the addresses.\\n- The vSphere HA agent on the host cannot access any of the agents that are running on the other cluster hosts.\\n\\nWhen a host is network-isolated, it continues to monitor the virtual machines that remain powered-on. Host network isolation affects how vSphere HA restarts virtual machines after a failure. vSphere HA powers off or shuts down a virtual machine only if the agent on the host determines that a master host is responsible for the virtual machine.\">, #<SNMP::VarBind:0x46ffd5a4 @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x3b49455e @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x35caa94b @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x6858b944 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x5032131e @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "36a5b92e-7505-43e6-9d2b-5e3c27d1fb3a", "event.kafka.offset": 4705, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-16T06:24:55.185Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.vmwareAlertEntityName": "itmsbiesxs261.msnet.railb.be", "snmptrap.vmwareAlertTimestamp": "1721111094795", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "vSphere High Availability (HA) has detected a network-isolated host", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "vSphere HA has detected that the host is network-isolated and has powered off or shut down virtual machines running on the host. A host is network isolated if both of the following conditions are met:\n- Isolation addresses are configured and the host is unable to ping the addresses.\n- The vSphere HA agent on the host cannot access any of the agents that are running on the other cluster hosts.\n\nWhen a host is network-isolated, it continues to monitor the virtual machines that remain powered-on. Host network isolation affects how vSphere HA restarts virtual machines after a failure. vSphere HA powers off or shuts down a virtual machine only if the agent on the host determines that a master host is responsible for the virtual machine.", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id dd4626b0-ba86-4a37-b5e4-68ed2a5555b6 is generated at Tue Jul 16 08:24:54 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "361 days, 14:16:10.77", "snmptrap.vmwareAlertType": "Virtualization/Hypervisor", "snmptrap.vmwareAlertSubtype": "Availability", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "HostSystem", "snmptrap.vmwareAlertMessage": "symptomSet: d108a80b-af24-4875-8f98-94341ef2e4ba\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: vSphere HA detected a network isolated host\nactive: true\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\nobj.1.metric: \nobj.1.info: vSphere HA detected that host itmsbiesxs261.msnet.railb.be is isolated from cluster Linea in VMWare\n", "snmptrap.vmwareAlertID": "dd4626b0-ba86-4a37-b5e4-68ed2a5555b6", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/dd4626b0-ba86-4a37-b5e4-68ed2a5555b6", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-11T03:04:26.471925295Z", "message": "#<SNMP::SNMPv2_Trap:0x3c03c25a @varbind_list=[#<SNMP::VarBind:0x3eb9bda8 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x1d350fb @value=**********>>, #<SNMP::VarBind:0x23604389 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x411f192d @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv019.msnet.railb.be\">, #<SNMP::VarBind:0x32b8b85b @name=[*******.4.1.6876.********.2.0], @value=\"Louisa\">, #<SNMP::VarBind:0x29f7727f @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x8aa10c3 @name=[*******.4.1.6876.********.4.0], @value=\"1720667062382\">, #<SNMP::VarBind:0x31c5a68a @name=[*******.4.1.6876.********.5.0], @value=\"warning\">, #<SNMP::VarBind:0x46c8ba89 @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id bfd7ac13-5f18-4169-bff8-03d28fd63472 is generated at Thu Jul 11 05:04:22 CEST 2024;\">, #<SNMP::VarBind:0x54be5ca5 @name=[*******.4.1.6876.********.7.0], @value=\"https://************/ui/index.action#environment/object-browser/hierarchy/3264ef12-da89-4cf3-bfc8-1d8572b63c96/alerts-and-symptoms/alerts/bfd7ac13-5f18-4169-bff8-03d28fd63472\">, #<SNMP::VarBind:0xc4a72f9 @name=[*******.4.1.6876.********.8.0], @value=\"bfd7ac13-5f18-4169-bff8-03d28fd63472\">, #<SNMP::VarBind:0x10a7640c @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: a32f3a6e-2495-4cf1-a816-eb978f41d71e\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: Cluster DRS settings are tunable to improve cluster workload balance\\nactive: true\\nobj.1.name: Louisa\\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\\nobj.1.metric: \\nobj.1.info: Property equals true = true\\nsymptom: At least one host in the cluster has critically high CPU \\\"demand\\\" workload\\nactive: true\\nobj.1.name: Louisa\\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\\nobj.1.metric: CPU|Demand|Highest Host Workload\\nobj.1.info: HT above 95.85239189120038 > 95\\nsymptom: SymptomDefinition-VMWARE-ClusterCPUDemandWorkloadWarning\\nactive: false\\nobj.1.name: Louisa\\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\\n\">, #<SNMP::VarBind:0x13b89863 @name=[*******.4.1.6876.********.10.0], @value=\"Virtualization/Hypervisor\">, #<SNMP::VarBind:0x3545a5c5 @name=[*******.4.1.6876.********.11.0], @value=\"Performance\">, #<SNMP::VarBind:0x6a47c42e @name=[*******.4.1.6876.********.12.0], @value=\"info\">, #<SNMP::VarBind:0x416b5a6e @name=[*******.4.1.6876.********.13.0], @value=\"warning\">, #<SNMP::VarBind:0x1647a9a0 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0xe344d20 @name=[*******.4.1.6876.********.15.0], @value=\"CPU|Demand|Highest Host Workload\">, #<SNMP::VarBind:0x5a869e7c @name=[*******.4.1.6876.********.16.0], @value=\"ClusterComputeResource\">, #<SNMP::VarBind:0x41251768 @name=[*******.4.1.6876.********.17.0], @value=\"Cluster has unbalanced workload\">, #<SNMP::VarBind:0x6a04ac45 @name=[*******.4.1.6876.********.18.0], @value=\"CPU or memory workload is spread unevenly among the hosts in the cluster\">, #<SNMP::VarBind:0x46f5a168 @name=[*******.4.1.6876.********.19.0], @value=\"risk\">, #<SNMP::VarBind:0x286ea2b4 @name=[*******.4.1.6876.********.20.0], @value=\"SNMP Olympus Prd\">, #<SNMP::VarBind:0x41197e86 @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0xeb09464 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x65a59a3b @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=2195128, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "50863d30-2ccc-47f6-a3b4-a990d5a62b08", "event.kafka.offset": 712, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_monceau-events-prd", "event.kafka.topic": "a1232-vserver_monceau-events-prd", "event.kafka.timestamp": "2024-07-11T03:04:26.573Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.vmwareAlertEntityName": "<PERSON>", "snmptrap.vmwareAlertTimestamp": "1720667062382", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "CPU|Demand|Highest Host Workload", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "Cluster has unbalanced workload", "snmptrap.vmwareAlertCriticality": "warning", "snmptrap.vmwareAlertDefinitionDesc": "CPU or memory workload is spread unevenly among the hosts in the cluster", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id bfd7ac13-5f18-4169-bff8-03d28fd63472 is generated at Thu Jul 11 05:04:22 CEST 2024;", "snmptrap.vmwareAlertHealth": "info", "snmptrap.SNMPv2-MIB::sysUpTime.0": "310 days, 04:51:24.66", "snmptrap.vmwareAlertType": "Virtualization/Hypervisor", "snmptrap.vmwareAlertSubtype": "Performance", "snmptrap.vmwareAlertAliveServerName": "iictcismwv019.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "SNMP Olympus Prd", "snmptrap.vmwareAlertResourceKind": "ClusterComputeResource", "snmptrap.vmwareAlertMessage": "symptomSet: a32f3a6e-2495-4cf1-a816-eb978f41d71e\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: Cluster DRS settings are tunable to improve cluster workload balance\nactive: true\nobj.1.name: <PERSON>\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\nobj.1.metric: \nobj.1.info: Property equals true = true\nsymptom: At least one host in the cluster has critically high CPU \"demand\" workload\nactive: true\nobj.1.name: <PERSON>\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\nobj.1.metric: CPU|Demand|Highest Host Workload\nobj.1.info: HT above 95.85239189120038 > 95\nsymptom: SymptomDefinition-VMWARE-ClusterCPUDemandWorkloadWarning\nactive: false\nobj.1.name: <PERSON>\nobj.1.id: 3264ef12-da89-4cf3-bfc8-1d8572b63c96\n", "snmptrap.vmwareAlertID": "bfd7ac13-5f18-4169-bff8-03d28fd63472", "snmptrap.vmwareAlertURL": "https://************/ui/index.action#environment/object-browser/hierarchy/3264ef12-da89-4cf3-bfc8-1d8572b63c96/alerts-and-symptoms/alerts/bfd7ac13-5f18-4169-bff8-03d28fd63472", "snmptrap.vmwareAlertRisk": "warning", "snmptrap.vmwareAlertImpact": "risk", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}, {"input": {"@version": "1", "@timestamp": "2024-07-16T08:05:13.409849779Z", "message": "#<SNMP::SNMPv2_Trap:0x3c932b25 @varbind_list=[#<SNMP::VarBind:0x65052cae @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x47b9fb21 @value=3130195404>>, #<SNMP::VarBind:0x62563af0 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x42d0932a @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0xefcb782 @name=[*******.4.1.6876.********.2.0], @value=\"itmsbiesxs261.msnet.railb.be\">, #<SNMP::VarBind:0x614358f @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x55460e07 @name=[*******.4.1.6876.********.4.0], @value=\"1721117113075\">, #<SNMP::VarBind:0x13499e46 @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x78626b0a @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id 92c3c5a8-c453-4686-bffa-80f57061bb5b is generated at Tue Jul 16 10:05:13 CEST 2024;\">, #<SNMP::VarBind:0x5f7d81c4 @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/92c3c5a8-c453-4686-bffa-80f57061bb5b\">, #<SNMP::VarBind:0x3aae8a1a @name=[*******.4.1.6876.********.8.0], @value=\"92c3c5a8-c453-4686-bffa-80f57061bb5b\">, #<SNMP::VarBind:0x65ca88a7 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: e6a56d2c-bb77-4e65-8064-205f14445a8c\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: Host disconnected from vCenter\\nactive: true\\nobj.1.name: itmsbiesxs261.msnet.railb.be\\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\\nobj.1.metric: \\nobj.1.info: Property not equal notResponding != connected\\nsymptom: SymptomDefinition-VMWARE-HostIsPlacedInStandbyMode\\nactive: false\\nobj.1.name: itmsbiesxs261.msnet.railb.be\\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\\n\">, #<SNMP::VarBind:0xe1b852f @name=[*******.4.1.6876.********.10.0], @value=\"Virtualization/Hypervisor\">, #<SNMP::VarBind:0x3ddb4689 @name=[*******.4.1.6876.********.11.0], @value=\"Availability\">, #<SNMP::VarBind:0x73ebe572 @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x4255c418 @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x10351380 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x773de001 @name=[*******.4.1.6876.********.15.0], @value=\"\">, #<SNMP::VarBind:0x5b515be1 @name=[*******.4.1.6876.********.16.0], @value=\"HostSystem\">, #<SNMP::VarBind:0x37483197 @name=[*******.4.1.6876.********.17.0], @value=\"Host has lost connection to vCenter Server\">, #<SNMP::VarBind:0xfd99ded @name=[*******.4.1.6876.********.18.0], @value=\"The host has been unexpectedly disconnected from vCenter Server. The virtual machines on the host have also lost connectivity to vCenter Server. The host might not have a problem and the virtual machines on the host might be running as expected, but any configuration, data, and events from the host and its virtual machines are lost.\">, #<SNMP::VarBind:0x177c59ff @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x46a386a2 @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x3091c368 @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x73e41662 @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x6a21a40 @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "f1632bf7-ad08-46fc-b364-58836df7ab3e", "event.kafka.offset": 4651, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-16T08:05:13.510Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.vmwareAlertEntityName": "itmsbiesxs261.msnet.railb.be", "snmptrap.vmwareAlertTimestamp": "1721117113075", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "Host has lost connection to vCenter Server", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "The host has been unexpectedly disconnected from vCenter Server. The virtual machines on the host have also lost connectivity to vCenter Server. The host might not have a problem and the virtual machines on the host might be running as expected, but any configuration, data, and events from the host and its virtual machines are lost.", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id 92c3c5a8-c453-4686-bffa-80f57061bb5b is generated at Tue Jul 16 10:05:13 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "362 days, 06:59:14.04", "snmptrap.vmwareAlertType": "Virtualization/Hypervisor", "snmptrap.vmwareAlertSubtype": "Availability", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "HostSystem", "snmptrap.vmwareAlertMessage": "symptomSet: e6a56d2c-bb77-4e65-8064-205f14445a8c\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: Host disconnected from vCenter\nactive: true\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\nobj.1.metric: \nobj.1.info: Property not equal notResponding != connected\nsymptom: SymptomDefinition-VMWARE-HostIsPlacedInStandbyMode\nactive: false\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\n", "snmptrap.vmwareAlertID": "92c3c5a8-c453-4686-bffa-80f57061bb5b", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/92c3c5a8-c453-4686-bffa-80f57061bb5b", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {"s2110_occurrence": [{"event_type": "problem", "ci_id": "itmsbiesxs261", "metric_name": "Host2vCenter", "metric_type": "/NetworkConnectivity/", "additional_data": {"collection": "iictcismwv009.msnet.railb.be", "delay": 900, "alert_message": "symptomSet: e6a56d2c-bb77-4e65-8064-205f14445a8c\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: Host disconnected from vCenter\nactive: true\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\nobj.1.metric: \nobj.1.info: Property not equal notResponding != connected\nsymptom: SymptomDefinition-VMWARE-HostIsPlacedInStandbyMode\nactive: false\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\n"}, "raise_time": "2024-07-16 08:05:13", "clear_time": null, "severity": 5, "summary": "The host has been unexpectedly disconnected from vCenter Server. The virtual machines on the host have also lost connectivity to vCenter Server. The host might not have a problem and the virtual machines on the host might be running as expected, but any configuration, data, and events from the host and its virtual machines are lost."}]}}, {"input": {"@version": "1", "@timestamp": "2024-07-16T08:35:18.612120564Z", "message": "#<SNMP::SNMPv2_Trap:0x35ead8d9 @varbind_list=[#<SNMP::VarBind:0x513e5da5 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x61988d49 @value=3132000607>>, #<SNMP::VarBind:0x2d7adb3 @name=[*******.*******.4.1.0], @value=[*******.4.1.6876.********.46]>, #<SNMP::VarBind:0x55bce18f @name=[*******.4.1.6876.********.1.0], @value=\"iictcismwv009.msnet.railb.be\">, #<SNMP::VarBind:0x764c5de8 @name=[*******.4.1.6876.********.2.0], @value=\"itmsbiesxs261.msnet.railb.be\">, #<SNMP::VarBind:0x539b74ad @name=[*******.4.1.6876.********.3.0], @value=\"General\">, #<SNMP::VarBind:0x6c1ee158 @name=[*******.4.1.6876.********.4.0], @value=\"1721118918358\">, #<SNMP::VarBind:0x5e9cd5cd @name=[*******.4.1.6876.********.5.0], @value=\"critical\">, #<SNMP::VarBind:0x4aab70eb @name=[*******.4.1.6876.********.6.0], @value=\"New alert by id f98851c8-4504-4534-a376-23557bde496f is generated at Tue Jul 16 10:35:18 CEST 2024;\">, #<SNMP::VarBind:0x429d5b0f @name=[*******.4.1.6876.********.7.0], @value=\"https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/f98851c8-4504-4534-a376-23557bde496f\">, #<SNMP::VarBind:0x59926625 @name=[*******.4.1.6876.********.8.0], @value=\"f98851c8-4504-4534-a376-23557bde496f\">, #<SNMP::VarBind:0x6198e267 @name=[*******.4.1.6876.********.9.0], @value=\"symptomSet: d108a80b-af24-4875-8f98-94341ef2e4ba\\nrelation: self\\ntotalObjects: 1\\nviolatingObjects: 1\\nsymptom: vSphere HA detected a network isolated host\\nactive: true\\nobj.1.name: itmsbiesxs261.msnet.railb.be\\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\\nobj.1.metric: \\nobj.1.info: vSphere HA detected that host itmsbiesxs261.msnet.railb.be is isolated from cluster Linea in VMWare\\n\">, #<SNMP::VarBind:0x70a5ea97 @name=[*******.4.1.6876.********.10.0], @value=\"Virtualization/Hypervisor\">, #<SNMP::VarBind:0x584882af @name=[*******.4.1.6876.********.11.0], @value=\"Availability\">, #<SNMP::VarBind:0x122174cd @name=[*******.4.1.6876.********.12.0], @value=\"critical\">, #<SNMP::VarBind:0x340c2734 @name=[*******.4.1.6876.********.13.0], @value=\"info\">, #<SNMP::VarBind:0x6ba29270 @name=[*******.4.1.6876.********.14.0], @value=\"info\">, #<SNMP::VarBind:0x77632851 @name=[*******.4.1.6876.********.15.0], @value=\"\">, #<SNMP::VarBind:0x6efdbd6c @name=[*******.4.1.6876.********.16.0], @value=\"HostSystem\">, #<SNMP::VarBind:0x3b1d0200 @name=[*******.4.1.6876.********.17.0], @value=\"vSphere High Availability (HA) has detected a network-isolated host\">, #<SNMP::VarBind:0x44d33601 @name=[*******.4.1.6876.********.18.0], @value=\"vSphere HA has detected that the host is network-isolated and has powered off or shut down virtual machines running on the host. A host is network isolated if both of the following conditions are met:\\n- Isolation addresses are configured and the host is unable to ping the addresses.\\n- The vSphere HA agent on the host cannot access any of the agents that are running on the other cluster hosts.\\n\\nWhen a host is network-isolated, it continues to monitor the virtual machines that remain powered-on. Host network isolation affects how vSphere HA restarts virtual machines after a failure. vSphere HA powers off or shuts down a virtual machine only if the agent on the host determines that a master host is responsible for the virtual machine.\">, #<SNMP::VarBind:0x2a9cbf6a @name=[*******.4.1.6876.********.19.0], @value=\"health\">, #<SNMP::VarBind:0x29097188 @name=[*******.4.1.6876.********.20.0], @value=\"Snmp to Olympus Prd\">, #<SNMP::VarBind:0x7a576bab @name=[*******.4.1.6876.********.21.0], @value=\"\">, #<SNMP::VarBind:0x27e929de @name=[*******.4.1.6876.********.22.0], @value=\"\">, #<SNMP::VarBind:0x6841a5a6 @name=[*******.4.1.6876.********.23.0], @value=\"\">], @request_id=**********, @error_index=0, @error_status=0, @source_ip=\"**************\">", "type": "snmp_trap", "event.uuid": "fb1b813d-2270-4266-a957-c03e64c93a66", "event.kafka.offset": 4654, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1232-vserver_muizen_brussel-events-prd", "event.kafka.topic": "a1232-vserver_muizen_brussel-events-prd", "event.kafka.timestamp": "2024-07-16T08:35:18.713Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.vmwareAlertEntityName": "itmsbiesxs261.msnet.railb.be", "snmptrap.vmwareAlertTimestamp": "1721118918358", "snmptrap.SNMPv2-SMI::enterprises.6876.********.23.0": "", "snmptrap.vmwareAlertMetricName": "", "snmptrap.vmwareAlertEfficiency": "info", "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.6876.********.46", "snmptrap.vmwareAlertDefinitionName": "vSphere High Availability (HA) has detected a network-isolated host", "snmptrap.vmwareAlertCriticality": "critical", "snmptrap.vmwareAlertDefinitionDesc": "vSphere HA has detected that the host is network-isolated and has powered off or shut down virtual machines running on the host. A host is network isolated if both of the following conditions are met:\n- Isolation addresses are configured and the host is unable to ping the addresses.\n- The vSphere HA agent on the host cannot access any of the agents that are running on the other cluster hosts.\n\nWhen a host is network-isolated, it continues to monitor the virtual machines that remain powered-on. Host network isolation affects how vSphere HA restarts virtual machines after a failure. vSphere HA powers off or shuts down a virtual machine only if the agent on the host determines that a master host is responsible for the virtual machine.", "snmptrap.vmwareAlertEntityType": "General", "snmptrap.vmwareAlertRootCause": "New alert by id f98851c8-4504-4534-a376-23557bde496f is generated at Tue Jul 16 10:35:18 CEST 2024;", "snmptrap.vmwareAlertHealth": "critical", "snmptrap.SNMPv2-MIB::sysUpTime.0": "362 days, 12:00:06.07", "snmptrap.vmwareAlertType": "Virtualization/Hypervisor", "snmptrap.vmwareAlertSubtype": "Availability", "snmptrap.vmwareAlertAliveServerName": "iictcismwv009.msnet.railb.be", "snmptrap.SNMPv2-SMI::enterprises.6876.********.22.0": "", "snmptrap.vmwAlertNotificationRules": "Snmp to Olympus Prd", "snmptrap.vmwareAlertResourceKind": "HostSystem", "snmptrap.vmwareAlertMessage": "symptomSet: d108a80b-af24-4875-8f98-94341ef2e4ba\nrelation: self\ntotalObjects: 1\nviolatingObjects: 1\nsymptom: vSphere HA detected a network isolated host\nactive: true\nobj.1.name: itmsbiesxs261.msnet.railb.be\nobj.1.id: 5b960a16-01f2-4056-bc1f-b1784dcedf32\nobj.1.metric: \nobj.1.info: vSphere HA detected that host itmsbiesxs261.msnet.railb.be is isolated from cluster Linea in VMWare\n", "snmptrap.vmwareAlertID": "f98851c8-4504-4534-a376-23557bde496f", "snmptrap.vmwareAlertURL": "https://*************/ui/index.action#environment/object-browser/hierarchy/5b960a16-01f2-4056-bc1f-b1784dcedf32/alerts-and-symptoms/alerts/f98851c8-4504-4534-a376-23557bde496f", "snmptrap.vmwareAlertRisk": "info", "snmptrap.vmwareAlertImpact": "health", "snmptrap.SNMPv2-SMI::enterprises.6876.********.21.0": "", "host.ip": "**************"}, "output": {}, "comment": "No alarms expected, this is here for code-coverage"}]}