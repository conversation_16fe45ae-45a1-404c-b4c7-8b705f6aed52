"""OID mapping for Netact."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Netact."""

    @property
    def unmapped_oids(self) -> dict[str, str]:
        """Return the unmapped OIDs."""
        return {
            "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-MIB::snmpTrapOID.0",
            "SNMPv2-SMI::enterprises.28458.1.26.2.1.6.2": "SNMPv2-SMI::enterprises.28458.1.26.2.1.6.2",
            "SNMPv2-SMI::enterprises.28458.1.26.2.1.6.3": "SNMPv2-SMI::enterprises.28458.1.26.2.1.6.3",
            "SNMPv2-SMI::enterprises.28458.1.26.2.1.6.4": "SNMPv2-SMI::enterprises.28458.1.26.2.1.6.4",
            "SNMPv2-SMI::enterprises.28458.1.26.2.1.6.5": "SNMPv2-SMI::enterprises.28458.1.26.2.1.6.5",
            "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.2": "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.2",
            "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.4": "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.4",
            "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.5": "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.5",
            "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.6": "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.6",
            "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.8": "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.8",
            "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.13": "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.13",
            "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.14": "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.14",
            "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.16": "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.16",
        }

    @property
    def drop_if_all_nan(self) -> list[str]:
        """Return fields to drop if all values are NaN."""
        return [
            "SNMPv2-SMI::enterprises.28458.1.26.2.1.6.5",
            "SNMPv2-MIB::snmpTrapOID.0",
            "SNMPv2-SMI::enterprises.28458.1.26.3.1.1.16",
        ]

    def get_oids(self) -> list[dict]:
        """Return oid."""
        return [self.unmapped_oids]
