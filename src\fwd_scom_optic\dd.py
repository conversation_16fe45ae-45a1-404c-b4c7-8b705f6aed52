"""Details Design module for mon-scom."""

from functools import partial

import pandas as pd

from fwd_scom_optic.config import config
from mon_scom import utils
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType


@trace_scan(MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, _agent_id: int) -> pd.DataFrame:
    """Run the Forward SCOM to OPTIC code."""
    if config.debug:
        pd.set_option("display.max_columns", None)

    transformed = _transform(df)

    return transformed


@trace_scan(MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame) -> pd.DataFrame:
    """Make the dataframe transformation to include all required details for the forwarding."""
    # NaN values are not handled properly so we first fillna with "N/A"
    df = df.fillna("N/A")

    # Use partial functions to apply the same function to multiple columns
    _str_to_timestamp_timeraised = partial(_str_to_timestamp, key="TimeRaised")
    _str_to_timestamp_timeresolved = partial(_str_to_timestamp, key="TimeResolved")

    df["TimeRaisedUTC"] = df.apply(_str_to_timestamp_timeraised, axis=1)
    df["TimeResolvedUTC"] = df.apply(_str_to_timestamp_timeresolved, axis=1)

    return df


def _str_to_timestamp(row: pd.Series, key: str) -> int | str:
    return utils.str_to_timestamp(row[key])
