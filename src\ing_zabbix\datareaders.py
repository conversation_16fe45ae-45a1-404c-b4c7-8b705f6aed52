"""Datareaders for ing-zabbix."""

import logging
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone

from dateutil import relativedelta
from zabbix_utils import ZabbixAPI

from ing_zabbix.config import ZabbixConfig
from olympus_common.dataclass import dataclass_field
from olympus_common.datareaders import Checkpoint, DataReader
from olympus_common.elastic_apm import CaptureSpan, trace_scan
from olympus_common.enums import MeasureType, ZabbixCC
from olympus_common.exceptions import OlympusError
from olympus_common.utils import now_timestamp


@dataclass
class ZabbixReader(DataReader):
    """Represent a ZabbixReader.

    This concrete DataReader reads events from the configured Zabbix API.
    """

    checkpoint: Checkpoint
    config: ZabbixConfig = dataclass_field(ZabbixConfig)

    _old_data: list[dict] | None = None
    _api: ZabbixAPI | None = None
    _drp_api: ZabbixAPI | None = None
    _master_drp_api: ZabbixAPI | None = None
    _master_api: ZabbixAPI | None = None

    @trace_scan(transaction_type=MeasureType.CUSTOM.value)
    def read_data(self) -> list[dict]:
        """Read events from the Zabbix api.

        Notes
        -----
        A list with a single dictionary is returned. "events" and "hosts" are the available keys in this dictionary.
        This way of working is used because Application.run_forever decorated functions expect to receive list[dict].
        """
        if not self.checkpoint.read():
            # The write is needed because without any checkpoint on cold start the event.get method of the Zabbix API
            # will retrieve the events from the past 4 years on our architecture and lead to a timeout.
            # Instead we will start reading events from 2 weeks ago.
            self.checkpoint.write(str(int((datetime.now(tz=timezone.utc) - timedelta(days=14)).timestamp())))
            self.checkpoint.read()

        # For some CCs (currently LCC and UCC), a DRP API exists so whenever we detect that the Zabbix server is down,
        # we switch to the DRP API.
        # This detection is made on the master Zabbix API (master Zabbix currently being LCC), more precisely on the
        # DRP API. In case both the DRP and the main Zabbix servers are down, we are toast anyway so we assume at least
        # one of them is up.
        if self.config.zabbix_cc in [ZabbixCC.LCC.value, ZabbixCC.UCC.value]:
            is_master_zabbix_down = self._is_zabbix_server_down(
                self.config.master_zabbix_server_alias, zabbix_api=self.master_drp_api
            )

        if self.config.zabbix_cc == ZabbixCC.UCC.value:
            if is_master_zabbix_down:
                master_zabbix_api = self.master_drp_api
            else:
                master_zabbix_api = self.master_api
            if self._is_zabbix_server_down(self.config.zabbix_server_alias, zabbix_api=master_zabbix_api):
                zabbix_api = self.drp_api
            else:
                zabbix_api = self.api
        elif self.config.zabbix_cc == ZabbixCC.LCC.value:
            # For LCC the master Zabbix is the LCC Zabbix itself.
            if is_master_zabbix_down:
                zabbix_api = self.drp_api
            else:
                zabbix_api = self.api
        else:
            zabbix_api = self.api

        logging.debug(f"Getting data for {type(self).__name__} with {self.checkpoint.value=}")
        events = self._get_events(zabbix_api=zabbix_api)
        if self._old_data:  # Filter old events since there may be some overlap.
            old_event_ids = [event["eventid"] for event in self._old_data[0]["events"]]
            events = [event for event in events if event["eventid"] not in old_event_ids]
        self._add_heartbeat_to_events(events)

        hosts = self._get_hosts(zabbix_api, events)

        if not events and not hosts:
            return []
        return [{"events": events, "hosts": hosts}]

    @trace_scan(transaction_type=MeasureType.CUSTOM.value)
    def success(self, data: list[dict]) -> None:
        """Update `self.checkpoint.value` with the max clock of the provided data and write it as checkpoint.

        Notes
        -----
        Because ZabbixAPI uses 'greater than' rather than 'greater than or equal', we subtract 5 milliseconds from the
        latest result in the given events and ignore events that were processed by a previous run (data stored in
        self._old_data).
        """
        if not data:
            return  # Nothing to do on empty data
        if not (events := [event for event in data[0]["events"] if event["eventid"] != "0"]):
            # Nothing to do with only heartbeats
            return

        new_checkpoint_value = str(max(int(result["clock"]) for result in events) - 5)
        self.checkpoint.write(new_checkpoint_value)
        self._old_data = data  # Store old data for next iteration to avoid overlapping events.

    @trace_scan(transaction_type=MeasureType.CUSTOM.value)
    def error(self, _: list[dict], __: Exception) -> None:
        """Do nothing on error."""

    @CaptureSpan(span_type=MeasureType.KAFKA.value)
    def _get_events(self, zabbix_api: ZabbixAPI) -> list[dict]:
        event_result = zabbix_api.event.get(
            output=["name", "value", "severity", "clock", "eventid"],
            selectHosts=["hostid", "host"],
            selectTags="extend",
            selectRelatedObject=["triggerid", "description", "comments"],
            time_from=self.checkpoint.value,
        )

        if not isinstance(event_result, list):
            raise OlympusError("The response received from the Zabbix API for get events is not of type list.")

        return event_result

    @CaptureSpan(span_type=MeasureType.KAFKA.value)
    def _get_hosts(self, zabbix_api: ZabbixAPI, events: list[dict]) -> list[dict]:
        """Get the hosts for each of the events."""
        if not events:
            return []

        hostids_set = set(
            host.get("hostid", "N/A")
            for event in events
            for host in event.get("hosts", [])
            if isinstance(host, dict)  # isinstance check for typing completion for `host.get`
        )
        if "N/A" in hostids_set:
            hostids_set.remove("N/A")

        host_result = zabbix_api.host.get(output=["hostid"], selectInventory=["os"], hostids=list(hostids_set))

        if not isinstance(host_result, list):
            raise OlympusError("The response received from the Zabbix API for get hosts is not of type list.")

        return host_result

    @CaptureSpan(span_type=MeasureType.KAFKA.value)
    def _get_zabbix_server(self, host_name: str, zabbix_api: ZabbixAPI) -> dict:
        """Get the hosts for each of the events."""
        host_result = zabbix_api.host.get(output=["hostid"], selectInventory=["os"], filter={"name": host_name})

        if not isinstance(host_result, list):
            raise OlympusError("The response received from the Zabbix API for the Zabbix server is not of type list.")

        return host_result[0]

    @CaptureSpan(span_type=MeasureType.KAFKA.value)
    def _get_zabbix_server_events(self, zabbix_server_host_id: str, zabbix_api: ZabbixAPI) -> list[dict]:
        """Get the events on the Zabbix server.

        We search up until one month ago (arbitrary); we cannot use the checkpoint value otherwise we would miss
        the cases where the Zabbix server is unavailable for more than 15 seconds.
        """
        one_month_ago: datetime = datetime.now() - relativedelta.relativedelta(months=1)
        time_from = int(one_month_ago.timestamp())

        event_result = zabbix_api.event.get(
            hostids=[zabbix_server_host_id],
            output=["name", "value", "severity", "clock", "eventid"],
            selectHosts=["hostid", "host"],
            selectTags="extend",
            selectRelatedObject=["triggerid", "description", "comments"],
            time_from=time_from,
        )

        if not isinstance(event_result, list):
            raise OlympusError(
                "The response received from the Zabbix API for the events on the Zabbix server is not of type list."
            )

        events_filtered = []
        for event in event_result:
            for tag in event.get("tags", []):
                if tag["tag"] == "MON_OBJ4" and tag["value"].lower() in ["frontend", "server"]:
                    events_filtered.append(event)

        return events_filtered

    @CaptureSpan(span_type=MeasureType.KAFKA.value)
    def _add_heartbeat_to_events(self, events: list[dict]) -> None:
        """Add a heartbeat event to the list."""
        name = f"{self.config.heartbeat_ci} Heartbeat Message"
        clock = now_timestamp()
        hosts = [{"host": self.config.heartbeat_ci}]
        tags = [
            {"tag": "METRIC_NAME", "value": "Heartbeat"},
            {"tag": "METRIC_TYPE", "value": "/ApplicationEvent/"},
            {"tag": "CI_ID", "value": self.config.heartbeat_ci},
        ]
        heartbeat_event = {
            "eventid": "0",
            "name": name,
            "value": 1,
            "severity": 1,
            "clock": clock,
            "hosts": hosts,
            "tags": tags,
        }
        events.append(heartbeat_event)
        return None

    @CaptureSpan(span_type=MeasureType.KAFKA.value)
    def _is_zabbix_server_down(self, zabbix_server_alias: str, zabbix_api: ZabbixAPI) -> bool:
        """Return whether the master Zabbix server (currently LCC) is down."""
        zabbix_server_host = self._get_zabbix_server(host_name=zabbix_server_alias, zabbix_api=zabbix_api)

        events = self._get_zabbix_server_events(
            zabbix_server_host_id=zabbix_server_host["hostid"], zabbix_api=zabbix_api
        )

        # Do we have an alarm that is not followed by a clear?
        is_problem = False
        for event in events:
            is_problem = str(event["value"]) == "1"

        return is_problem

    @property
    @CaptureSpan(span_type=MeasureType.CUSTOM.value)
    def api(self):
        """Return an instantiated ZabbixAPI object."""
        if not self._api:
            self._api = ZabbixAPI(url=self.config.endpoint, user=self.config.user, password=self.config.password)

        return self._api

    @property
    @CaptureSpan(span_type=MeasureType.CUSTOM.value)
    def drp_api(self):
        """Return an instantiated ZabbixAPI object for the DRP queries."""
        if not self._drp_api:
            self._drp_api = ZabbixAPI(
                url=self.config.drp_endpoint, user=self.config.user, password=self.config.password
            )

        return self._drp_api

    @property
    @CaptureSpan(span_type=MeasureType.CUSTOM.value)
    def master_api(self):
        """Return an instantiated ZabbixAPI object."""
        if not self._master_api:
            self._master_api = ZabbixAPI(
                url=self.config.master_endpoint, user=self.config.master_user, password=self.config.master_password
            )

        return self._master_api

    @property
    @CaptureSpan(span_type=MeasureType.CUSTOM.value)
    def master_drp_api(self):
        """Return an instantiated ZabbixAPI object for the DRP queries."""
        if not self._drp_api:
            self._master_drp_api = ZabbixAPI(
                url=self.config.master_drp_endpoint, user=self.config.master_user, password=self.config.master_password
            )

        return self._master_drp_api
