"""Static variables used in the mon_zabbix module."""

# Repetitive strings
CL_RG_STR = "CL_RG"
SWITCHED_STR = "has been switched"
NA_STR = "N/A"

# Lookup tables relatives variables
ZABBIX_SPECIAL_CI_ID_MAPPING: dict[str, str] = {
    "OSE_API": "MON_INFRA_A1647",
    "OSE_DNS": "MON_MASTER_A1647",
    "OSE_ETCD": "MON_MASTER_A1647",
    "OSE_FREE": "MON_INFRA_A1647",
    "OSE_LOADS": "MON_INFRA_A1647",
    "OSE_NET": "MON_NODE_A1647",
    "OSE_SDN": "MON_NODE_A1647",
}

SNOOZED_MONITORED_ELEMENTS: list[str] = [
    "URL_A1959_HC_DRP_Hand_Proxy_DRP",
    "URL_A1959_HC_DRP_ISE_Proxy_DRP",
    "URL_A1959_HC_DRP_Infoblox_Proxy_DRP",
    "URL_A1959_HC_Happi_Happi_Api",
    "URL_A1959_HC_Proxy_Hand_Proxy",
    "URL_A1959_HC_Proxy_ISE_Proxy",
    "URL_A1959_HC_Proxy_Infoblox_Proxy",
    "URL_A1959_HC_Telecom_Hand_Service",
    "URL_A1959_HC_Telecom_Ip_Service_Monitoring",
    "URL_A1959_HC_Telecom_NDM",
    "URL_A1962_HC_Networkteam_ISE",
    "URL_A1962_HC_Networkteam_Infoblox",
]

PLATFORM_MAPPING: dict[str, str] = {
    "MW_APACHE": "Apache",
    "MW_HAWK": "Hawk",
    "MW_JAVA": "Java",
    "MW_JBOSS": "JBoss",
    "MW_MEMCACHED": "Memcached",
    "MW_MQ": "WebSphereMQ",
    "MW_NGINX": "Nginx",
    "MW_ODM": "ODM",
    "MW_TIBCO-AS": "TIBCO",
    "MW_TIBCO-BW": "TIBCO",
    "MW_TIBCO-EWS": "TIBCO",
    "MW_TOMCAT": "Tomcat",
    "MW_VARNISH": "Varnish",
    "MW_WEBLOGIC": "WebLogic",
}
