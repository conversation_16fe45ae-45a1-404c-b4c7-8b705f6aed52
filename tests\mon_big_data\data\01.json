{"data": [{"input": {"truncatedAlerts": 0, "groupKey": "{}/{env=\"prod\",optic=\"enabled\"}:{}", "commonAnnotations": {"summary": "GitLab CI pipeline #testid3 for test_olympus_3 has failed on meru: testurl3"}, "status": "firing", "alerts": [{"annotations": {"summary": "GitLab CI pipeline #testid3 for test_olympus_3 has failed on meru: testurl3"}, "generatorURL": "", "labels": {"metrictype": "/Application/", "ci": "meru", "optic": "enabled", "alertname": "ingestion_job_failure-test_olympus_3", "application": "cloudera", "env": "prod", "acode": "A1556", "metricname": "ingestion_job_failure", "ingest_pipeline": "test_olympus_3", "severity": "error"}, "fingerprint": "6b2c1e1fc151523a", "status": "firing", "startsAt": "2025-01-08T08:21:23Z", "endsAt": "0001-01-01T00:00:00Z"}], "commonLabels": {"metrictype": "/Application/", "ci": "meru", "optic": "enabled", "alertname": "ingestion_job_failure-test_olympus_3", "application": "cloudera", "env": "prod", "acode": "A1556", "metricname": "ingestion_job_failure", "ingest_pipeline": "test_olympus_3", "severity": "error"}, "externalURL": "https://iictmismlv013.msnet.railb.be/alertmanager", "receiver": "logstash-lab", "groupLabels": {}, "version": "4"}, "output": {"event_type": "problem", "severity": 4, "summary": "GitLab CI pipeline #testid3 for test_olympus_3 has failed on meru: testurl3", "agent_id": 0, "clear_type": "automatic", "manager": "mon-big-data", "action_class": "IT", "top_level": "A1556", "actionable": null, "additional_data": "{\"a_code\": \"A1556\", \"alert_name\": \"ingestion_job_failure-test_olympus_3\", \"ci\": \"meru\", \"metric_name\": \"ingestion_job_failure\", \"metric_type\": \"/Application/\", \"severity\": \"error\", \"status\": \"firing\", \"summary\": \"GitLab CI pipeline #testid3 for test_olympus_3 has failed on meru: testurl3\"}", "ci_id": "meru", "node": "N/A", "metric_type": "/Application/", "metric_name": "ingestion_job_failure", "node_alias": "N/A", "clear_time": null}}, {"input": {"receiver": "olympus-acc", "status": "firing", "alerts": [{"status": "firing", "labels": {"acode": "A2066", "alertname": "prometheus_healthcheck", "category": "healthcheck", "ci": "iictmismlv013", "env": "prod", "instance": "iictmismlv013.msnet.railb.be", "job": "prometheus", "metricname": "prometheus_healthcheck", "metrictype": "/Application/", "node": "iictmismlv013.msnet.railb.be", "optic": "enabled", "server": "iictmismlv013", "severity": "info", "source": "prometheus"}, "annotations": {"summary": "Healthcheck Instance iictmismlv013.msnet.railb.be is ok"}, "startsAt": "2023-11-27T12:49:33.674Z", "endsAt": "0001-01-01T00:00:00Z", "generatorURL": "https://iictmismlv013.msnet.railb.be/prometheus/graph?", "fingerprint": "0eedc3cd100922a7"}], "groupLabels": {"instance": "iictmismlv013.msnet.railb.be"}, "commonLabels": {"acode": "A2066", "alertname": "prometheus_healthcheck", "category": "healthcheck", "ci": "iictmismlv013", "env": "prod", "instance": "iictmismlv013.msnet.railb.be", "job": "prometheus", "metricname": "prometheus_healthcheck", "metrictype": "/Application/", "node": "iictmismlv013.msnet.railb.be", "optic": "enabled", "server": "iictmismlv013", "severity": "info", "source": "prometheus"}, "commonAnnotations": {"summary": "Healthcheck Instance iictmismlv013.msnet.railb.be is ok"}, "externalURL": "https://iictmismlv013.msnet.railb.be/alertmanager", "version": "4", "groupKey": "{}/{env=\"prod\",optic=\"enabled\"}: {instance=\"iictmismlv013.msnet.railb.be\"}", "truncatedAlerts": 0}, "output": {"event_type": "heartbeat", "severity": 1, "summary": "BigData HeartBeat Message", "agent_id": 0, "clear_type": "automatic", "manager": "mon-big-data", "action_class": "IT", "top_level": "A2066", "actionable": false, "additional_data": null, "ci_id": "iictmismlv013", "node": "iictmismlv013", "metric_type": "/ApplicationEvent/", "metric_name": "Heartbeat", "node_alias": "iictmismlv013", "clear_time": null}}]}