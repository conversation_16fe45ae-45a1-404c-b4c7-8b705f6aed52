"""OID mapping for WebSphere MQ."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for WebSphere MQ."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "@timestamp": "event_timestamp",
            "host.name": "hostname",
            "message": "message",
            "metric_name": "metric_name",
            "ci_id": "ci_id",
            "syslogseverity": "syslogseverity",
        }

    @property
    def drop_if_all_nan(self) -> list[str]:
        """Return fields to drop if all values are NaN."""
        return ["message", "metric_name", "ci_id", "syslogseverity"]
