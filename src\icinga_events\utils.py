"""Utility functions for Icinga-events."""

from bisect import bisect_left
from typing import Any

from sqlalchemy.orm import Session

from icinga_events.icinga_alarm import CustomIcingaAlarm
from olympus_common.db import Alarm, get_alarm_field
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import IcingaHostStatus, IcingaObjectType, IcingaServiceStatus, MeasureType, Severity
from olympus_common.icinga import IcingaClient


@CaptureSpan(MeasureType.CUSTOM.value)
def compute_icinga_host_status(alarm: CustomIcingaAlarm, session: Session) -> int:
    """Compute the Icinga host status based on the status of its services."""
    if alarm.status == IcingaServiceStatus.CRITICAL.value:
        return IcingaHostStatus.CRITICAL.value

    if ci_alarms := Alarm.get_active_alarms_on_ci(
        session=session, agent_id=alarm.agent_id or 0, ci_id=alarm.ci_id or ""
    ):
        for ci_alarm in ci_alarms:
            if get_alarm_field(ci_alarm, "severity") in [Severity.MAJOR, Severity.CRITICAL]:
                return IcingaHostStatus.CRITICAL.value

    return IcingaHostStatus.OK.value


@CaptureSpan(MeasureType.CUSTOM.value)
def add_or_update_service(
    client: IcingaClient,
    alarm_id: int,
    service_name: str,
    encoded_service_name: str,
    host_name: str,
    service_details: dict,
    alarm_summary: str,
    alarm_status: int,
    operation: str,
    host_exceptions: list[dict | None],
) -> dict[str, Any]:
    """Add or update a service into Icinga and process its check result."""
    try:
        # If the host had an exception, raise an exception for the service also.
        if host_exceptions:
            if host_exception := get_process_error(host_name, host_exceptions):
                return {"id": alarm_id, "exception": host_exception}

        match operation:
            case "add":
                client.add(
                    IcingaObjectType.SERVICE, encoded_service_name, service_details | {"vars.missing_service": True}
                )
            case "update":
                client.update(IcingaObjectType.SERVICE, encoded_service_name, {"attrs": service_details})
            case "none":
                pass
            case _:
                raise ValueError("Unknown operation.")

        client.process_check_result(
            IcingaObjectType.SERVICE,
            service_name,
            alarm_summary,
            alarm_status,
        )

        return {"id": alarm_id, "exception": None}
    except Exception as e:
        return {"id": alarm_id, "exception": str(e)}


@CaptureSpan(MeasureType.CUSTOM.value)
def add_or_update_host(
    client: IcingaClient,
    host_name: str,
    encoded_host_name: str,
    host_details: dict,
    operation: str,
    ci_id: str,
    host_status: int,
) -> dict[str, Any]:
    """Add or update a service into Icinga and process its check result."""
    try:
        match operation:
            case "add":
                client.add(IcingaObjectType.HOST, encoded_host_name, host_details | {"vars.missing_host": True})
            case "update":
                client.update(IcingaObjectType.HOST, encoded_host_name, {"attrs": host_details})
            case "none":
                pass
            case _:
                raise ValueError("Unknown operation.")

        if operation != "none":
            client.process_check_result(
                IcingaObjectType.HOST,
                host_name,
                ci_id,
                host_status,
            )
        return {"id": host_name, "exception": None}
    except Exception as e:
        return {"id": host_name, "exception": str(e)}


def get_process_error(id: int | str, exceptions: list[dict | None]) -> str | None:
    """Get the process error corresponding to an alarm ID."""
    # It is possible that we don't have a list of dictionnaries, and our list returns us this [none]
    if not isinstance(exceptions[0], dict) and exceptions[0] is None:
        return None
    list_ids = [exception["id"] for exception in exceptions]
    idx = bisect_left(list_ids, id)
    if idx >= len(list_ids) or list_ids[idx] != id:
        return None

    return exceptions[idx]["exception"]
