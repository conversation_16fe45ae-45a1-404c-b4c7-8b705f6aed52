{"extra_env": {"OLYMPUS_AGENT_ID": "18"}, "agent_ids": [18], "data": [{"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.27645.5444.2.93", "SNMPv2-MIB::sysUpTime.0": "6 days, 15:53:18.17", "@version": "1", "SNMPv2-SMI::enterprises.27645.5444.10.3": "2", "host": "***********", "SNMPv2-SMI::enterprises.27645.5444.10.5": "IP11131M_iictyidblc032 (iictyidblc032.msnet.railb.be: 11131)", "@timestamp": "2025-02-13T03:30:38.222547893Z", "type": "snmp_trap", "SNMPv2-SMI::enterprises.27645.5444.10.11": "0", "SNMPv2-SMI::enterprises.27645.5444.10.10": "1", "SNMPv2-SMI::enterprises.27645.5444.10.1": "Check PEM heartbeat", "SNMPv2-SMI::enterprises.27645.5444.10.12": "HIGH", "message": "#<SNMP::SNMPv2_Trap:0x79c6c00c @varbind_list=[#<SNMP::VarBind:0x4e05561f @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x18446ac @value=57559817>>, #<SNMP::VarBind:0x4e0d2030 @name=[*******.*******.4.1.0], @value=[*******.4.1.27645.5444.2.93]>, #<SNMP::VarBind:0x1ab1efdb @name=[*******.4.1.27645.5444.10.1], @value=\"Check PEM heartbeat\">, #<SNMP::VarBind:0x7e60e73c @name=[*******.4.1.27645.5444.10.3], @value=\"2\">, #<SNMP::VarBind:0x5df26f7a @name=[*******.4.1.27645.5444.10.5], @value=\"IP11131M_iictyidblc032 (iictyidblc032.msnet.railb.be: 11131)\">, #<SNMP::VarBind:0x7ad9d854 @name=[*******.4.1.27645.5444.10.9], @value=\"{0.7,0.8,0.9}\">, #<SNMP::VarBind:0x6f033fe3 @name=[*******.4.1.27645.5444.10.10], @value=\"1\">, #<SNMP::VarBind:0x16a37c38 @name=[*******.4.1.27645.5444.10.11], @value=\"0\">, #<SNMP::VarBind:0x58644829 @name=[*******.4.1.27645.5444.10.12], @value=\"HIGH\">, #<SNMP::VarBind:0x6c4b2009 @name=[*******.4.1.27645.5444.10.13], @value=\"CLEAR\">, #<SNMP::VarBind:0x1a41f644 @name=[*******.4.1.27645.5444.10.14], @value=\"2025-02-13 04:30:28.155064+01\">], @request_id=7763, @error_index=0, @error_status=0, @source_ip=\"***********\">", "SNMPv2-SMI::enterprises.27645.5444.10.13": "CLEAR", "SNMPv2-SMI::enterprises.27645.5444.10.14": "2025-02-13 04:30:28.155064+01", "SNMPv2-SMI::enterprises.27645.5444.10.9": "{0.7,0.8,0.9}"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "PEM_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "PEM Heartbeat Message", "clear_time": null, "severity": 1, "additional_data": null}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.27645.5444.2.93", "SNMPv2-MIB::sysUpTime.0": "6 days, 16:05:29.11", "@version": "1", "SNMPv2-SMI::enterprises.27645.5444.10.3": "2", "host": "***********", "SNMPv2-SMI::enterprises.27645.5444.10.5": "IP11131M_iictyidblc032 (iictyidblc032.msnet.railb.be: 11131)", "@timestamp": "2025-02-13T03:42:49.161538699Z", "type": "snmp_trap", "SNMPv2-SMI::enterprises.27645.5444.10.11": "1", "SNMPv2-SMI::enterprises.27645.5444.10.10": "0", "SNMPv2-SMI::enterprises.27645.5444.10.1": "Check PEM heartbeat", "SNMPv2-SMI::enterprises.27645.5444.10.12": "CLEAR", "message": "#<SNMP::SNMPv2_Trap:0x409bd25 @varbind_list=[#<SNMP::VarBind:0x278c3367 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x5e8a0d7d @value=57632911>>, #<SNMP::VarBind:0x1723731c @name=[*******.*******.4.1.0], @value=[*******.4.1.27645.5444.2.93]>, #<SNMP::VarBind:0x4ffbaf17 @name=[*******.4.1.27645.5444.10.1], @value=\"Check PEM heartbeat\">, #<SNMP::VarBind:0x92aba84 @name=[*******.4.1.27645.5444.10.3], @value=\"2\">, #<SNMP::VarBind:0x7c5af39 @name=[*******.4.1.27645.5444.10.5], @value=\"IP11131M_iictyidblc032 (iictyidblc032.msnet.railb.be: 11131)\">, #<SNMP::VarBind:0xb72974e @name=[*******.4.1.27645.5444.10.9], @value=\"{0.7,0.8,0.9}\">, #<SNMP::VarBind:0x19631220 @name=[*******.4.1.27645.5444.10.10], @value=\"0\">, #<SNMP::VarBind:0x45417512 @name=[*******.4.1.27645.5444.10.11], @value=\"1\">, #<SNMP::VarBind:0x6f57b6e4 @name=[*******.4.1.27645.5444.10.12], @value=\"CLEAR\">, #<SNMP::VarBind:0x708094a7 @name=[*******.4.1.27645.5444.10.13], @value=\"HIGH\">, #<SNMP::VarBind:0x71737a85 @name=[*******.4.1.27645.5444.10.14], @value=\"2025-02-13 04:42:39.080267+01\">], @request_id=10883, @error_index=0, @error_status=0, @source_ip=\"***********\">", "SNMPv2-SMI::enterprises.27645.5444.10.13": "HIGH", "SNMPv2-SMI::enterprises.27645.5444.10.14": "2025-02-13 04:42:39.080267+01", "SNMPv2-SMI::enterprises.27645.5444.10.9": "{0.7,0.8,0.9}"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "PEM_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "PEM Heartbeat Message", "clear_time": null, "severity": 1, "additional_data": null}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.27645.5444.2.91", "SNMPv2-MIB::sysUpTime.0": "6 days, 20:23:45.47", "@version": "1", "SNMPv2-SMI::enterprises.27645.5444.10.3": "397", "host": "***********", "SNMPv2-SMI::enterprises.27645.5444.10.5": "IP1048FA_iictbidbls003 (iictbidbls003.msnet.railb.be: 10495)", "@timestamp": "2025-02-13T08:01:05.608004872Z", "type": "snmp_trap", "SNMPv2-SMI::enterprises.27645.5444.10.11": "1", "SNMPv2-SMI::enterprises.27645.5444.10.10": "0", "SNMPv2-SMI::enterprises.27645.5444.10.1": "Check PGAGENT status", "SNMPv2-SMI::enterprises.27645.5444.10.12": "CLEAR", "message": "#<SNMP::SNMPv2_Trap:0x7e3a4b32 @varbind_list=[#<SNMP::VarBind:0x63d168b6 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x1379b161 @value=59182547>>, #<SNMP::VarBind:0x3fb60be7 @name=[*******.*******.4.1.0], @value=[*******.4.1.27645.5444.2.91]>, #<SNMP::VarBind:0x29c4dae3 @name=[*******.4.1.27645.5444.10.1], @value=\"Check PGAGENT status\">, #<SNMP::VarBind:0x41b883b5 @name=[*******.4.1.27645.5444.10.3], @value=\"397\">, #<SNMP::VarBind:0x1f94c05a @name=[*******.4.1.27645.5444.10.5], @value=\"IP1048FA_iictbidbls003 (iictbidbls003.msnet.railb.be: 10495)\">, #<SNMP::VarBind:0x1072097c @name=[*******.4.1.27645.5444.10.9], @value=\"{0,1,2}\">, #<SNMP::VarBind:0x1feeca53 @name=[*******.4.1.27645.5444.10.10], @value=\"0\">, #<SNMP::VarBind:0x1ace4730 @name=[*******.4.1.27645.5444.10.11], @value=\"1\">, #<SNMP::VarBind:0x2361d223 @name=[*******.4.1.27645.5444.10.12], @value=\"CLEAR\">, #<SNMP::VarBind:0x235816da @name=[*******.4.1.27645.5444.10.13], @value=\"LOW\">, #<SNMP::VarBind:0x4a8decb0 @name=[*******.4.1.27645.5444.10.14], @value=\"2025-02-13 09:00:55.45943+01\">], @request_id=13483, @error_index=0, @error_status=0, @source_ip=\"***********\">", "SNMPv2-SMI::enterprises.27645.5444.10.13": "LOW", "SNMPv2-SMI::enterprises.27645.5444.10.14": "2025-02-13 09:00:55.45943+01", "SNMPv2-SMI::enterprises.27645.5444.10.9": "{0,1,2}"}, "output": {"s2110_alarm": [{"ci_id": "IP1048FA", "metric_name": "CheckPGAGENTstatus", "metric_type": "/PostgreSQLDatabase/", "event_id": null, "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "IP1048FA", "metric_name": "CheckPGAGENTstatus", "metric_type": "/PostgreSQLDatabase/", "summary": "Check PGAGENT status 2025-02-13 08:00:55.459430", "clear_time": null, "severity": 3, "raise_time": "2025-02-13 08:00:55.459430", "additional_data": null}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.27645.5444.2.87", "SNMPv2-MIB::sysUpTime.0": "7 days, 15:49:34.92", "@version": "1", "SNMPv2-SMI::enterprises.27645.5444.10.3": "314", "host": "***********", "SNMPv2-SMI::enterprises.27645.5444.10.5": "IP47340A_iictbidbls001 (iictbidbls001.msnet.railb.be: 47340)", "@timestamp": "2025-02-14T03:26:54.976405956Z", "type": "snmp_trap", "SNMPv2-SMI::enterprises.27645.5444.10.11": "96", "SNMPv2-SMI::enterprises.27645.5444.10.10": "92", "SNMPv2-SMI::enterprises.27645.5444.10.1": "Check BCK diskspace", "SNMPv2-SMI::enterprises.27645.5444.10.12": "MEDIUM", "message": "#<SNMP::SNMPv2_Trap:0xb86f103 @varbind_list=[#<SNMP::VarBind:0x5c7b5b28 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x1590c2af @value=66177492>>, #<SNMP::VarBind:0x29a237f7 @name=[*******.*******.4.1.0], @value=[*******.4.1.27645.5444.2.87]>, #<SNMP::VarBind:0x320830ad @name=[*******.4.1.27645.5444.10.1], @value=\"Check BCK diskspace\">, #<SNMP::VarBind:0x342c9fdf @name=[*******.4.1.27645.5444.10.3], @value=\"314\">, #<SNMP::VarBind:0x27baa64f @name=[*******.4.1.27645.5444.10.5], @value=\"IP47340A_iictbidbls001 (iictbidbls001.msnet.railb.be: 47340)\">, #<SNMP::VarBind:0x1f1f30c0 @name=[*******.4.1.27645.5444.10.9], @value=\"{85,90,95}\">, #<SNMP::VarBind:0x6c61f6d6 @name=[*******.4.1.27645.5444.10.10], @value=\"92\">, #<SNMP::VarBind:0x3ce34ee2 @name=[*******.4.1.27645.5444.10.11], @value=\"96\">, #<SNMP::VarBind:0x4e991fef @name=[*******.4.1.27645.5444.10.12], @value=\"MEDIUM\">, #<SNMP::VarBind:0x2866afcc @name=[*******.4.1.27645.5444.10.13], @value=\"HIGH\">, #<SNMP::VarBind:0x4bd22b50 @name=[*******.4.1.27645.5444.10.14], @value=\"2025-02-14 04:26:44.913648+01\">], @request_id=21266, @error_index=0, @error_status=0, @source_ip=\"***********\">", "SNMPv2-SMI::enterprises.27645.5444.10.13": "HIGH", "SNMPv2-SMI::enterprises.27645.5444.10.14": "2025-02-14 04:26:44.913648+01", "SNMPv2-SMI::enterprises.27645.5444.10.9": "{85,90,95}"}, "output": {"s2110_alarm": [{"ci_id": "IP47340A", "metric_name": "CheckBCKdiskspace", "metric_type": "/PostgreSQLDatabase/", "event_id": null, "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "IP47340A", "metric_name": "CheckBCKdiskspace", "metric_type": "/PostgreSQLDatabase/", "summary": "Check BCK diskspace 2025-02-14 03:26:44.913648", "clear_time": null, "severity": 5, "additional_data": null}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.27645.5444.2.87", "SNMPv2-MIB::sysUpTime.0": "7 days, 15:46:21.69", "@version": "1", "SNMPv2-SMI::enterprises.27645.5444.10.3": "325", "host": "***********", "SNMPv2-SMI::enterprises.27645.5444.10.5": "IP1048EA_iictzidblc021 (iictzidblc021: 10494)", "@timestamp": "2025-02-14T03:23:41.741032653Z", "type": "snmp_trap", "SNMPv2-SMI::enterprises.27645.5444.10.11": "91", "SNMPv2-SMI::enterprises.27645.5444.10.10": "89", "SNMPv2-SMI::enterprises.27645.5444.10.1": "Check BCK diskspace", "SNMPv2-SMI::enterprises.27645.5444.10.12": "LOW", "message": "#<SNMP::SNMPv2_Trap:0x4bfd3d4e @varbind_list=[#<SNMP::VarBind:0x54dacdf1 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x4ad77a7f @value=66158169>>, #<SNMP::VarBind:0x68e23926 @name=[*******.*******.4.1.0], @value=[*******.4.1.27645.5444.2.87]>, #<SNMP::VarBind:0x75d4f1f6 @name=[*******.4.1.27645.5444.10.1], @value=\"Check BCK diskspace\">, #<SNMP::VarBind:0x77e30ff8 @name=[*******.4.1.27645.5444.10.3], @value=\"325\">, #<SNMP::VarBind:0x69697f9 @name=[*******.4.1.27645.5444.10.5], @value=\"IP1048EA_iictzidblc021 (iictzidblc021: 10494)\">, #<SNMP::VarBind:0x7f44a504 @name=[*******.4.1.27645.5444.10.9], @value=\"{85,90,95}\">, #<SNMP::VarBind:0x2dcdb05b @name=[*******.4.1.27645.5444.10.10], @value=\"89\">, #<SNMP::VarBind:0x42bef6d5 @name=[*******.4.1.27645.5444.10.11], @value=\"91\">, #<SNMP::VarBind:0x70a58824 @name=[*******.4.1.27645.5444.10.12], @value=\"LOW\">, #<SNMP::VarBind:0x2a936fb1 @name=[*******.4.1.27645.5444.10.13], @value=\"MEDIUM\">, #<SNMP::VarBind:0x6a1c4dd0 @name=[*******.4.1.27645.5444.10.14], @value=\"2025-02-14 04:23:31.675554+01\">], @request_id=29739, @error_index=0, @error_status=0, @source_ip=\"***********\">", "SNMPv2-SMI::enterprises.27645.5444.10.13": "MEDIUM", "SNMPv2-SMI::enterprises.27645.5444.10.14": "2025-02-14 04:23:31.675554+01", "SNMPv2-SMI::enterprises.27645.5444.10.9": "{85,90,95}"}, "output": {"s2110_alarm": [{"ci_id": "IP1048EA", "metric_name": "CheckBCKdiskspace", "metric_type": "/PostgreSQLDatabase/", "event_id": null, "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "IP1048EA", "metric_name": "CheckBCKdiskspace", "metric_type": "/PostgreSQLDatabase/", "summary": "Check BCK diskspace 2025-02-14 03:23:31.675554", "clear_time": null, "severity": 4, "additional_data": null}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.27645.5444.2.87", "SNMPv2-MIB::sysUpTime.0": "7 days, 16:05:36.19", "@version": "1", "SNMPv2-SMI::enterprises.27645.5444.10.3": "325", "host": "***********", "SNMPv2-SMI::enterprises.27645.5444.10.5": "IP1048EA_iictzidblc021 (iictzidblc021: 10494)", "@timestamp": "2025-02-14T03:42:56.241674516Z", "type": "snmp_trap", "SNMPv2-SMI::enterprises.27645.5444.10.11": "68", "SNMPv2-SMI::enterprises.27645.5444.10.10": "94", "SNMPv2-SMI::enterprises.27645.5444.10.1": "Check BCK diskspace", "SNMPv2-SMI::enterprises.27645.5444.10.12": "MEDIUM", "message": "#<SNMP::SNMPv2_Trap:0x35153811 @varbind_list=[#<SNMP::VarBind:0x1aedf0b8 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x669254a6 @value=66273619>>, #<SNMP::VarBind:0x7adb2dd5 @name=[*******.*******.4.1.0], @value=[*******.4.1.27645.5444.2.87]>, #<SNMP::VarBind:0xe2421e0 @name=[*******.4.1.27645.5444.10.1], @value=\"Check BCK diskspace\">, #<SNMP::VarBind:0x7b3968ac @name=[*******.4.1.27645.5444.10.3], @value=\"325\">, #<SNMP::VarBind:0xf7a2409 @name=[*******.4.1.27645.5444.10.5], @value=\"IP1048EA_iictzidblc021 (iictzidblc021: 10494)\">, #<SNMP::VarBind:0x2c6c05b6 @name=[*******.4.1.27645.5444.10.9], @value=\"{85,90,95}\">, #<SNMP::VarBind:0x3e80afa @name=[*******.4.1.27645.5444.10.10], @value=\"94\">, #<SNMP::VarBind:0x33a46e23 @name=[*******.4.1.27645.5444.10.11], @value=\"68\">, #<SNMP::VarBind:0x6da04e32 @name=[*******.4.1.27645.5444.10.12], @value=\"MEDIUM\">, #<SNMP::VarBind:0x21ada785 @name=[*******.4.1.27645.5444.10.13], @value=\"CLEAR\">, #<SNMP::VarBind:0x62d1d690 @name=[*******.4.1.27645.5444.10.14], @value=\"2025-02-14 04:42:46.15727+01\">], @request_id=15238, @error_index=0, @error_status=0, @source_ip=\"***********\">", "SNMPv2-SMI::enterprises.27645.5444.10.13": "CLEAR", "SNMPv2-SMI::enterprises.27645.5444.10.14": "2025-02-14 04:42:46.15727+01", "SNMPv2-SMI::enterprises.27645.5444.10.9": "{85,90,95}"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "IP1048EA", "metric_name": "CheckBCKdiskspace", "metric_type": "/PostgreSQLDatabase/", "summary": "Check BCK diskspace 2025-02-14 03:42:46.157270", "clear_time": "2025-02-14 03:42:46.157270", "severity": 0, "raise_time": "2025-02-14 03:42:46.157270", "additional_data": null}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.27645.5444.2.87", "SNMPv2-MIB::sysUpTime.0": "7 days, 23:04:57.69", "@version": "1", "SNMPv2-SMI::enterprises.27645.5444.10.3": "314", "host": "***********", "SNMPv2-SMI::enterprises.27645.5444.10.5": "IP47340A_iictbidbls001 (iictbidbls001.msnet.railb.be: 47340)", "@timestamp": "2025-02-14T10:42:17.740240376Z", "type": "snmp_trap", "SNMPv2-SMI::enterprises.27645.5444.10.11": "72", "SNMPv2-SMI::enterprises.27645.5444.10.10": "99", "SNMPv2-SMI::enterprises.27645.5444.10.1": "Check BCK diskspace", "SNMPv2-SMI::enterprises.27645.5444.10.12": "HIGH", "message": "#<SNMP::SNMPv2_Trap:0x338e421c @varbind_list=[#<SNMP::VarBind:0x226aa3d9 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x7d02cbb5 @value=68789769>>, #<SNMP::VarBind:0x3c5df8ab @name=[*******.*******.4.1.0], @value=[*******.4.1.27645.5444.2.87]>, #<SNMP::VarBind:0x539c7eb2 @name=[*******.4.1.27645.5444.10.1], @value=\"Check BCK diskspace\">, #<SNMP::VarBind:0x241f7b73 @name=[*******.4.1.27645.5444.10.3], @value=\"314\">, #<SNMP::VarBind:0x4f2b4326 @name=[*******.4.1.27645.5444.10.5], @value=\"IP47340A_iictbidbls001 (iictbidbls001.msnet.railb.be: 47340)\">, #<SNMP::VarBind:0x1ce072c8 @name=[*******.4.1.27645.5444.10.9], @value=\"{85,90,95}\">, #<SNMP::VarBind:0x2cb9f608 @name=[*******.4.1.27645.5444.10.10], @value=\"99\">, #<SNMP::VarBind:0x73a2d40f @name=[*******.4.1.27645.5444.10.11], @value=\"72\">, #<SNMP::VarBind:0x66858b2e @name=[*******.4.1.27645.5444.10.12], @value=\"HIGH\">, #<SNMP::VarBind:0x79ecd304 @name=[*******.4.1.27645.5444.10.13], @value=\"CLEAR\">, #<SNMP::VarBind:0x1a067387 @name=[*******.4.1.27645.5444.10.14], @value=\"2025-02-14 11:42:07.677989+01\">], @request_id=5979, @error_index=0, @error_status=0, @source_ip=\"***********\">", "SNMPv2-SMI::enterprises.27645.5444.10.13": "CLEAR", "SNMPv2-SMI::enterprises.27645.5444.10.14": "2025-02-14 11:42:07.677989+01", "SNMPv2-SMI::enterprises.27645.5444.10.9": "{85,90,95}"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "IP47340A", "metric_name": "CheckBCKdiskspace", "metric_type": "/PostgreSQLDatabase/", "summary": "Check BCK diskspace 2025-02-14 10:42:07.677989", "clear_time": "2025-02-14 10:42:07.677989", "severity": 0, "raise_time": "2025-02-14 10:42:07.677989", "additional_data": null}]}}]}