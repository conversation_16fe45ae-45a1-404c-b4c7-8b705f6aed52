"""Patterns module for mon-scom.

This module contains all compiled regular expression patterns used in the mon-scom module.
All patterns are pre-compiled for better performance and organized by their usage context.
"""

import re

# Computer and Node Name Patterns

NETBIOS_MS_DOMAIN_PATTERN = re.compile(r"(.*)\.MS")
FQDN_MSNET_PATTERN = re.compile(r"\.MSNET\.")

# IIS and Web Service Patterns

IIS_DETECTION_PATTERN = re.compile(r"^(?:.* )?(?:IIS [0-9]+|Internet Information Services 2003) .+")
IIS_ALERT_KEY_EXTRACTION_PATTERN = re.compile(r"^(?:.* )?(?:IIS [0-9]+|Internet Information Services 2003) (.+)")

# Cluster and System Patterns

CLUSTER_ROLE_MOVE_PATTERN = re.compile(r"Clustered role .* is moving to cluster node")
SKYPE_DETECTION_PATTERN = re.compile(r"^\[Skype\]")

# Text Cleaning Patterns

TRAILING_WHITESPACE_PATTERN = re.compile(r"[ \t\.]+$")
EVENT_DESCRIPTION_PREFIX_PATTERN = re.compile(r"^Event Description: ")
NAMED_OBJECT_REPLACEMENT_PATTERN = re.compile(r"^(.* named )[^ ]+( .*)$")

# Disk and Storage Patterns

CLUSTER_DISK_ALERT_PATTERN = re.compile(r"^FREE SPACE \(%\) FOR CLUSTER DISK ALERT")
CLUSTER_DISK_NAME_EXTRACTION_PATTERN = re.compile(r"^(.*)_.*$")
DISK_GENERAL_PATTERN = re.compile(r"DISK")

# Process and Service Patterns

PROCESS_DETECTION_PATTERN = re.compile(r"PROCESS")
SERVICE_STOPPED_PATTERN = re.compile(r".*SERVICE STOPPED$")

# File Extension Patterns

VBS_FILE_PATTERN = re.compile(r"\.vbs")
JS_FILE_PATTERN = re.compile(r"\.js")
JAVA_FILE_PATTERN = re.compile(r"\.java")
BAT_FILE_PATTERN = re.compile(r"\.bat")
JAR_FILE_PATTERN = re.compile(r"\.jar")

# Platform Value Extraction Patterns

PLATFORM_NOLOGO_VBS_PATTERN = re.compile(r".*nologo(.*)\.vbs")
PLATFORM_NOLOGO_JS_PATTERN = re.compile(r".*nologo(.*)\.js")
PLATFORM_NOLOGO_JAVA_PATTERN = re.compile(r".*nologo(.*)\.java")
PLATFORM_NOLOGO_BAT_PATTERN = re.compile(r".*nologo(.*)\.bat")
PLATFORM_NOLOGO_JAR_PATTERN = re.compile(r".*nologo(.*)\.jar")
PLATFORM_VALUE_CLEANING_PATTERN = re.compile(r"^..(.*)")

# SCOM Specific Patterns

ACODE_EXTRACTION_PATTERN = re.compile(r"A\d{1,4}")

# MSSQL Patterns

MSSQL_LOG_FULL_PATTERN = re.compile(r"^MSSQL ON WINDOWS: DATABASE LOG FILE IS FULL")
MSSQL_DATABASE_PATTERN = re.compile(r"^MSSQL ON WINDOWS: DATABASE")
MSSQL_COMPUTER_EXTRACTION_PATTERN = re.compile(r"^([^\\]*)")
MSSQL_INSTANCE_EXTRACTION_PATTERN = re.compile(r"([^\\]*)$")
MSSQL_DATABASE_NAME_PATTERN = re.compile(r"database[ \\']*([^ \\']*)")
MSSQL_FULLNAME_COMPUTER_PATTERN = re.compile(r":([^\.]*)\.")
MSSQL_FULLNAME_INSTANCE_PATTERN = re.compile(r"([^\.]*);")
MSSQL_FULLNAME_DATABASE_PATTERN = re.compile(r";(.*)")

# Custom Application Patterns

CUSTOM_APP_FILECONTENT_PATTERN = re.compile(r"^CUSTOM APPLICATION FILECONTENT")
CUSTOM_APP_FILEWATCHER_PATTERN = re.compile(r"^CUSTOM APPLICATION FILEWATCHER")

# Additional Utility Patterns

IIS_WEB_SERVER_PLATFORM_PATTERN = re.compile(r".* -k (.*)$")
AD_CI_ID_PATTERN = re.compile(r"^(.*?) ")
SECURITY_HEALTH_NODE_PATTERN = re.compile(r"(.*)\.")
SERVICE_NAME_PATTERN = re.compile(r"^([A-Za-z0-9]+)$")


def create_description_part_pattern(part_name: str) -> re.Pattern[str]:
    """Create a compiled pattern for extracting description parts.

    Args:
        part_name: The name of the part to extract

    Returns
    -------
        Compiled regex pattern for the specific part name
    """
    return re.compile(f"::__{re.escape(part_name)}__=(.*?)::", re.DOTALL)


def extract_netbios_node(netbios_name: str) -> tuple[str, str, str]:
    """Extract node information from NetBIOS computer name.

    Args:
        netbios_name: The NetBIOS computer name

    Returns
    -------
        Tuple of (node, node_alias, ci_id)
    """
    netbios_upper = netbios_name.upper()
    if match := NETBIOS_MS_DOMAIN_PATTERN.search(netbios_upper):
        node = match.group(1)
        return node, node, node
    else:
        return netbios_upper, netbios_upper, netbios_upper


def extract_mssql_computer_instance(monitoring_object_display_name: str) -> tuple[str, str]:
    """Extract computer and instance from MSSQL monitoring object display name.

    Args:
        monitoring_object_display_name: The monitoring object display name

    Returns
    -------
        Tuple of (computer, instance)
    """
    computer_match = MSSQL_COMPUTER_EXTRACTION_PATTERN.search(monitoring_object_display_name)
    instance_match = MSSQL_INSTANCE_EXTRACTION_PATTERN.search(monitoring_object_display_name)

    computer = computer_match.group(1) if computer_match else ""
    instance = instance_match.group(1) if instance_match else ""

    return computer, instance


def extract_mssql_database_name(monitoring_object_display_name: str) -> str:
    """Extract database name from monitoring object display name.

    Args:
        monitoring_object_display_name: The monitoring object display name

    Returns
    -------
        The database name or empty string if not found
    """
    match = MSSQL_DATABASE_NAME_PATTERN.search(monitoring_object_display_name)
    return match.group(1) if match else ""


def extract_mssql_fullname_parts(monitoring_object_display_name: str) -> tuple[str, str, str]:
    """Extract computer, instance, and database from MSSQL full name.

    Args:
        monitoring_object_display_name: The monitoring object display name

    Returns
    -------
        Tuple of (computer, instance, database)
    """
    computer_match = MSSQL_FULLNAME_COMPUTER_PATTERN.search(monitoring_object_display_name)
    instance_match = MSSQL_FULLNAME_INSTANCE_PATTERN.search(monitoring_object_display_name)
    database_match = MSSQL_FULLNAME_DATABASE_PATTERN.search(monitoring_object_display_name)

    computer = computer_match.group(1) if computer_match else ""
    instance = instance_match.group(1) if instance_match else ""
    database = database_match.group(1) if database_match else ""

    return computer, instance, database


def clean_trailing_whitespace(text: str) -> str:
    """Remove trailing spaces, tabs, and dots from text.

    Args:
        text: The text to clean

    Returns
    -------
        Cleaned text
    """
    return TRAILING_WHITESPACE_PATTERN.sub("", text)


def extract_platform_value(context_param: str, file_pattern: re.Pattern[str]) -> str:
    """Extract platform value from context parameter using file pattern.

    Args:
        context_param: The context parameter string
        file_pattern: The compiled regex pattern for the file type

    Returns
    -------
        Extracted platform value or empty string
    """
    match = file_pattern.search(context_param)
    if match:
        return match.group(1)
    return ""
