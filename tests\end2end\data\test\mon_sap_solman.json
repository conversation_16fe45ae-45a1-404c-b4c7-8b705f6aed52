{"extra_env": {"OLYMPUS_AGENT_ID": "22"}, "agent_ids": [22], "data": [{"input": {"@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x7c7b9d0c @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x3c8a3cbc @value=0>, @varbind_list=[#<SNMP::VarBind:0x27e45acc @name=[*******.4.1.694.*******.1.1], @value=\"SAPYCP_HEARTBEAT\">, #<SNMP::VarBind:0x75720c1 @name=[*******.4.1.694.*******.1.2], @value=\"HEARTBEAT\">, #<SNMP::VarBind:0x6a55834b @name=[*******.4.1.694.*******.1.3], @value=\"45138DE8AB0B1ED6AAEC463B98DDDAC6\">, #<SNMP::VarBind:0x5c1472b1 @name=[*******.4.1.694.*******.1.4], @value=\"20010101\">, #<SNMP::VarBind:0x59a361c5 @name=[*******.4.1.694.*******.1.5], @value=\"123456\">, #<SNMP::VarBind:0x67cc1318 @name=[*******.4.1.694.*******.1.6], @value=\"SAPYCP_HEARTBEAT\">, #<SNMP::VarBind:0x21c58b71 @name=[*******.4.1.694.*******.1.7], @value=\"SAPYCP_HEARTBEAT\">, #<SNMP::VarBind:0x5e3bf402 @name=[*******.4.1.694.*******.1.8], @value=\"SAPYCP_HEARTBEAT\">, #<SNMP::VarBind:0x1cd41b49 @name=[*******.4.1.694.*******.1.9], @value=\"HEARTBEAT\">, #<SNMP::VarBind:0x1b879f18 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x1614011a @value=3>>, #<SNMP::VarBind:0x170e7c3e @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x2bbc33c5 @value=7>>, #<SNMP::VarBind:0xce416f5 @name=[*******.4.1.694.*******.1.12], @value=\"SAPYCP_HEARTBEAT\">, #<SNMP::VarBind:0x3a4edaa2 @name=[*******.4.1.694.*******.1.13], @value=\"SAPYCP_HEARTBEAT\">, #<SNMP::VarBind:0x5e45ae59 @name=[*******.4.1.694.*******.1.14], @value=\"4F16F070B85B18A3E10000000A0F0F2B\">, #<SNMP::VarBind:0x14ad9c4f @name=[*******.4.1.694.*******.1.15], @value=\"C\">, #<SNMP::VarBind:0x38d26391 @name=[*******.4.1.694.*******.1.16], @value=#<SNMP::Integer:0x748525e3 @value=3>>, #<SNMP::VarBind:0x7c17a4c @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x7f76d11f @value=4>>], @specific_trap=60, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x5493eb8b @value=\"\\n\\xF8\\f\\x01\">, @generic_trap=6>", "host": "***********", "type": "snmp_trap", "@timestamp": "2025-02-13T07:30:02.037015635Z", "snmptrap.r3maiAlertTechnicalName": "SAPYCP_HEARTBEAT", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertName": "SAPYCP_HEARTBEAT", "snmptrap.r3maiAlertMetricValue": "SAPYCP_HEARTBEAT", "snmptrap.r3maiAlertMetricName": "SAPYCP_HEARTBEAT", "snmptrap.r3maiAlertStatus": "C", "snmptrap.r3maiAlertReasonClosure": "3", "snmptrap.r3maiAlertTime": "123456", "snmptrap.r3maiAlertDate": "20010101", "snmptrap.r3maiAlertMOId": "4F16F070B85B18A3E10000000A0F0F2B", "snmptrap.r3maiAlertMOType": "HEARTBEAT", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertId": "45138DE8AB0B1ED6AAEC463B98DDDAC6", "snmptrap.r3maiAlertMOName": "SAPYCP_HEARTBEAT", "snmptrap.r3maiAlertCategory": "HEARTBEAT", "snmptrap.r3maiAlertDescription": "SAPYCP_HEARTBEAT", "event.kafka.timestamp": "2025-02-13T07:30:02.142Z", "event.kafka.offset": 36368, "event.kafka.key": null, "event.kafka.topic": "a1846-sap_solman-events-prd", "event.kafka.partition": 2, "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-prd", "event.logstash.instance_name": "iictniapls016", "event.uuid": "7b39f2b2-8b9e-4903-a7f5-e73d21c77386"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "Solman_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "SAP probe: SAPYCP Heartbeat Message", "clear_time": null, "severity": 1, "raise_time": "2025-02-13 07:30:02.037015", "additional_data": {"additional_info": "SAPYCP_HEARTBEAT.SAPYCP_HEARTBEAT"}}]}}, {"input": {"@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x30d2942b @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x55e3a6c4 @value=1854267088>, @varbind_list=[#<SNMP::VarBind:0xa5b8fe9 @name=[*******.4.1.694.*******.1.1], @value=\"YCP~WEBDISP\">, #<SNMP::VarBind:0x2e8c2145 @name=[*******.4.1.694.*******.1.2], @value=\"T_SYSTEM\">, #<SNMP::VarBind:0x661177d6 @name=[*******.4.1.694.*******.1.3], @value=\"48DF37CF5C001EEFBAE6DA2197BB2034\">, #<SNMP::VarBind:0x6d372908 @name=[*******.4.1.694.*******.1.4], @value=\"20250215\">, #<SNMP::VarBind:0x3b45d780 @name=[*******.4.1.694.*******.1.5], @value=\"005013\">, #<SNMP::VarBind:0x748c4fa4 @name=[*******.4.1.694.*******.1.6], @value=\"Web Dispatcher not available\">, #<SNMP::VarBind:0x19ce3924 @name=[*******.4.1.694.*******.1.7], @value=\"WEBDISP_AVAILABLITY\">, #<SNMP::VarBind:0x4de52c7c @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x58890b97 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x4eee8e4d @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x268199ad @value=3>>, #<SNMP::VarBind:0x4263d7cd @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x5226b9a5 @value=7>>, #<SNMP::VarBind:0x6880d94a @name=[*******.4.1.694.*******.1.12], @value=\"Web Dispatcher Status\">, #<SNMP::VarBind:0x42e67906 @name=[*******.4.1.694.*******.1.13], @value=\"Cannot connect to service sapstartsrv\">, #<SNMP::VarBind:0x1e670328 @name=[*******.4.1.694.*******.1.14], @value=\"2298101818031ED9BBD1E4CA7AD6845C\">, #<SNMP::VarBind:0x796b6ad0 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x17cfa63e @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x6610d388 @value=4>>], @specific_trap=60, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x40c8e221 @value=\"\\n\\xF8\\x16\\x1D\">, @generic_trap=6>", "host": "***********", "type": "snmp_trap", "@timestamp": "2025-02-15T00:50:14.498674056Z", "snmptrap.r3maiAlertTechnicalName": "WEBDISP_AVAILABLITY", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertName": "Web Dispatcher not available", "snmptrap.r3maiAlertMetricValue": "Cannot connect to service sapstartsrv", "snmptrap.r3maiAlertMetricName": "Web Dispatcher Status", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertReasonClosure": null, "snmptrap.r3maiAlertTime": "005013", "snmptrap.r3maiAlertDate": "20250215", "snmptrap.r3maiAlertMOId": "2298101818031ED9BBD1E4CA7AD6845C", "snmptrap.r3maiAlertMOType": "T_SYSTEM", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertId": "48DF37CF5C001EEFBAE6DA2197BB2034", "snmptrap.r3maiAlertMOName": "YCP~WEBDISP", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertDescription": "n/a", "event.kafka.timestamp": "2025-02-15T00:50:14.599Z", "event.kafka.offset": 36722, "event.kafka.key": null, "event.kafka.topic": "a1846-sap_solman-events-prd", "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-prd", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "e2ccefa6-14a4-4ae1-a831-4649d179c0a6"}, "output": {"s2110_alarm": [{"ci_id": "YCP", "metric_name": "WEBDISP_AVAILABLITY", "metric_type": "/SAPEvent/", "event_id": "2298101818031ED9BBD1E4CA7AD6845C", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "YCP", "metric_name": "WEBDISP_AVAILABLITY", "metric_type": "/SAPEvent/", "summary": "Web Dispatcher not available.Cannot connect to service sapstartsrv", "clear_time": null, "severity": 5}]}}, {"input": {"@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x5ba1cf2d @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x62426723 @value=1880187670>, @varbind_list=[#<SNMP::VarBind:0x4c9a7bd2 @name=[*******.4.1.694.*******.1.1], @value=\"YCP~ABAP~Central Service Instance\">, #<SNMP::VarBind:0x19171dcb @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x681d0ab5 @name=[*******.4.1.694.*******.1.3], @value=\"D4F5EF1446101EEFBAE6DA26AE19B19A\">, #<SNMP::VarBind:0x51382318 @name=[*******.4.1.694.*******.1.4], @value=\"20250215\">, #<SNMP::VarBind:0x4e638606 @name=[*******.4.1.694.*******.1.5], @value=\"005013\">, #<SNMP::VarBind:0x6fbb3344 @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Central Service not available\">, #<SNMP::VarBind:0x6ff3d4a4 @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_SCS_MESSAGE_SERVER\">, #<SNMP::VarBind:0x7f431aa1 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x2d42e0f9 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x1bd5c706 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x6a72662c @value=3>>, #<SNMP::VarBind:0xd03ffd1 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x37fc93a0 @value=9>>, #<SNMP::VarBind:0x2a49fae @name=[*******.4.1.694.*******.1.12], @value=\"ABAP Enqueue Server Status\">, #<SNMP::VarBind:0x7b9edad0 @name=[*******.4.1.694.*******.1.13], @value=\"Cannot connect to service sapstartsrv\">, #<SNMP::VarBind:0x69715f05 @name=[*******.4.1.694.*******.1.14], @value=\"66DA43C154031ED9BAE5CC1C3068834B\">, #<SNMP::VarBind:0x3a43a018 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x1c3c1111 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x11f9001c @value=4>>], @specific_trap=60, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x7293ec51 @value=\"\\n\\xF8\\x16\\x1C\">, @generic_trap=6>", "host": "***********", "type": "snmp_trap", "@timestamp": "2025-02-15T00:50:14.858482508Z", "snmptrap.r3maiAlertTechnicalName": "ABAP_SCS_MESSAGE_SERVER", "snmptrap.r3maiAlertSeverity": "9", "snmptrap.r3maiAlertName": "ABAP Central Service not available", "snmptrap.r3maiAlertMetricValue": "Cannot connect to service sapstartsrv", "snmptrap.r3maiAlertMetricName": "ABAP Enqueue Server Status", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertReasonClosure": null, "snmptrap.r3maiAlertTime": "005013", "snmptrap.r3maiAlertDate": "20250215", "snmptrap.r3maiAlertMOId": "66DA43C154031ED9BAE5CC1C3068834B", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertId": "D4F5EF1446101EEFBAE6DA26AE19B19A", "snmptrap.r3maiAlertMOName": "YCP~ABAP~Central Service Instance", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertDescription": "n/a", "event.kafka.timestamp": "2025-02-15T00:50:14.960Z", "event.kafka.offset": 36723, "event.kafka.key": null, "event.kafka.topic": "a1846-sap_solman-events-prd", "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-prd", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "a83b52bf-9691-4395-977a-33db3872c0ea"}, "output": {"s2110_alarm": [{"ci_id": "YCP", "metric_name": "ABAP_SCS_MESSAGE_SERVER", "metric_type": "/SAPEvent/", "event_id": "66DA43C154031ED9BAE5CC1C3068834B", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "YCP", "metric_name": "ABAP_SCS_MESSAGE_SERVER", "metric_type": "/SAPEvent/", "summary": "ABAP Central Service not available.Cannot connect to service sapstartsrv", "clear_time": null, "severity": 5}]}}, {"input": {"@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x62d72d02 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x8c39930 @value=1854273175>, @varbind_list=[#<SNMP::VarBind:0x6ff1a9ed @name=[*******.4.1.694.*******.1.1], @value=\"YCP~WEBDISP\">, #<SNMP::VarBind:0x3415262c @name=[*******.4.1.694.*******.1.2], @value=\"T_SYSTEM\">, #<SNMP::VarBind:0x200b3387 @name=[*******.4.1.694.*******.1.3], @value=\"48DF37CF5C001EEFBAE6DA2197BB2034\">, #<SNMP::VarBind:0x3409beaa @name=[*******.4.1.694.*******.1.4], @value=\"20250215\">, #<SNMP::VarBind:0x5655d707 @name=[*******.4.1.694.*******.1.5], @value=\"005013\">, #<SNMP::VarBind:0x4becbfa7 @name=[*******.4.1.694.*******.1.6], @value=\"Web Dispatcher not available\">, #<SNMP::VarBind:0x32023a56 @name=[*******.4.1.694.*******.1.7], @value=\"WEBDISP_AVAILABLITY\">, #<SNMP::VarBind:0x4b073e52 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x331235da @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x1dadb598 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x3edeecf1 @value=3>>, #<SNMP::VarBind:0x4c59a0d6 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x6ec7e062 @value=7>>, #<SNMP::VarBind:0x1eb68957 @name=[*******.4.1.694.*******.1.12], @value=\"Web Dispatcher Status\">, #<SNMP::VarBind:0x57464714 @name=[*******.4.1.694.*******.1.13], @value=\"Cannot connect to service sapstartsrv\">, #<SNMP::VarBind:0x5618123a @name=[*******.4.1.694.*******.1.14], @value=\"2298101818031ED9BBD1E4CA7AD6845C\">, #<SNMP::VarBind:0x70d3c6b @name=[*******.4.1.694.*******.1.15], @value=\"C\">, #<SNMP::VarBind:0x2ddbf1ec @name=[*******.4.1.694.*******.1.16], @value=#<SNMP::Integer:0x48406da4 @value=3>>, #<SNMP::VarBind:0x5edf3f54 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x305fae80 @value=4>>], @specific_trap=60, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x42f97df6 @value=\"\\n\\xF8\\x16 \">, @generic_trap=6>", "host": "***********", "type": "snmp_trap", "@timestamp": "2025-02-15T00:51:15.382191424Z", "snmptrap.r3maiAlertTechnicalName": "WEBDISP_AVAILABLITY", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertName": "Web Dispatcher not available", "snmptrap.r3maiAlertMetricValue": "Cannot connect to service sapstartsrv", "snmptrap.r3maiAlertMetricName": "Web Dispatcher Status", "snmptrap.r3maiAlertStatus": "C", "snmptrap.r3maiAlertReasonClosure": "3", "snmptrap.r3maiAlertTime": "005013", "snmptrap.r3maiAlertDate": "20250215", "snmptrap.r3maiAlertMOId": "2298101818031ED9BBD1E4CA7AD6845C", "snmptrap.r3maiAlertMOType": "T_SYSTEM", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertId": "48DF37CF5C001EEFBAE6DA2197BB2034", "snmptrap.r3maiAlertMOName": "YCP~WEBDISP", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertDescription": "n/a", "event.kafka.timestamp": "2025-02-15T00:51:15.482Z", "event.kafka.offset": 36724, "event.kafka.key": null, "event.kafka.topic": "a1846-sap_solman-events-prd", "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-prd", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "2e69c409-b71d-4a45-b029-b5145914c81e"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "YCP", "metric_name": "WEBDISP_AVAILABLITY", "metric_type": "/SAPEvent/", "summary": "Web Dispatcher not available.Cannot connect to service sapstartsrv", "clear_time": "2025-01-01 12:00:01", "severity": 0, "raise_time": "2025-01-01 12:00:01", "additional_data": {"additional_info": "Web Dispatcher not available.Cannot connect to service sapstartsrv"}}]}}, {"input": {"@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x91f1540 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x2e1dbea8 @value=1854273201>, @varbind_list=[#<SNMP::VarBind:0x6ae4e911 @name=[*******.4.1.694.*******.1.1], @value=\"YCP~ABAP~Central Service Instance\">, #<SNMP::VarBind:0x1b204418 @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x588b512a @name=[*******.4.1.694.*******.1.3], @value=\"D4F5EF1446101EEFBAE6DA26AE19B19A\">, #<SNMP::VarBind:0x62b85578 @name=[*******.4.1.694.*******.1.4], @value=\"20250215\">, #<SNMP::VarBind:0x2f59945f @name=[*******.4.1.694.*******.1.5], @value=\"005013\">, #<SNMP::VarBind:0x36401528 @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Central Service not available\">, #<SNMP::VarBind:0x344010cb @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_SCS_MESSAGE_SERVER\">, #<SNMP::VarBind:0x14f390ae @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x77eb1503 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x4134a783 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x75c62126 @value=3>>, #<SNMP::VarBind:0x7db8b9d0 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x52d4f869 @value=9>>, #<SNMP::VarBind:0x3638450f @name=[*******.4.1.694.*******.1.12], @value=\"ABAP Message Server Status\">, #<SNMP::VarBind:0x72b38dff @name=[*******.4.1.694.*******.1.13], @value=\"Cannot connect to service sapstartsrv\">, #<SNMP::VarBind:0x4a7ddc40 @name=[*******.4.1.694.*******.1.14], @value=\"66DA43C154031ED9BAE5CC1C3068834B\">, #<SNMP::VarBind:0x3f9f72d6 @name=[*******.4.1.694.*******.1.15], @value=\"C\">, #<SNMP::VarBind:0x4bfb4817 @name=[*******.4.1.694.*******.1.16], @value=#<SNMP::Integer:0x2549f8e2 @value=3>>, #<SNMP::VarBind:0x172afd7 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x453f64ed @value=4>>], @specific_trap=60, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x549527ad @value=\"\\n\\xF8\\x16 \">, @generic_trap=6>", "host": "***********", "type": "snmp_trap", "@timestamp": "2025-02-15T00:51:15.524915767Z", "snmptrap.r3maiAlertTechnicalName": "ABAP_SCS_MESSAGE_SERVER", "snmptrap.r3maiAlertSeverity": "9", "snmptrap.r3maiAlertName": "ABAP Central Service not available", "snmptrap.r3maiAlertMetricValue": "Cannot connect to service sapstartsrv", "snmptrap.r3maiAlertMetricName": "ABAP Message Server Status", "snmptrap.r3maiAlertStatus": "C", "snmptrap.r3maiAlertReasonClosure": "3", "snmptrap.r3maiAlertTime": "005013", "snmptrap.r3maiAlertDate": "20250215", "snmptrap.r3maiAlertMOId": "66DA43C154031ED9BAE5CC1C3068834B", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertId": "D4F5EF1446101EEFBAE6DA26AE19B19A", "snmptrap.r3maiAlertMOName": "YCP~ABAP~Central Service Instance", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertDescription": "n/a", "event.kafka.timestamp": "2025-02-15T00:51:15.625Z", "event.kafka.offset": 36725, "event.kafka.key": null, "event.kafka.topic": "a1846-sap_solman-events-prd", "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-prd", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "8b89747a-0a67-405a-a99b-8b99c9bb3f8c"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "YCP", "metric_name": "ABAP_SCS_MESSAGE_SERVER", "metric_type": "/SAPEvent/", "summary": "ABAP Central Service not available.Cannot connect to service sapstartsrv", "clear_time": "2025-01-01 12:00:01", "severity": 0, "raise_time": "2025-01-01 12:00:01", "additional_data": {"additional_info": "ABAP Central Service not available.Cannot connect to service sapstartsrv"}}]}}, {"input": {"@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x7f1c0916 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x2c779865 @value=1910417777>, @varbind_list=[#<SNMP::VarBind:0x4e352a21 @name=[*******.4.1.694.*******.1.1], @value=\"YCP~WEBDISP\">, #<SNMP::VarBind:0x51fede48 @name=[*******.4.1.694.*******.1.2], @value=\"T_SYSTEM\">, #<SNMP::VarBind:0x41dad0a2 @name=[*******.4.1.694.*******.1.3], @value=\"D4F5EF1446101EEFBBBED54E65B72621\">, #<SNMP::VarBind:0x7a25b17c @name=[*******.4.1.694.*******.1.4], @value=\"20250218\">, #<SNMP::VarBind:0x1d26f83b @name=[*******.4.1.694.*******.1.5], @value=\"124816\">, #<SNMP::VarBind:0x3a8d6949 @name=[*******.4.1.694.*******.1.6], @value=\"Web Dispatcher not available\">, #<SNMP::VarBind:0x2dc5e17a @name=[*******.4.1.694.*******.1.7], @value=\"WEBDISP_AVAILABLITY\">, #<SNMP::VarBind:0x7a3c0bef @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x50f1d3b3 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x1e72fc5 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x3abd0b2 @value=3>>, #<SNMP::VarBind:0x19d2d05b @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x47e0e499 @value=7>>, #<SNMP::VarBind:0x7f4162a7 @name=[*******.4.1.694.*******.1.12], @value=\"Web Dispatcher Status\">, #<SNMP::VarBind:0x22b213bd @name=[*******.4.1.694.*******.1.13], @value=\"Cannot connect to service sapstartsrv\">, #<SNMP::VarBind:0x2ded4fb8 @name=[*******.4.1.694.*******.1.14], @value=\"2298101818031ED9BBD1E4CA7AD6845C\">, #<SNMP::VarBind:0x6de9a2b0 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x1949b1f0 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x2bd4669f @value=4>>], @specific_trap=60, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x6c036cd2 @value=\"\\n\\xF8\\x16\\x1A\">, @generic_trap=6>", "host": "***********", "type": "snmp_trap", "@timestamp": "2025-02-18T12:48:35.923550274Z", "snmptrap.r3maiAlertTechnicalName": "WEBDISP_AVAILABLITY", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertName": "Web Dispatcher not available", "snmptrap.r3maiAlertMetricValue": "Cannot connect to service sapstartsrv", "snmptrap.r3maiAlertMetricName": "Web Dispatcher Status", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertReasonClosure": null, "snmptrap.r3maiAlertTime": "124816", "snmptrap.r3maiAlertDate": "20250218", "snmptrap.r3maiAlertMOId": "2298101818031ED9BBD1E4CA7AD6845C", "snmptrap.r3maiAlertMOType": "T_SYSTEM", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertId": "D4F5EF1446101EEFBBBED54E65B72621", "snmptrap.r3maiAlertMOName": "YCP~WEBDISP", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertDescription": "n/a", "event.kafka.timestamp": "2025-02-18T12:48:36.024Z", "event.kafka.offset": 37053, "event.kafka.key": null, "event.kafka.topic": "a1846-sap_solman-events-prd", "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-prd", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "883a8029-9afb-4ec1-b69d-700c678186fc"}, "output": {"s2110_occurrence": [{"event_type": "problem", "ci_id": "YCP", "metric_name": "WEBDISP_AVAILABLITY", "metric_type": "/SAPEvent/", "summary": "Web Dispatcher not available.Cannot connect to service sapstartsrv", "clear_time": null, "severity": 5}]}, "comment": "Problem already occurred once, do not expect another alarm entry."}, {"frozen_time": "2025-01-01 13:00:01", "input": {"@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x4c5fafc0 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x18cd518a @value=0>, @varbind_list=[#<SNMP::VarBind:0x67b7b7b8 @name=[*******.4.1.694.*******.1.1], @value=\"YCP~WEBDISP\">, #<SNMP::VarBind:0x56272521 @name=[*******.4.1.694.*******.1.2], @value=\"T_SYSTEM\">, #<SNMP::VarBind:0x24146f6e @name=[*******.4.1.694.*******.1.3], @value=\"D4F5EF1446101EEFBBBED54E65B72621\">, #<SNMP::VarBind:0x1d6c96c2 @name=[*******.4.1.694.*******.1.4], @value=\"20250218\">, #<SNMP::VarBind:0x473ce8a0 @name=[*******.4.1.694.*******.1.5], @value=\"124816\">, #<SNMP::VarBind:0x285dc34b @name=[*******.4.1.694.*******.1.6], @value=\"Web Dispatcher not available\">, #<SNMP::VarBind:0x2fb5b631 @name=[*******.4.1.694.*******.1.7], @value=\"WEBDISP_AVAILABLITY\">, #<SNMP::VarBind:0x5616ff83 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x76cf7409 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x304ea600 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x32488a06 @value=3>>, #<SNMP::VarBind:0x53c3912b @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x5ea1d866 @value=7>>, #<SNMP::VarBind:0x1b1a1b14 @name=[*******.4.1.694.*******.1.12], @value=\"Web Dispatcher Status\">, #<SNMP::VarBind:0x6df4bacb @name=[*******.4.1.694.*******.1.13], @value=\"Cannot connect to service sapstartsrv\">, #<SNMP::VarBind:0x22712497 @name=[*******.4.1.694.*******.1.14], @value=\"2298101818031ED9BBD1E4CA7AD6845C\">, #<SNMP::VarBind:0x72c3aa57 @name=[*******.4.1.694.*******.1.15], @value=\"C\">, #<SNMP::VarBind:0x39cacda0 @name=[*******.4.1.694.*******.1.16], @value=#<SNMP::Integer:0x71a00ab0 @value=3>>, #<SNMP::VarBind:0x5a5ecd8f @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x35205581 @value=4>>], @specific_trap=60, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x5ae83948 @value=\"\\n\\xF8\\f\\b\">, @generic_trap=6>", "host": "***********", "type": "snmp_trap", "@timestamp": "2025-02-18T12:49:19.700339525Z", "snmptrap.r3maiAlertTechnicalName": "WEBDISP_AVAILABLITY", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertName": "Web Dispatcher not available", "snmptrap.r3maiAlertMetricValue": "Cannot connect to service sapstartsrv", "snmptrap.r3maiAlertMetricName": "Web Dispatcher Status", "snmptrap.r3maiAlertStatus": "C", "snmptrap.r3maiAlertReasonClosure": "3", "snmptrap.r3maiAlertTime": "124816", "snmptrap.r3maiAlertDate": "20250218", "snmptrap.r3maiAlertMOId": "2298101818031ED9BBD1E4CA7AD6845C", "snmptrap.r3maiAlertMOType": "T_SYSTEM", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertId": "D4F5EF1446101EEFBBBED54E65B72621", "snmptrap.r3maiAlertMOName": "YCP~WEBDISP", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertDescription": "n/a", "event.kafka.timestamp": "2025-02-18T12:49:19.800Z", "event.kafka.offset": 37054, "event.kafka.key": null, "event.kafka.topic": "a1846-sap_solman-events-prd", "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-prd", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "131984c2-89d7-47b7-aa01-1f76d81c7b8d"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "YCP", "metric_name": "WEBDISP_AVAILABLITY", "metric_type": "/SAPEvent/", "summary": "Web Dispatcher not available.Cannot connect to service sapstartsrv", "clear_time": "2025-01-01 13:00:01", "severity": 0, "raise_time": "2025-01-01 13:00:01", "additional_data": {"additional_info": "Web Dispatcher not available.Cannot connect to service sapstartsrv"}}]}}]}