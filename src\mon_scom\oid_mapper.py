"""OID mapping for SCOM."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for SCOM."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "TimeRaised": "TimeRaised",
            "TimeResolved": "TimeResolved",
            "ResolutionState": "ResolutionState",
            "Name": "Name",
            "Severity": "Severity",
            "Priority": "Priority",
            "Id": "Id",
            "NetbiosComputerName": "NetbiosComputerName",
            "Description": "Description",
            "DisplayString": "DisplayString",
            "MonitoringObjectDisplayName": "MonitoringObjectDisplayName",
            "MonitoringObjectHealthState": "MonitoringObjectHealthState",
            "Context": "Context",
            "LastModified": "LastModified",
            "Category": "Category",
        }
