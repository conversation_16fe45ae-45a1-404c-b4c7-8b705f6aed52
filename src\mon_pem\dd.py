"""Detail design implementation for mon-pem."""

import logging
from datetime import datetime, timezone

import pandas as pd

from mon_pem import statics
from olympus_common import enums, utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Apply all transformations to the data and return it.

    The logic here is outlined in pem-dd.
    """
    df = _clean_raw_data(df)
    _transform(df, agent_id)
    return df


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> None:
    logging.debug("Starting _transform.")

    # Hardcoded values
    df["agent_id"] = agent_id
    df["manager"] = _manager()
    df["action_class"] = _action_class()
    df["clear_type"] = _clear_type()
    df["handle_time"] = utils.now_naive()

    # Applied values
    df["raise_time"] = df.apply(_raise_time, axis=1)
    df["severity"] = df.apply(_severity, axis=1)
    df["ci_id"] = df.apply(_ci_id, axis=1)
    df["metric_name"] = df.apply(_metric_name, axis=1)
    df["metric_type"] = df.apply(_metric_type, axis=1)
    df["event_type"] = df.apply(_event_type, axis=1)
    df["clear_time"] = df.apply(_clear_time, axis=1)
    df["summary"] = df.apply(_summary, axis=1)
    df["actionable_alarm"] = df.apply(_actionable_alarm, axis=1)
    df["node"] = df.apply(_node, axis=1)
    df["wake_up_time"] = df.apply(_wake_up_time, axis=1)

    logging.debug("finished _transform")


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _is_heartbeat_message(row: pd.Series) -> bool:
    """Determine if the message is a heartbeat."""
    return row["SNMPv2-SMI::enterprises.27645.5444.10.1"] == "Check PEM heartbeat"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Return the row's metric_name.

    Any spaces are removed from the original metric_name.
    """
    if _is_heartbeat_message(row):
        return "Heartbeat"

    metric_name: str = row["SNMPv2-SMI::enterprises.27645.5444.10.1"]
    return metric_name.replace(" ", "")


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Return the row's ci_id.

    We always split the ci_id on underscores ("_").
    If the ci_id starts with PEMAGT, we return the last part of the split ci_id, prepended with PEMAGT.
    Otherwise we return the first part of the split ci_id without any further modifications.
    """
    if _is_heartbeat_message(row):
        return "PEM_HB"

    ci_id: str = row["ci_id_raw"]
    ci_id_split = ci_id.split("_")
    if ci_id.startswith("PEMAGT"):
        final_ci_id_part = ci_id_split[-1]
        return f"PEMAGT_{final_ci_id_part}"
    else:
        return ci_id_split[0].strip()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the manager."""
    return "mon-pem"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime:
    """Return the row's raise_time.

    Since the format is something like:
    - "2024-08-13 05:44:46.803451+01", or
    - "2024-08-13 04:58:57.439841+00",

    We add "00" at the end so we can use the %z option in strptime.
    """
    timestamp_str = row["SNMPv2-SMI::enterprises.27645.5444.10.14"] + "00"
    timestamp_tz = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S.%f%z")
    return timestamp_tz.astimezone(timezone.utc).replace(tzinfo=None)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _action_class() -> str:
    """Return the action_class."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type(row: pd.Series) -> str:
    """Return the metric type."""
    if _is_heartbeat_message(row):
        return "/ApplicationEvent/"

    return "/PostgreSQLDatabase/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Return the row's severity.

    The alert value possible are HIGH (red), LOW (yellow), MEDIUM (orange) or CLEAR severity warning.
    """
    if _is_heartbeat_message(row):
        return enums.Severity.INDETERMINATE.value

    match str(row["SNMPv2-SMI::enterprises.27645.5444.10.13"]).upper():
        case "CLEAR":
            return enums.Severity.CLEARED.value
        case "LOW":
            return enums.Severity.MINOR.value
        case "MEDIUM":
            return enums.Severity.MAJOR.value
        case "HIGH":
            return enums.Severity.CRITICAL.value
        case _:
            return enums.Severity.INDETERMINATE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_type(row: pd.Series) -> str:
    """Return the row's event type.

    The event type is based on the row's severity. When it's CLEARED, the type will be RESOLUTION, PROBLEM otherwise.
    """
    if _is_heartbeat_message(row):
        return enums.AlarmType.HEARTBEAT.value

    severity = _severity(row)
    if severity == enums.Severity.CLEARED.value:
        return enums.AlarmType.RESOLUTION.value
    else:
        return enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime | None:
    """Return the row's clear_time.

    If the event_type is RESOLUTION, the _raise time is used. None otherwise.
    """
    if _event_type(row) == enums.AlarmType.RESOLUTION.value:
        return _raise_time(row)

    return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series):
    """Return the summary.

    The summary is the column that maps to metric_name without modifications plus the raise_time.
    """
    if _is_heartbeat_message(row):
        return "PEM Heartbeat Message"

    return row["SNMPv2-SMI::enterprises.27645.5444.10.1"] + " " + str(_raise_time(row))


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm(row: pd.Series) -> bool | None:
    """Return if the event is actionable."""
    if _severity(row) == enums.Severity.INDETERMINATE.value and _event_type(row) != enums.AlarmType.RESOLUTION.value:
        return False
    else:
        return None  # need enrichment


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type() -> str:
    """Return the clear_type for this DD module."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Return the node of the event."""
    return row["host"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime:
    return _raise_time(row)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clean_raw_data(original: pd.DataFrame) -> pd.DataFrame:
    """Create a subset of the original dataframe to keep only relevant columns.

    The columns are renamed to have a more human-readable name.

    Notes
    -----
    There are multiple keys that can contain the ci_id. If one key has the ci_id, the other key does not.
    We combine the ci_id fields into a single ci_id_raw and drop the original ci_id fields.
    The name is not `ci_id` but `ci_id_raw` to avoid name conflicts with the `ci_id` field in the transformed dataframe.
    """
    df = original.reindex(statics.COLUMNS_TO_KEEP, axis=1)
    ci_id_columns = ["SNMPv2-SMI::enterprises.27645.5444.10.4", "SNMPv2-SMI::enterprises.27645.5444.10.5"]
    df["ci_id_raw"] = df[ci_id_columns[0]].combine_first(df[ci_id_columns[1]])
    df = df.drop(columns=ci_id_columns)
    return df
