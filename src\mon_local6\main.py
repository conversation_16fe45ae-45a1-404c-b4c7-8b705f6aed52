"""Entrypoint for the application."""

from mon_local6 import dd
from mon_local6.config import config
from mon_local6.oid_mapper import OIDMapper
from olympus_common import defaults

application = defaults.databasewriter_kafkareader_app(config)


@application.run_forever(config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Execute the main function for when the project is run."""
    return defaults.transform(data, config, dd.run, oid_mapper=OIDMapper)
