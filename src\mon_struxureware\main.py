"""Entrypoint for the application."""

from mon_struxureware import dd
from mon_struxureware.config import config
from mon_struxureware.oid_mapper import OIDMapper
from olympus_common import defaults

application = defaults.databasewriter_kafkareader_app(config)


@application.run_forever(sleep_time=config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Run the dd.

    This function is the starting point of the dd and should be used to run the dd.
    """
    return defaults.transform(
        data, config, dd.run, ["raise_time", "@timestamp"], sort_ascending=True, oid_mapper=OIDMapper
    )
