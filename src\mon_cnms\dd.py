"""Detail design implementation for mon-cnms."""

import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import overload

import numpy as np
import pandas as pd

from mon_cnms import statics
from olympus_common import enums
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Return the row's summary."""
    if _is_heartbeat(row):
        return "C-NMS Heartbeat Message"
    else:
        return row["logmsg"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Return the row's severity."""
    if _is_heartbeat(row):
        return enums.Severity.INDETERMINATE.value

    severity_map = {
        "CLEARED": enums.Severity.CLEARED.value,
        "NORMAL": enums.Severity.CLEARED.value,
        "INDETERMINATE": enums.Severity.INDETERMINATE.value,
        "WARNING": enums.Severity.WARNING.value,
        "MINOR": enums.Severity.MINOR.value,
        "MAJOR": enums.Severity.MAJOR.value,
        "CRITICAL": enums.Severity.CRITICAL.value,
    }
    severityname = str(row["severityname"]).upper()
    return severity_map[severityname]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _element_monitored_name(row: pd.Series) -> str:
    """Return the row's element_monitored_name."""
    if _is_heartbeat(row):
        return "Heartbeat"
    else:
        return ""


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metrics(row: pd.Series) -> str:
    """Return the row's metrics."""
    if _is_heartbeat(row):
        return "/ApplicationEvent/"
    else:
        return ""


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime:
    delay = _delay(row)
    return _raise_time() + timedelta(0, delay)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Return the row's ci_id."""
    if _is_heartbeat(row):
        return "C-NMS_HB"
    else:
        return str(row["nodelabel"]).upper()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the row's manager."""
    return "mon-cnms"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Return the row's node."""
    if _is_heartbeat(row):
        return "C-NMS"
    else:
        return str(row["nodelabel"]).upper()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_level(row: pd.Series) -> str:
    """Return the row's clear_level."""
    if _is_heartbeat(row):
        return enums.AlarmType.HEARTBEAT.value

    if _severity(row) == enums.Severity.CLEARED.value:
        return enums.AlarmType.RESOLUTION.value
    else:
        return enums.AlarmType.PROBLEM.value


# TODO: See with the OCO or the responsible of the EMS if raise_time and last_occurrence can be reversed.
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time() -> datetime:
    """Return the row's raise_time.

    Notes
    -----
    In the OPTIC code and DD the definition of raise_time and last_occurrence are reversed from the generic logic.
    """
    return olympus_utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> None | datetime:
    """Return the row's clear_time."""
    if _is_heartbeat(row):
        return None
    else:
        return row["raise_time"] if _clear_level(row) == enums.AlarmType.RESOLUTION.value else None


# this value must be adressed in the next meeting
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type(row: pd.Series) -> str:
    """Return the row's clear_type."""
    if _is_heartbeat(row):
        return enums.ClearType.MANUALLY.value
    else:
        return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the row's scope."""
    return enums.Scope.TE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level(row: pd.Series) -> str:
    """Return the row's top_level."""
    key = str(row["nodelabel"]).upper()
    # basically return the key if it is in the statics.TOPLEVEL_DISCARD or it will be statics.TOPLEVEL_DEFAULT
    return statics.TOPLEVEL_DISCARD.get(key, statics.TOPLEVEL_DEFAULT)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_id(row: pd.Series) -> str:
    """Return the row's event_id."""
    if _is_heartbeat(row):
        return ""
    else:
        return row["eventuei"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str:
    """Return the row's node_alias."""
    if _is_heartbeat(row):
        return "C-NMS"
    else:
        return row["ipaddr"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm() -> bool:
    """Return the row's actionable_alarm."""
    return False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _extended_attr(row: pd.Series) -> str | None:
    """Return the row's extended_attr."""
    if _is_heartbeat(row):
        return None

    alarm_id = _alarm_id(row)

    return json.dumps({"alarm_id": alarm_id})


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _alarm_id(row: pd.Series) -> str:
    """Return the row's custom alarm id.

    It is the second word of the logmsg.

    Notes
    -----
    This is not to be confused with the original alarmid field in the row.
    """
    if _is_heartbeat(row):
        return ""
    else:
        return str(row["logmsg"]).split(" ")[1]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _delay(row: pd.Series) -> int:
    """Return the row's custom delay."""
    if _is_heartbeat(row):
        return 0

    alarm_id = _alarm_id(row)
    log_message: str = row["logmsg"]
    if alarm_id in ["5101002e", "ISDN105", "TRK101", "TRK102", "TRK103"] and "KDT_0_0" in log_message:
        return 3600
    else:
        match alarm_id:
            case "HLR1302" | "MRF1302" | "MSC1302" | "USM1304":
                return 1800
            case "ProcessingError_DATA_FILES":
                return 21600
            case "MSC1362":
                return 0
            case "NTP1300":
                return 900
            case _:
                return 120


@overload
def _is_heartbeat(row: pd.Series) -> bool: ...


@overload
def _is_heartbeat(row: pd.DataFrame) -> pd.Series: ...


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _is_heartbeat(row: pd.DataFrame | pd.Series) -> pd.Series | bool:
    """Return whether or not the row is a heartbeat event.

    In case of heartbeat events, the sourcetype will be SNMPv2-SMI::enterprises.20306.3.7.1.2, for all others it will be
    SNMPv2-SMI::enterprises.20306.3.7.1.1
    """
    return row["sourcetype"] == "SNMPv2-SMI::enterprises.20306.3.7.1.2"


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Apply all transformations to the data and insert it.

    The logic here is outlined in cnms-dd.
    """
    df = _clean_raw_data(df)

    if df.empty:
        return df

    _transform(df, agent_id)
    df = df.replace({pd.NA: None, np.nan: None})
    df = _clean_transformed_data(df)
    return df


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clean_raw_data(original: pd.DataFrame) -> pd.DataFrame:
    """Create a subset of the original dataframe to keep only relevant columns.

    The columns are renamed to have a more human-readable name.
    We discard some alarms before any other data transformation because we can decide it based on the raw data already.

    Notes
    -----
    We reindex the provided dataframe with config.COLUMNS_TO_KEEP because sometimes we receive only heartbeats and these
    types of messages do not include snmptrap related information.
    """
    if _is_heartbeat(original).all():
        # No need to remove any rows if all rows are heartbeats.
        return original

    df = original.loc[~original["nodelabel"].str.upper().isin(statics.DISCARD_NODES)]
    df = _drop_invalid_rows(df)
    return df


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> None:
    # Fields present on olympus_common.db.Alarm
    # raise_time should be called first as other functions (_clear_time) rely on the key being present in the dataframe.
    df["raise_time"] = _raise_time()

    # Hardcoded values
    df["agent_id"] = agent_id
    df["manager"] = _manager()
    df["action_class"] = _scope()
    df["actionable"] = _actionable_alarm()
    df["handle_time"] = olympus_utils.now_naive()

    # Applied values
    df["summary"] = df.apply(_summary, axis=1)
    df["severity"] = df.apply(_severity, axis=1)
    df["ci_id"] = df.apply(_ci_id, axis=1)
    df["node"] = df.apply(_node, axis=1)
    df["event_type"] = df.apply(_clear_level, axis=1)
    df["clear_time"] = df.apply(_clear_time, axis=1)
    df["clear_type"] = df.apply(_clear_type, axis=1)
    df["top_level"] = df.apply(_top_level, axis=1)
    df["event_id"] = df.apply(_event_id, axis=1)
    df["node_alias"] = df.apply(_node_alias, axis=1)
    df["additional_data"] = df.apply(_extended_attr, axis=1)
    df["metric_name"] = df.apply(_element_monitored_name, axis=1)
    df["metric_type"] = df.apply(_metrics, axis=1)
    df["wake_up_time"] = df.apply(_wake_up_time, axis=1)

    # Fields not present on olympus_common.db.Alarm
    df["ext_alarm_id"] = df.apply(_alarm_id, axis=1)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clean_transformed_data(original: pd.DataFrame) -> pd.DataFrame:
    """Remove certain irrelevant rows from the provided dataframe.

    This function is intended to be called after applying all transformations to the dataframe since it relies on some
    columns to be present that are not present in the raw data.

    Notes
    -----
    Only `clean_alarm_df` is called if the provided df consists of only heartbeats.
    """
    alarm_id = "SWER"
    description = "0457A6B0=EPRIPRTI.AK01:PRINT_OU+#0110"
    df = original.loc[~((original["ext_alarm_id"] == alarm_id) & (original["description"].str.contains(description)))]
    return df


def _drop_invalid_rows(df: pd.DataFrame) -> pd.DataFrame:
    """Drop rows which are not heartbeats and where the hostname is NaN.

    Notes
    -----
    It is assumed that when a hostname is nan, all other fields are nan.
    This function is required because some older data is in a different format and can be safely discarded.
    """
    return df.loc[~(df["hostname"].isna() & ~_is_heartbeat(df))]
