"""OID mapping for Big Data."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Big Data."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "labels.acode": "a_code",
            "labels.alertname": "alert_name",
            "labels.ci": "ci",
            "labels.metricname": "metric_name",
            "labels.metrictype": "metric_type",
            "labels.node": "node",
            "labels.severity": "severity",
            "status": "status",
            "annotations.summary": "summary",
            "startsAt": "starts_at",
            "endsAt": "ends_at",
        }
