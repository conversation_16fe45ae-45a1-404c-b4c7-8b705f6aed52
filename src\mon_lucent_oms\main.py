"""Entrypoint for the application."""

from mon_lucent_oms import dd
from mon_lucent_oms.config import config
from mon_lucent_oms.oid_mapper import OIDMapper
from olympus_common import defaults

application = defaults.databasewriter_kafkareader_app(config)


@application.run_forever(config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Execute the main function for when the project is run."""
    return defaults.transform(data=data, config=config, run_fn=dd.run, oid_mapper=OIDMapper)
