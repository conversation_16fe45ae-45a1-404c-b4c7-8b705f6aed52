"""OID mapping for Local6."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Local6."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "@timestamp": "event_timestamp",
            "channel": "channel",
            "ci_id": "ci_id",
            "environment": "environment",
            "hostname": "hostname",
            "host.name": "syslog_hostname",
            "log.original": "log_original",
            "message": "message",
            "metric": "metric",
            "severity": "syslog_severity",
            "syslog_timestamp": "syslog_timestamp",
            "tag": "tag",
            "tags": "tags",
            "optional.parameter": "optional_parameter",
        }

    @property
    def drop_if_all_nan(self) -> list[str]:
        """Return fields to drop if all values are NaN."""
        return ["ci_id", "tag", "metric", "channel", "environment"]
