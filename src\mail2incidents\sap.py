"""Module to interact with SAP."""

import datetime as dt
import logging
import mimetypes
import os

from dateutil.relativedelta import relativedelta
from requests import Response, Session

from mail2incidents.config import config  # type: ignore
from mail2incidents.statics import data_sap, header_sap  # type: ignore


class SAPIncident:
    """Class to get data from Microsoft Graph API."""

    incident_set: str = "IncidentSet"
    feedback_set: str = "ProvideFeedback"
    attachment_set: str = "AttachmentSet"
    status_set: str = "SetSecondaryStatus"
    endpoint: str = config.sap.incident_endpoint
    incident_path: str = str(endpoint) + str(incident_set)
    feedback_path: str = str(endpoint) + str(feedback_set)
    attachment_path: str = str(endpoint) + str(attachment_set)
    status_path: str = str(endpoint) + str(status_set)
    token_expiration: dt.datetime = dt.datetime.now()

    session: Session = Session()
    session.verify = False
    session.auth = (config.sap.user, config.sap.password)
    session.headers.update(header_sap)

    def get_token(self) -> str:
        """Get token from SAP endpoint.

        Do not use self._request as it would cause a recursion error.
        """
        response = self.session.get(url=self.incident_path, headers=header_sap)
        return response.headers["x-csrf-token"]

    def _login(self) -> None:
        """Add token in the header if it is not yet present."""
        if self.token_expiration <= dt.datetime.now():
            self.session.headers["x-csrf-token"] = self.get_token()
            self.token_expiration = dt.datetime.now() + dt.timedelta(minutes=25)

    def _request(self, method: str, *args, **kwargs) -> Response:
        """Make a request to SAP endpoint, login if needed."""
        self._login()
        response = self.session.request(method, *args, **kwargs)
        response.raise_for_status()
        return response

    def create_entity_and_get_qmnum(self, email_body, email_subject_lim):
        """Create a SAP entity."""
        data = {
            "Qmtxt": str(email_subject_lim),
            "toLongText": [{"Tdobject": "QMEL", "Tdname": "", "Tdspras": "", "Ltxt": str(email_body)}],
            "WorkCenter": config.sap.workcenter,
            **data_sap,
        }
        response = self._request("post", self.incident_path, json=data).json()
        return response["d"]["Qmnum"]

    def get_entities_3m(self) -> Response:
        """Check if entity already exists."""
        current_time = dt.datetime.now().strftime("%Y-%m-%d" + "T" + "%H:%M:%S")
        three_months_ago = (dt.datetime.now() - relativedelta(months=3)).strftime("%Y-%m-%d" + "T" + "%H:%M:%S")
        request_url = (
            f"{self.incident_path}?$filter= RequiredStartDate ge datetime'{three_months_ago}' "
            f"and RequiredStartDate le datetime'{current_time}' "
            "and (WorkCenter eq 'T31A' or WorkCenter eq 'T31B' or WorkCenter eq 'T31C' or WorkCenter eq 'T33B01') "
            "and PartnerNumber eq 'F1021' "
            "and Open eq true and Completed eq true "
            "&$select= Qmnum,LongText,Qmtxt,StatOrdUsrSeq"
        )
        return self._request("get", request_url).json()

    def check_inc_in_entities_and_get_qmnum(self, entities, inc_code) -> str | bool:
        """Verifiy if entity already exists."""
        for entity in entities["d"]["results"]:
            data_qmtxt = entity["Qmtxt"]
            data_longtxt = entity["LongText"]
            if inc_code in data_qmtxt or inc_code in data_longtxt:
                return entity["Qmnum"]
        return False

    def add_email_to_entity(self, qmnum, name_pdf) -> Response:
        """Add PDF from e-mail to entity."""
        try:
            project_root = os.path.dirname(os.path.abspath(__file__))
            file_path = os.path.join(project_root, name_pdf)
            with open(file_path, "rb") as pdf_file:
                file_content = pdf_file.read()
        except FileNotFoundError as exc:
            raise ValueError("This file doesn't exists.") from exc
        headers = {
            "Slug": f"{qmnum}/{name_pdf}.pdf",
            "Content-Type": "application/pdf",
        }
        response = self._request("post", self.attachment_path, headers=headers, data=file_content)

        if response.ok:
            os.remove(file_path)
        else:
            print("Something went wrong while adding the PDF.")

        return response

    def add_attachment_to_entity(self, qmnum, file_name) -> Response:
        """Add attachment from e-mail to entity."""
        try:
            project_root = os.path.dirname(os.path.abspath(__file__))
            file_path = os.path.join(project_root, file_name)

            if not os.path.isfile(file_path):
                raise ValueError("This file doesn't exists.")

            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type is None:
                mime_type = "application/octet-stream"

            with open(file_path, "rb") as file:
                file_content = file.read()
            headers = {
                "Slug": f"{qmnum}/{file_name}",
                "Content-Type": mime_type,
            }
            response = self._request("post", self.attachment_path, headers=headers, data=file_content)

            if not response.ok:
                logging.error(f"Something went wrong while adding the attachment {file_name}.")
            os.remove(file_path)
            return response
        except FileNotFoundError as exc:
            raise ValueError("This file doesn't exists.") from exc

    def update_incident_status(self, qmnum) -> Response:
        """Set status to STUS."""
        path = f"{self.status_path}?Qmnum='{qmnum}'&Status='{config.sap.status}'&format=json"
        return self._request("post", path)

    def add_feedback(self, qmnum) -> Response:
        """Update a sap main incident to add something as a log."""
        path = (
            f"{self.feedback_path}?Qmnum='{qmnum}'&LongText='{config.sap.long_text}'&"
            f"ConfirmationText='{config.sap.confirmation_text}'&Reason='{config.sap.reason}'"
        )
        return self._request("post", path)

    def get_entity(self, qmnum: int) -> Response:
        """Get a specific incident from SAP endpoint."""
        incident_path_url = f"{self.incident_path}('{qmnum}')"
        return self._request("get", incident_path_url)

    def release_main_order_incident(self, qmnum: int) -> Response:
        """Release the main order of an incident."""
        data = {"FlagMainOrderRelease": True, "FlagMainOrderCreate": True}
        request_url = f"{self.incident_path}('{qmnum}')"
        return self._request("patch", request_url, json=data)


sap_incident = SAPIncident()
