"""OID mapping for Lucent OMS."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Lucent OMS."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mapped OIDs."""
        return {
            "alarm.qualifier": "alarm_qualifier",  # q
            "alarm.start_time": "raise_time_raw",  # tm
            "alarm.severity": "alarm_severity",  # sv
            "alarm.name": "alarm_name",  # n
            "alarm.service_affecting": "service_affecting_flag",  # af
            "alarm.network_resource": "name_network_resource",  # cla
            "alarm.additional_info": "additional_info",  # ai
            "alarm.type": "alarm_type",  # cat
            "alarm.additional_text": "alarm_text_string",  # txt
            "alarm.state": "alarm_state",  # s
            "event.sequence": "sequence_number_tag",  # t
            "source.physical_port": "physical_port_address",  # pa
        }

    def get_oids(self) -> list[dict]:
        """Return oid."""
        return [self.mappings]
