"""Module to provide methods and objects for UCMDB."""

from dataclasses import dataclass
from typing import Optional, Self, Sequence

import pandas as pd
from sqlalchemy import PrimaryKeyConstraint, Row, and_, literal_column, or_, select
from sqlalchemy.orm import DeclarativeBase, Mapped, Session, mapped_column

from olympus_common.config import CMDBConfig
from olympus_common.db import SessionManager
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import MeasureType


@dataclass
class CmdbBase(DeclarativeBase):
    """Represent the base model of UCMDB views."""

    @classmethod
    def to_dict(cls, objects) -> list:
        """Convert object to dict."""
        return [obj.__dict__ if isinstance(obj, cls) else obj[0].__dict__ for obj in objects]

    def get_all(self, ucmdb_db_session: Session) -> Sequence[Row[tuple["CmdbBase"]]]:
        """Get all CI's or Metrics object in UCMDB views."""
        stmt = select(self.__class__)
        return ucmdb_db_session.execute(stmt).all()

    def get_from_field(
        self, target_field: str, target_value: str, ucmdb_db_session: Session
    ) -> Sequence[Row[tuple["CmdbBase"]]]:
        """Select CI's or Metrics based on a specific field and specific value."""
        stmt = select(self.__class__).where(literal_column(target_field) == target_value)
        return ucmdb_db_session.execute(stmt).all()

    def get_all_df(self, ucmdb_db_session: Session) -> pd.DataFrame:
        """Return a dataframe from get all."""
        data = self.to_dict(self.get_all(ucmdb_db_session))
        df = pd.DataFrame(data).drop_duplicates(ignore_index=True)
        return df.drop(columns=["_sa_instance_state"]) if data else pd.DataFrame()

    def get_from_field_df(self, target_field: str, target_value: str, ucmdb_db_session: Session) -> pd.DataFrame:
        """Return a dataframe from get something from a field."""
        data = self.to_dict(self.get_from_field(target_field, target_value, ucmdb_db_session))
        df = pd.DataFrame(data).drop_duplicates(ignore_index=True)
        return df.drop(columns=["_sa_instance_state"]) if data else pd.DataFrame()

    def get_one_by_field(self, target_field: str, target_value: str, ucmdb_db_session: Session) -> Self | None:
        """Select a one CI's or Metrics  based on a specific field and specific value."""
        stmt = select(self.__class__).where(literal_column(target_field) == target_value)
        return ucmdb_db_session.scalars(stmt).first()


@dataclass
class AssetListView(CmdbBase):
    """Represent the AssetListView table corresponding to the CI's Objects."""

    __tablename__ = "AssetListView"

    identification: Mapped[str] = mapped_column()
    ci_uuid: Mapped[Optional[str]] = mapped_column()
    ci_type: Mapped[Optional[str]] = mapped_column()
    floc_id: Mapped[Optional[str]] = mapped_column()
    floc_class: Mapped[Optional[str]] = mapped_column()
    critical_ci: Mapped[Optional[str]] = mapped_column()
    model: Mapped[Optional[str]] = mapped_column()
    brand: Mapped[Optional[str]] = mapped_column()
    comment: Mapped[Optional[str]] = mapped_column()
    ip_address: Mapped[Optional[str]] = mapped_column()
    address: Mapped[Optional[str]] = mapped_column()
    location_category: Mapped[Optional[str]] = mapped_column()
    location_address: Mapped[Optional[str]] = mapped_column()
    location_attribute: Mapped[Optional[str]] = mapped_column()
    location_type: Mapped[Optional[str]] = mapped_column()
    location_floc_id: Mapped[Optional[str]] = mapped_column()
    location_postal_code: Mapped[Optional[str]] = mapped_column()
    location_latitude: Mapped[Optional[str]] = mapped_column()
    location_longitude: Mapped[Optional[str]] = mapped_column()
    location_telecom_center: Mapped[Optional[str]] = mapped_column()

    __table_args__ = (PrimaryKeyConstraint(floc_id, identification), {"schema": "Monitoring"})

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_from_ci_type(self, ci_type: str, ucmdb_db_session: Session) -> list["AssetListView"]:
        """Select CI's from ci_type."""
        return ucmdb_db_session.query(self.__class__).filter(self.__class__.ci_type == ci_type).all()

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_from_ci_type_df(self, ci_type: str, ucmdb_db_session: Session) -> pd.DataFrame:
        """Select CI's from ci_type and return a dataframe."""
        data = self.to_dict(self.get_from_ci_type(ci_type, ucmdb_db_session))
        return pd.DataFrame(data).drop(columns=["_sa_instance_state"]) if data else pd.DataFrame()

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_from_floc_id_process_check(self, hostnames: list[str], ucmdb_db_session: Session) -> list["AssetListView"]:
        """Get the unprocessed CI's from floc_id for the process check."""
        return ucmdb_db_session.query(self.__class__).filter(self.__class__.floc_id.in_(hostnames)).all()

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_from_floc_id_process_check_df(self, hostnames: list[str], ucmdb_db_session: Session) -> pd.DataFrame:
        """Get the unprocessed CI's from floc_id for the process check and return a dataframe."""
        data = self.to_dict(self.get_from_floc_id_process_check(hostnames, ucmdb_db_session))
        return pd.DataFrame(data).drop(columns=["_sa_instance_state"]) if data else pd.DataFrame()

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_ci_data(self, ci_ids: list[str], ucmdb_db_session: Session) -> list["AssetListView"]:
        """Get CI data for enrichment."""
        return (
            ucmdb_db_session.query(self.__class__)
            .distinct()
            .filter(self.__class__.identification.in_(ci_ids))
            .order_by(self.__class__.identification, self.__class__.floc_id)
            .all()
        )

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_ci_data_dict(self, ci_ids: list[str], ucmdb_db_session: Session) -> list[dict]:
        """Get CI data for enrichment and return a list."""
        return self.to_dict(self.get_ci_data(ci_ids, ucmdb_db_session))


@dataclass
class MetricListView(CmdbBase):
    """Represent the MetricListView table."""

    __tablename__ = "MetricListView"

    lportfolio_item_id: Mapped[str] = mapped_column()
    identification: Mapped[Optional[str]] = mapped_column()
    floc_id: Mapped[Optional[str]] = mapped_column()
    source: Mapped[Optional[str]] = mapped_column()
    metric_type: Mapped[Optional[str]] = mapped_column()
    metric_name: Mapped[Optional[str]] = mapped_column()
    parent_impact: Mapped[Optional[str]] = mapped_column()
    top_level_impact: Mapped[Optional[int]] = mapped_column()
    actionable: Mapped[Optional[int]] = mapped_column()
    metric_category: Mapped[Optional[str]] = mapped_column()
    actionable_alarm_raise: Mapped[Optional[str]] = mapped_column()
    actionable_alarm_clear: Mapped[Optional[str]] = mapped_column()
    warning_level_criteria: Mapped[Optional[str]] = mapped_column()
    critical_level_criteria: Mapped[Optional[str]] = mapped_column()
    action_class: Mapped[Optional[str]] = mapped_column()
    actions_enabled: Mapped[Optional[int]] = mapped_column()
    mon_product_knowledge_ref: Mapped[Optional[str]] = mapped_column()
    mon_impl_knowledge_ref: Mapped[Optional[str]] = mapped_column()
    op_mon_knowledge_ref: Mapped[Optional[str]] = mapped_column()
    metric_description: Mapped[Optional[str]] = mapped_column()
    instructions: Mapped[Optional[str]] = mapped_column()
    metric_short_name: Mapped[Optional[str]] = mapped_column()
    subgroups: Mapped[Optional[str]] = mapped_column()
    metric_service: Mapped[Optional[str]] = mapped_column()
    metric_sub_service: Mapped[Optional[str]] = mapped_column()

    __table_args__ = (
        PrimaryKeyConstraint(floc_id, identification, metric_type, metric_name),
        {"schema": "Monitoring"},
    )

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_metric_data(
        self,
        floc_ids: list[str],
        ci_ids: list[str],
        metric_types: list[str],
        ucmdb_db_session: Session,
    ) -> list["MetricListView"]:
        """Get metric data for enrichment."""
        return (
            ucmdb_db_session.query(self.__class__)
            .distinct()
            .filter(or_(self.__class__.floc_id.in_(floc_ids), (self.__class__.identification.in_(ci_ids))))
            .filter(self.__class__.metric_type.in_(metric_types))
            .all()
        )

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_metric_data_from_source(self, source: str, ucmdb_db_session: Session) -> list["MetricListView"]:
        """Get metric data from the source."""
        return (
            ucmdb_db_session.query(self.__class__)
            .filter(self.__class__.source == source)
            .filter(self.__class__.floc_id.is_not(None))
            .all()
        )

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_metric_data_from_source_df(self, source: str, ucmdb_db_session: Session) -> pd.DataFrame:
        """Get metric data from the source and return a dataframe."""
        data = self.to_dict(self.get_metric_data_from_source(source, ucmdb_db_session))
        return pd.DataFrame(data).drop(columns=["_sa_instance_state"]) if data else pd.DataFrame()

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_metric_data_dict(
        self,
        floc_ids: list[str],
        ci_ids: list[str],
        metric_types: list[str],
        ucmdb_db_session: Session,
    ) -> list[dict]:
        """Get metric data for enrichment and return a list."""
        return self.to_dict(self.get_metric_data(floc_ids, ci_ids, metric_types, ucmdb_db_session))

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_metric_description(
        self,
        d_condition: list[tuple],
        ucmdb_db_session: Session,
    ) -> list["MetricListView"]:
        """Get metric description  for process check."""
        conditions = (
            and_(
                self.__class__.floc_id == floc_id,
                self.__class__.metric_type == m_type,
                self.__class__.metric_name == m_name,
            )
            for (floc_id, m_type, m_name) in d_condition
        )
        return ucmdb_db_session.query(self.__class__).filter(or_(*conditions)).all()

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_metric_description_df(
        self,
        d_condition: list[tuple],
        ucmdb_db_session: Session,
    ) -> pd.DataFrame:
        """Get metric description for process check."""
        data = self.to_dict(
            self.get_metric_description(
                d_condition,
                ucmdb_db_session=ucmdb_db_session,
            )
        )
        df = pd.DataFrame(data).drop_duplicates(ignore_index=True)
        return df.drop(columns=["_sa_instance_state"]) if data else pd.DataFrame()


@dataclass
class HierarchyListView(CmdbBase):
    """Represent the HierarchyListView table."""

    __tablename__ = "HierarchyListView"

    application: Mapped[Optional[str]] = mapped_column()
    environment: Mapped[Optional[str]] = mapped_column()
    dashboard: Mapped[Optional[str]] = mapped_column()
    ci_identification: Mapped[Optional[str]] = mapped_column()
    ci_type: Mapped[Optional[str]] = mapped_column()
    ci_floc_id: Mapped[Optional[str]] = mapped_column()
    ci_floc_class: Mapped[Optional[str]] = mapped_column()
    service: Mapped[Optional[str]] = mapped_column()
    sub_service: Mapped[Optional[str]] = mapped_column()
    critical_confirmed: Mapped[Optional[int]] = mapped_column()
    sub_service_code: Mapped[Optional[str]] = mapped_column()

    __table_args__ = (
        PrimaryKeyConstraint(ci_identification, ci_floc_id, environment, dashboard),
        {"schema": "Monitoring"},
    )

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_dashboards_from_ci_type(self, ci_type: str, ucmdb_db_session: Session) -> list["HierarchyListView"]:
        """Select all dashboards based on dashboard_name and ci_type."""
        return (
            ucmdb_db_session.query(self.__class__)
            .filter(self.__class__.ci_type == ci_type)
            .filter(self.__class__.ci_floc_id.isnot(None))
            .order_by(self.__class__.ci_floc_id, self.__class__.dashboard)
            .all()
        )

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_dashboards_from_ci_type_df(self, ci_type: str, ucmdb_db_session: Session) -> pd.DataFrame:
        """Select all dashboards based on dashboard_name and ci_type in dataframe."""
        data = self.to_dict(self.get_dashboards_from_ci_type(ci_type, ucmdb_db_session))
        return pd.DataFrame(data).drop(columns=["_sa_instance_state"]) if data else pd.DataFrame()

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_topology_data(
        self, floc_ids: list[str], ci_ids: list[str], ucmdb_db_session: Session
    ) -> list["HierarchyListView"]:
        """Get topology data for enrichment."""
        return (
            ucmdb_db_session.query(self.__class__)
            .distinct()
            .filter(or_(self.__class__.ci_floc_id.in_(floc_ids), self.__class__.ci_identification.in_(ci_ids)))
            .all()
        )

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_topology_data_dict(self, floc_ids: list[str], ci_ids: list[str], ucmdb_db_session: Session) -> list[dict]:
        """Get metric data for enrichment and return a list."""
        return self.to_dict(self.get_topology_data(floc_ids, ci_ids, ucmdb_db_session))


class CIDependencies(CmdbBase):
    """Represent the CIDependencies table."""

    __tablename__ = "CIDependencies"

    source_floc_id: Mapped[Optional[str]] = mapped_column()
    destination_floc_id: Mapped[Optional[str]] = mapped_column()
    source_identification: Mapped[Optional[str]] = mapped_column()
    destination_identification: Mapped[Optional[str]] = mapped_column()

    __table_args__ = (
        PrimaryKeyConstraint(source_floc_id, destination_floc_id),
        {"schema": "Monitoring"},
    )

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_ci_dependencies(self, ucmdb_db_session: Session) -> list["CIDependencies"]:
        """Select all dashboards based on dashboard_name and ci_type."""
        return ucmdb_db_session.query(self.__class__).all()

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_ci_dependencies_df(self, ucmdb_db_session: Session) -> pd.DataFrame:
        """Select all dashboards based on dashboard_name and ci_type in dataframe."""
        data = self.to_dict(self.get_ci_dependencies(ucmdb_db_session))
        df = pd.DataFrame(data)
        if "_sa_instance_state" in list(df.columns):
            df = df.drop(columns=["_sa_instance_state"])
        return df


def create_ucmdb_session_manager() -> SessionManager:
    """Create a database session manager for uCMDB."""
    cmdb_config = CMDBConfig()
    return SessionManager(cmdb_config.to_conninfo(), echo=False)
