"""Entrypoint for the application."""

from mon_certificates import dd, queries
from mon_certificates.config import config
from olympus_common.core import Application
from olympus_common.datareaders import ElasticReader
from olympus_common.datawriters import DatabaseWriter
from olympus_common.db import <PERSON><PERSON>ur<PERSON>ce, SessionManager, create_session_manager
from olympus_common.defaults import get_logger


def _init_application() -> tuple[Application, SessionManager]:
    session_manager = create_session_manager(config.service_name)
    datareader = ElasticReader(query=queries.ES_QUERY, sort_query=queries.ES_SORT_QUERY)
    logger = get_logger(config.debug, config.logger_config)
    datawriter = DatabaseWriter(object_class=Occurrence, session_manager=session_manager)
    return Application(datareader=datareader, datawriter=datawriter, logger=logger), session_manager


application, session_manager = _init_application()


@application.run_once()
def main(data: list[dict]) -> list[dict]:
    """Execute the main function for when the project is run."""
    with session_manager.get_session() as session:
        df_alarms = dd.run(data, session)

    return df_alarms.to_dict("records")
