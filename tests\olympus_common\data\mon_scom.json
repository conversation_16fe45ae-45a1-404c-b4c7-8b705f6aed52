{"data": [{"input": {"DisplayString": "Logical Disk Free Space is low", "Category": "AvailabilityHealth", "ClassId": "3d549847-ed40-6252-1070-a03d39b0cf29", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.PropertyBagData\" time=\"2024-12-06T08:28:11.8059150+01:00\" sourceHealthServiceId=\"C675A42C-CB89-143D-67C6-3617C2EF2E15\"><Property Name=\"PctFree\" VariantType=\"8\">0</Property><Property Name=\"MbFree\" VariantType=\"8\">966</Property><Property Name=\"DiskLabel\" VariantType=\"8\">D:</Property><Property Name=\"IsSystem\" VariantType=\"8\">false</Property></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The disk D: on computer IAMNCIAPWV009.msnet.railb.be is running out of disk space. The values that exceeded the threshold are 0% free space and 966 free Mbytes.", "Id": "75153097-a985-4cc0-9308-bb146adf12b8", "IsMonitorAlert": "True", "LastModified": "06/12/2024 07:28:33", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "06/12/2024 07:28:33", "MaintenanceModeLastModified": "02/12/2024 12:27:19", "MonitoringClassId": "3d549847-ed40-6252-1070-a03d39b0cf29", "MonitoringObjectDisplayName": "D:", "MonitoringObjectFullName": "Microsoft.Windows.Server.6.2.LogicalDisk:IAMNCIAPWV009.msnet.railb.be;D:", "MonitoringObjectHealthState": "Error", "MonitoringObjectId": "6eb8d47e-6ab0-bb11-37ee-c37d329e8748", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "D:", "MonitoringObjectPath": "IAMNCIAPWV009.msnet.railb.be", "MonitoringRuleId": "904ec1e7-405b-bba9-4858-9cfec269b209", "Name": "Logical Disk Free Space is low", "NetbiosComputerName": "IAMNCIAPWV009", "NetbiosDomainName": "MSNET", "Owner": null, "Parameters": "", "PrincipalName": "IAMNCIAPWV009.msnet.railb.be", "Priority": "Normal", "ProblemId": "904ec1e7-405b-bba9-4858-9cfec269b209", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "904ec1e7-405b-bba9-4858-9cfec269b209", "Severity": "Error", "SiteName": null, "StateLastModified": "06/12/2024 07:28:11", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "06/12/2024 07:28:12", "TimeRaised": "06/12/2024 07:28:11", "TimeResolutionStateLastModified": "06/12/2024 07:28:11", "TimeResolved": "", "UnformattedDescription": "The disk D: on computer IAMNCIAPWV009.msnet.railb.be is running out of disk space. The values that exceeded the threshold are 0% free space and 966 free Mbytes."}, "output": {"ci_id": "IAMNCIAPWV009", "metric_type": "/LogicalDisk/LogicalDiskSpace/", "metric_name": "D:"}}, {"input": {"DisplayString": "Custom Application EventLog failed", "Category": "Custom", "ClassId": "034b9448-238c-4195-e4b6-c6e65e439595", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"Microsoft.Windows.EventData\" time=\"2024-12-10T08:52:45.4039552+01:00\" sourceHealthServiceId=\"4E1BBE95-86BF-FCC1-1BD0-2B29AA39C59E\"><EventOriginId>{E2D712BF-8E26-434A-85EB-FEC933078EEE}</EventOriginId><PublisherId>{E75C9D97-B482-2CDE-052F-F34E1C8007A6}</PublisherId><PublisherName>AUDIOCODES_GENERAL</PublisherName><EventSourceName>AUDIOCODES_GENERAL</EventSourceName><Channel>NOC_A1247</Channel><LoggingComputer>IICTCIVOWV010.msnet.railb.be</LoggingComputer><EventNumber>221</EventNumber><EventCategory>0</EventCategory><EventLevel>1</EventLevel><UserName>MSNET\\ADS_A1245</UserName><RawDescription><![CDATA[%1\r\n]]></RawDescription><LCID>2057</LCID><CollectDescription Type=\"Boolean\">true</CollectDescription><Params><Param>10.86.6.154__Certificate_expiry__The_certificate_of_TLS_context_1_will_expire_in_60_days_</Param></Params><EventData><DataItem type=\"System.XmlData\" time=\"2024-12-10T08:52:45.4039552+01:00\" sourceHealthServiceId=\"4E1BBE95-86BF-FCC1-1BD0-2B29AA39C59E\"><EventData xmlns=\"http://schemas.microsoft.com/win/2004/08/events/event\"><Data>10.86.6.154__Certificate_expiry__The_certificate_of_TLS_context_1_will_expire_in_60_days_</Data></EventData></DataItem></EventData><EventDisplayNumber>221</EventDisplayNumber><EventDescription><![CDATA[10.86.6.154__Certificate_expiry__The_certificate_of_TLS_context_1_will_expire_in_60_days_\r\n]]></EventDescription><Keywords>36028797018963968</Keywords></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "Custom::__AlertGroup__=EventLog::__CI_ID__=AUDIOCODES_GENERAL::__MonitoredElementName__=AUDIOCODES_GENERAL_221::__Platform__=Windows::__Summary__= 10.86.6.154__Certificate_expiry__The_certificate_of_TLS_context_1_will_expire_in_60_days_\n is not running. ID=221 ::", "Id": "e45d8d95-4b8e-4f81-b4fd-2e48f9a575b5", "IsMonitorAlert": "True", "LastModified": "10/12/2024 07:54:05", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "10/12/2024 07:54:05", "MaintenanceModeLastModified": "28/11/2024 11:41:43", "MonitoringClassId": "034b9448-238c-4195-e4b6-c6e65e439595", "MonitoringObjectDisplayName": "EventLog_A1247_Audiocodes_Telephony_Gateways_AUDIOCODES_GENERAL_221", "MonitoringObjectFullName": "EventLog_A1247_Audiocodes_Telephony_Gateways_AUDIOCODES_GENERAL_221:IICTCIVOWV010.msnet.railb.be;EventLog_A1247_Audiocodes_Telephony_Gateways_AUDIOCODES_GENERAL_221", "MonitoringObjectHealthState": "Error", "MonitoringObjectId": "88a3fb22-8182-35dc-add8-da5f8a46c49b", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "EventLog_A1247_Audiocodes_Telephony_Gateways_AUDIOCODES_GENERAL_221", "MonitoringObjectPath": "IICTCIVOWV010.msnet.railb.be", "MonitoringRuleId": "7e4f1697-cb6d-bd78-814f-70f608359c46", "Name": "Custom Application EventLog failed", "NetbiosComputerName": "IICTCIVOWV010", "NetbiosDomainName": "MSNET", "Owner": null, "Parameters": "", "PrincipalName": "IICTCIVOWV010.msnet.railb.be", "Priority": "High", "ProblemId": "7e4f1697-cb6d-bd78-814f-70f608359c46", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "7e4f1697-cb6d-bd78-814f-70f608359c46", "Severity": "Error", "SiteName": null, "StateLastModified": "10/12/2024 07:52:45", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "10/12/2024 07:53:11", "TimeRaised": "10/12/2024 07:52:45", "TimeResolutionStateLastModified": "10/12/2024 07:52:45", "TimeResolved": "", "UnformattedDescription": "Custom::__AlertGroup__=EventLog::__CI_ID__=AUDIOCODES_GENERAL::__MonitoredElementName__=AUDIOCODES_GENERAL_221::__Platform__=Windows::__Summary__= 10.86.6.154__Certificate_expiry__The_certificate_of_TLS_context_1_will_expire_in_60_days_\n is not running. ID=221 ::"}, "output": {"ci_id": "AUDIOCODES_GENERAL", "metric_type": "/Event/", "metric_name": "AUDIOCODES_GENERAL_221"}}, {"input": {"DisplayString": "Health Service Heartbeat Failure", "Category": "StateCollection", "ClassId": "a4899740-ef2f-1541-6c1d-51d34b739491", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.Availability.StateData\" time=\"2025-02-06T08: 26: 28.9028779+01: 00\" sourceHealthServiceId=\"382A6556-3FBB-0F4B-6D42-2754FE54A7A7\"><ManagementGroupId>{01232B72-5C22-A6FE-4DED-56C043A6DE06}</ManagementGroupId><HealthServiceId>C4DE9B52-81B4-EFA6-837C-F062B7030E78</HealthServiceId><HostName>IICTZIAPWV002.msnet.railb.be</HostName><Reachability ThruServer=\"false\"><State>0</State></Reachability></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The System Center Management Health Service on computer IICTZIAPWV002.msnet.railb.be failed to heartbeat.", "Id": "95086613-83b8-463e-9655-74835508519c", "IsMonitorAlert": "True", "LastModified": "06/02/2025 07:27:07", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "06/02/2025 07:27:07", "MaintenanceModeLastModified": "10/10/2024 12:30:04", "MonitoringClassId": "a4899740-ef2f-1541-6c1d-51d34b739491", "MonitoringObjectDisplayName": "IICTZIAPWV002.msnet.railb.be", "MonitoringObjectFullName": "Microsoft.SystemCenter.HealthServiceWatcher:Microsoft.SystemCenter.AgentWatchersGroup;c4de9b52-81b4-efa6-837c-f062b7030e78", "MonitoringObjectHealthState": "Success", "MonitoringObjectId": "a3f1657f-9073-f10b-3439-c82438b36836", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "c4de9b52-81b4-efa6-837c-f062b7030e78", "MonitoringObjectPath": "Microsoft.SystemCenter.AgentWatchersGroup", "MonitoringRuleId": "b59f78ce-c42a-8995-f099-e705dbb34fd4", "Name": "Health Service Heartbeat Failure", "NetbiosComputerName": null, "NetbiosDomainName": null, "Owner": null, "Parameters": "", "PrincipalName": null, "Priority": "High", "ProblemId": "b59f78ce-c42a-8995-f099-e705dbb34fd4", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "b59f78ce-c42a-8995-f099-e705dbb34fd4", "Severity": "Error", "SiteName": null, "StateLastModified": "05/02/2025 21:31:50", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "06/02/2025 07:26:11", "TimeRaised": "06/02/2025 07:26:28", "TimeResolutionStateLastModified": "06/02/2025 07:26:28", "TimeResolved": "", "UnformattedDescription": "The System Center Management Health Service on computer IICTZIAPWV002.msnet.railb.be failed to heartbeat."}, "output": {"ci_id": "IICTZIAPWV002", "metric_type": "/ServiceAvailability/", "metric_name": "HealthService"}}, {"input": {"DisplayString": "MSSQL on Windows: DB Engine is in unhealthy state", "Category": "AvailabilityHealth", "ClassId": "8def7ccc-ca28-c7ef-796f-644160e0b22a", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.PropertyBagData\" time=\"2025-02-06T16:03:41.9918853+01:00\" sourceHealthServiceId=\"137BAE80-F5ED-E489-B7B4-B53C3C734730\"><Property Name=\"Status\" VariantType=\"8\">AVAILABLE</Property><Property Name=\"StatusText\" VariantType=\"8\">Instance is available</Property></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The SQL Server instance \"ISA01601P\" on computer \"IICTCISQWC405.msnet.railb.be\" is unhealthy and reports \"Instance is unavailable\".", "Id": "e2cff6a3-5b60-46dd-a325-2dfc9d0e5533", "IsMonitorAlert": "True", "LastModified": "06/02/2025 15:03:42", "LastModifiedBy": "System", "LastModifiedByNonConnector": "06/02/2025 15:03:42", "MaintenanceModeLastModified": "01/01/1900 00:00:00", "MonitoringClassId": "8def7ccc-ca28-c7ef-796f-644160e0b22a", "MonitoringObjectDisplayName": "IICTCISQWC405\\ISA01601P", "MonitoringObjectFullName": "Microsoft.SQLServer.Windows.DBEngine:IICTCISQWC405.msnet.railb.be.ISA01601P", "MonitoringObjectHealthState": "Warning", "MonitoringObjectId": "08d9891a-caa7-f317-48e0-8ccaf2e95764", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "IICTCISQWC405.msnet.railb.be.ISA01601P", "MonitoringObjectPath": null, "MonitoringRuleId": "79fe8669-94ae-11ee-e430-4252da54a899", "Name": "MSSQL on Windows: DB Engine is in unhealthy state", "NetbiosComputerName": null, "NetbiosDomainName": null, "Owner": null, "Parameters": "", "PrincipalName": null, "Priority": "Normal", "ProblemId": "79fe8669-94ae-11ee-e430-4252da54a899", "RepeatCount": "0", "ResolutionState": "255", "ResolvedBy": "System", "RuleId": "79fe8669-94ae-11ee-e430-4252da54a899", "Severity": "Error", "SiteName": null, "StateLastModified": "06/02/2025 15:03:41", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "06/02/2025 15:02:49", "TimeRaised": "06/02/2025 15:02:49", "TimeResolutionStateLastModified": "06/02/2025 15:03:41", "TimeResolved": "06/02/2025 15:03:42", "UnformattedDescription": "The SQL Server instance \"ISA01601P\" on computer \"IICTCISQWC405.msnet.railb.be\" is unhealthy and reports \"Instance is unavailable\"."}, "output": {"ci_id": "ISA01601P", "metric_type": "/SQLDatabase/", "metric_name": "SQL_Instance_State"}}, {"input": {"DisplayString": "Custom Application Service failed", "Category": "Custom", "ClassId": "8e71ba92-dc7f-2969-18d6-be50839d7c1a", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem time=\"2025-02-09T02:26:31.6693289+01:00\" type=\"System.Health.StateData\" sourceHealthServiceId=\"00000000-0000-0000-0000-000000000000\"><HealthState>-1</HealthState><Context>The monitor has entered maintenance mode, or was requested not to change state when it had exited maintenance mode. The health state of this monitor will change as per the monitor configuration.</Context></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "Custom::__AlertGroup__=Service::__CI_ID__=A317_IOPECIAPWV003::__MonitoredElementName__=CPMSProcessManagementService::__Platform__=Windows - CPMSProcessManagementService::__Summary__=Service CPMSProcessManagementService is not running ::", "Id": "3feca750-8f69-496e-ab72-fd1ee0602dcb", "IsMonitorAlert": "True", "LastModified": "09/02/2025 01:26:31", "LastModifiedBy": "System", "LastModifiedByNonConnector": "09/02/2025 01:26:31", "MaintenanceModeLastModified": "09/02/2025 01:25:44", "MonitoringClassId": "8e71ba92-dc7f-2969-18d6-be50839d7c1a", "MonitoringObjectDisplayName": "Service_A317_OWS_CPMSProcessManagementService", "MonitoringObjectFullName": "Service_A317_OWS_CPMSProcessManagementService:IOPECIAPWV003.msnet.railb.be;Service_A317_OWS_CPMSProcessManagementService", "MonitoringObjectHealthState": "Uninitialized", "MonitoringObjectId": "51b178f6-5580-e15b-b9e1-7bdccf70b213", "MonitoringObjectInMaintenanceMode": "True", "MonitoringObjectName": "Service_A317_OWS_CPMSProcessManagementService", "MonitoringObjectPath": "IOPECIAPWV003.msnet.railb.be", "MonitoringRuleId": "912619a3-a688-7fda-2c90-1fe05208d119", "Name": "Custom Application Service failed", "NetbiosComputerName": "IOPECIAPWV003", "NetbiosDomainName": "MSNET", "Owner": null, "Parameters": "", "PrincipalName": "IOPECIAPWV003.msnet.railb.be", "Priority": "High", "ProblemId": "912619a3-a688-7fda-2c90-1fe05208d119", "RepeatCount": "0", "ResolutionState": "255", "ResolvedBy": "System", "RuleId": "912619a3-a688-7fda-2c90-1fe05208d119", "Severity": "Error", "SiteName": null, "StateLastModified": "09/02/2025 01:26:31", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "09/02/2025 01:02:16", "TimeRaised": "09/02/2025 01:02:16", "TimeResolutionStateLastModified": "09/02/2025 01:26:31", "TimeResolved": "09/02/2025 01:26:31", "UnformattedDescription": "Custom::__AlertGroup__=Service::__CI_ID__=A317_IOPECIAPWV003::__MonitoredElementName__=CPMSProcessManagementService::__Platform__=Windows - CPMSProcessManagementService::__Summary__=Service CPMSProcessManagementService is not running ::"}, "output": {"ci_id": "A317_IOPECIAPWV003", "metric_type": "/ServiceAvailability/", "metric_name": "CPMSProcessManagementService"}}, {"input": {"DisplayString": "Total CPU Utilization Percentage is too high", "Category": "PerformanceHealth", "ClassId": "66dd9b43-3dc1-3831-95d4-1b03b0a6ea13", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.PropertyBagData\" time=\"2025-02-09T19:22:02.1715485+01:00\" sourceHealthServiceId=\"FD45AE55-C432-7DEB-DB86-150EBD519E96\"><Property Name=\"State\" VariantType=\"8\">BAD</Property><Property Name=\"QueueLength\" VariantType=\"19\">22</Property><Property Name=\"PctUsage\" VariantType=\"8\">99.9965286254883</Property></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The threshold for the Processor Information\\% Processor Time\\_Total performance counter has been exceeded. The values that exceeded the threshold are: 99.9965286254883% CPU and a processor queue length of 22.", "Id": "00f4e9c2-dccc-4e35-bedb-0a9ad5481ab5", "IsMonitorAlert": "True", "LastModified": "09/02/2025 18:22:39", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "09/02/2025 18:22:39", "MaintenanceModeLastModified": "30/01/2025 10:41:29", "MonitoringClassId": "66dd9b43-3dc1-3831-95d4-1b03b0a6ea13", "MonitoringObjectDisplayName": "Microsoft Windows Server 2019 Standard", "MonitoringObjectFullName": "Microsoft.Windows.OperatingSystem:IICTCIAPWV378.msnet.railb.be", "MonitoringObjectHealthState": "Error", "MonitoringObjectId": "2ac2db10-ec3f-7a26-6f74-9b1088da53e3", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": null, "MonitoringObjectPath": "IICTCIAPWV378.msnet.railb.be", "MonitoringRuleId": "1828d8b8-e2ab-8374-166d-07c3b9218903", "Name": "Total CPU Utilization Percentage is too high", "NetbiosComputerName": "IICTCIAPWV378", "NetbiosDomainName": "MSNET", "Owner": null, "Parameters": "", "PrincipalName": "IICTCIAPWV378.msnet.railb.be", "Priority": "Normal", "ProblemId": "1828d8b8-e2ab-8374-166d-07c3b9218903", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "1828d8b8-e2ab-8374-166d-07c3b9218903", "Severity": "Error", "SiteName": null, "StateLastModified": "09/02/2025 18:22:02", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "09/02/2025 18:21:39", "TimeRaised": "09/02/2025 18:22:02", "TimeResolutionStateLastModified": "09/02/2025 18:22:02", "TimeResolved": "", "UnformattedDescription": "The threshold for the Processor Information\\% Processor Time\\_Total performance counter has been exceeded. The values that exceeded the threshold are: 99.9965286254883% CPU and a processor queue length of 22."}, "output": {"ci_id": "IICTCIAPWV378", "metric_type": "/System/ProcessorLoad/", "metric_name": "Processor"}}, {"input": {"DisplayString": "Custom Application Share", "Category": "Custom", "ClassId": "9c4a2550-97fa-2c4e-53f5-2d6eb442869d", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.PropertyBagData\" time=\"2025-02-10T05:12:48.1880079+01:00\" sourceHealthServiceId=\"C698C370-5F0B-3B97-FDB0-661DE96797FC\"><Property Name=\"MPStatus\" VariantType=\"8\">OK</Property><Property Name=\"MPmessage\" VariantType=\"8\">UNC \\\\msnet.railb.be\\iap1\\IAM\\IAMA1790 is available. Access permission is OK.</Property></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "Custom::__AlertGroup__=Share::__CI_ID__=A1790_IAMNCIWWWV005::__MonitoredElementName__=IAMA1790_COGENIUS::__Platform__=Windows::__Summary__=UNC \\\\msnet.railb.be\\iap1\\IAM\\IAMA1790 is available. But access permission is not OK. ::", "Id": "434fb890-0fef-4022-95d0-6314bf59012e", "IsMonitorAlert": "True", "LastModified": "10/02/2025 04:12:52", "LastModifiedBy": "System", "LastModifiedByNonConnector": "10/02/2025 04:12:52", "MaintenanceModeLastModified": "03/02/2025 14:10:02", "MonitoringClassId": "9c4a2550-97fa-2c4e-53f5-2d6eb442869d", "MonitoringObjectDisplayName": "Share_A1790_COGENIUS_IAMA1790_COGENIUS", "MonitoringObjectFullName": "Share_A1790_COGENIUS_IAMA1790_COGENIUS:IAMNCIWWWV005.msnet.railb.be;Share_A1790_COGENIUS_IAMA1790_COGENIUS", "MonitoringObjectHealthState": "Success", "MonitoringObjectId": "4061a4f4-b15c-b53c-bec1-727ae4aeb9d5", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "Share_A1790_COGENIUS_IAMA1790_COGENIUS", "MonitoringObjectPath": "IAMNCIWWWV005.msnet.railb.be", "MonitoringRuleId": "b23afe38-047b-b324-5b56-defe7e4cac7b", "Name": "Custom Application Share", "NetbiosComputerName": "IAMNCIWWWV005", "NetbiosDomainName": "MSNET", "Owner": null, "Parameters": "", "PrincipalName": "IAMNCIWWWV005.msnet.railb.be", "Priority": "High", "ProblemId": "b23afe38-047b-b324-5b56-defe7e4cac7b", "RepeatCount": "0", "ResolutionState": "255", "ResolvedBy": "System", "RuleId": "b23afe38-047b-b324-5b56-defe7e4cac7b", "Severity": "Error", "SiteName": null, "StateLastModified": "10/02/2025 04:12:52", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "10/02/2025 04:09:52", "TimeRaised": "10/02/2025 04:09:52", "TimeResolutionStateLastModified": "10/02/2025 04:12:52", "TimeResolved": "10/02/2025 04:12:52", "UnformattedDescription": "Custom::__AlertGroup__=Share::__CI_ID__=A1790_IAMNCIWWWV005::__MonitoredElementName__=IAMA1790_COGENIUS::__Platform__=Windows::__Summary__=UNC \\\\msnet.railb.be\\iap1\\IAM\\IAMA1790 is available. But access permission is not OK. ::"}, "output": {"ci_id": "A1790_IAMNCIWWWV005", "metric_type": "/ShareAvailability/", "metric_name": "IAMA1790_COGENIUS"}}, {"input": {"DisplayString": "Health Service Heartbeat Failure", "Category": "StateCollection", "ClassId": "a4899740-ef2f-1541-6c1d-51d34b739491", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.Availability.StateData\" time=\"2025-02-25T09:15:42.4251695+01:00\" sourceHealthServiceId=\"382A6556-3FBB-0F4B-6D42-2754FE54A7A7\"><ManagementGroupId>{01232B72-5C22-A6FE-4DED-56C043A6DE06}</ManagementGroupId><HealthServiceId>81412F40-D625-D49F-612D-C2BEF8893AF6</HealthServiceId><HostName>IICTBIEMWS002.msnet.railb.be</HostName><Reachability ThruServer=\"false\"><State>0</State></Reachability></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The System Center Management Health Service on computer IICTBIEMWS002.msnet.railb.be failed to heartbeat.", "Id": "de361d58-c798-4428-97b2-30dc6b8dd3fe", "IsMonitorAlert": "True", "LastModified": "25/02/2025 08:16:15", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "25/02/2025 08:16:15", "MaintenanceModeLastModified": "16/01/2024 15:09:18", "MonitoringClassId": "a4899740-ef2f-1541-6c1d-51d34b739491", "MonitoringObjectDisplayName": "IICTBIEMWS002.msnet.railb.be", "MonitoringObjectFullName": "Microsoft.SystemCenter.HealthServiceWatcher:Microsoft.SystemCenter.AgentWatchersGroup;81412f40-d625-d49f-612d-c2bef8893af6", "MonitoringObjectHealthState": "Error", "MonitoringObjectId": "600f2a3a-a787-cedf-17b0-f7177b90baa4", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "81412f40-d625-d49f-612d-c2bef8893af6", "MonitoringObjectPath": "Microsoft.SystemCenter.AgentWatchersGroup", "MonitoringRuleId": "b59f78ce-c42a-8995-f099-e705dbb34fd4", "Name": "Health Service Heartbeat Failure", "NetbiosComputerName": null, "NetbiosDomainName": null, "Owner": null, "Parameters": "", "PrincipalName": null, "Priority": "High", "ProblemId": "b59f78ce-c42a-8995-f099-e705dbb34fd4", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "b59f78ce-c42a-8995-f099-e705dbb34fd4", "Severity": "Error", "SiteName": null, "StateLastModified": "25/02/2025 08:15:42", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "25/02/2025 08:15:42", "TimeRaised": "25/02/2025 08:15:42", "TimeResolutionStateLastModified": "25/02/2025 08:15:42", "TimeResolved": "", "UnformattedDescription": "The System Center Management Health Service on computer IICTBIEMWS002.msnet.railb.be failed to heartbeat."}, "output": {"ci_id": "IICTBIEMWS002", "metric_type": "/ServiceAvailability/", "metric_name": "HealthService"}}, {"input": {"DisplayString": "Failed to Connect to Computer", "Category": "StateCollection", "ClassId": "a4899740-ef2f-1541-6c1d-51d34b739491", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"MonitorTaskDataType\" time=\"2025-02-25T09:16:00.2142504+01:00\" sourceHealthServiceId=\"00000000-0000-0000-0000-000000000000\"><StateChange><DataItem time=\"2025-02-25T09:15:42.4407814+01:00\" type=\"System.Health.MonitorStateChangeData\" sourceHealthServiceId=\"382A6556-3FBB-0F4B-6D42-2754FE54A7A7\" MonitorExists=\"true\" DependencyInstanceId=\"00000000-0000-0000-0000-000000000000\" DependencyMonitorId=\"00000000-0000-0000-0000-000000000000\"><ManagedEntityId>600F2A3A-A787-CEDF-17B0-F7177B90BAA4</ManagedEntityId><EventOriginId>CF6C2D9C-CA6B-474D-BF87-5827DB7374E7</EventOriginId><MonitorId>B59F78CE-C42A-8995-F099-E705DBB34FD4</MonitorId><ParentMonitorId>A6C69968-61AA-A6B9-DB6E-83A0DA6110EA</ParentMonitorId><HealthState>3</HealthState><OldHealthState>1</OldHealthState><TimeChanged>2025-02-25T09:15:42.4407814+01:00</TimeChanged><Context><DataItem type=\"System.Availability.StateData\" time=\"2025-02-25T09:15:42.4251695+01:00\" sourceHealthServiceId=\"382A6556-3FBB-0F4B-6D42-2754FE54A7A7\"><ManagementGroupId>{01232B72-5C22-A6FE-4DED-56C043A6DE06}</ManagementGroupId><HealthServiceId>81412F40-D625-D49F-612D-C2BEF8893AF6</HealthServiceId><HostName>IICTBIEMWS002.msnet.railb.be</HostName><Reachability ThruServer=\"false\"><State>0</State></Reachability></DataItem></Context></DataItem></StateChange><Diagnostic><DataItem type=\"System.PropertyBagData\" time=\"2025-02-25T09:15:42.5970413+01:00\" sourceHealthServiceId=\"382A6556-3FBB-0F4B-6D42-2754FE54A7A7\"><Property Name=\"StatusCode\" VariantType=\"8\">11010</Property><Property Name=\"ResponseTime\" VariantType=\"8\"></Property></DataItem></Diagnostic></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The computer IICTBIEMWS002.msnet.railb.be was not accessible.", "Id": "f505e3ff-c935-45f8-bc7e-eee4d10b25ce", "IsMonitorAlert": "True", "LastModified": "25/02/2025 08:16:15", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "25/02/2025 08:16:15", "MaintenanceModeLastModified": "16/01/2024 15:09:18", "MonitoringClassId": "a4899740-ef2f-1541-6c1d-51d34b739491", "MonitoringObjectDisplayName": "IICTBIEMWS002.msnet.railb.be", "MonitoringObjectFullName": "Microsoft.SystemCenter.HealthServiceWatcher:Microsoft.SystemCenter.AgentWatchersGroup;81412f40-d625-d49f-612d-c2bef8893af6", "MonitoringObjectHealthState": "Error", "MonitoringObjectId": "600f2a3a-a787-cedf-17b0-f7177b90baa4", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "81412f40-d625-d49f-612d-c2bef8893af6", "MonitoringObjectPath": "Microsoft.SystemCenter.AgentWatchersGroup", "MonitoringRuleId": "308c0379-f7f0-0a81-a947-d0dbcf1216a7", "Name": "Failed to Connect to Computer", "NetbiosComputerName": null, "NetbiosDomainName": null, "Owner": null, "Parameters": "", "PrincipalName": null, "Priority": "High", "ProblemId": "308c0379-f7f0-0a81-a947-d0dbcf1216a7", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "308c0379-f7f0-0a81-a947-d0dbcf1216a7", "Severity": "Error", "SiteName": null, "StateLastModified": "25/02/2025 08:15:42", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "25/02/2025 08:16:00", "TimeRaised": "25/02/2025 08:16:00", "TimeResolutionStateLastModified": "25/02/2025 08:16:00", "TimeResolved": "", "UnformattedDescription": "The computer IICTBIEMWS002.msnet.railb.be was not accessible."}, "output": {"ci_id": "IICTBIEMWS002", "metric_type": "/System/NetworkConnectivity/", "metric_name": "PING"}}, {"input": {"DisplayString": "AD Site Availability Health Degraded", "Category": "Custom", "ClassId": "adf4f2cc-8380-9b9b-532e-20306371d65a", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": null, "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "More than 60% of the DCs contained in this AD Site report an Availability Health problem]", "Id": "04907632-8cdc-492d-88a4-3c1a6f2e1ec3", "IsMonitorAlert": "True", "LastModified": "15/03/2025 07:34:45", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "15/03/2025 07:34:45", "MaintenanceModeLastModified": "11/12/2023 17:21:10", "MonitoringClassId": "adf4f2cc-8380-9b9b-532e-20306371d65a", "MonitoringObjectDisplayName": "MONCEAU", "MonitoringObjectFullName": "Microsoft.Windows.Server.AD.Library.Site:msnet.railb.be;MONCEAU", "MonitoringObjectHealthState": "Error", "MonitoringObjectId": "737991ef-e6aa-343c-0bfd-822ba5690c1b", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "MONCEAU", "MonitoringObjectPath": "msnet.railb.be", "MonitoringRuleId": "a42c964a-2826-e972-2196-a2c195ed01b0", "Name": "AD Site Availability Health Degraded", "NetbiosComputerName": null, "NetbiosDomainName": null, "Owner": null, "Parameters": "", "PrincipalName": null, "Priority": "High", "ProblemId": "a42c964a-2826-e972-2196-a2c195ed01b0", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "a42c964a-2826-e972-2196-a2c195ed01b0", "Severity": "Error", "SiteName": null, "StateLastModified": "15/03/2025 07:33:54", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "15/03/2025 07:33:54", "TimeRaised": "15/03/2025 07:33:54", "TimeResolutionStateLastModified": "15/03/2025 07:33:54", "TimeResolved": "", "UnformattedDescription": "More than 60% of the DCs contained in this AD Site report an Availability Health problem]"}, "output": {"ci_id": "AD", "metric_type": "/ServiceAvailability/", "metric_name": "AD_Site_Availability_Health_Degraded"}}]}