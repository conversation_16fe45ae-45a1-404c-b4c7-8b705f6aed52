"""Details design implementation for mon-dica."""

import json
from datetime import datetime, timedelta

import pandas as pd

from mon_dica import utils
from olympus_common import enums
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _agent() -> str:
    """Return the agent name for the DD."""
    return "DICA"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def actionable_alarm(row: pd.Series) -> bool:
    """Return the actionable alarm value."""
    trap_description: str = row["trap_description"]

    if utils.is_heartbeat(trap_description):
        return True

    if alarm_type(row) == enums.AlarmType.RESOLUTION.value:
        return False

    if row["severity"] == enums.Severity.INDETERMINATE.value:
        return False

    return False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def additional_data(row: pd.Series) -> str:
    """Return the additional data."""
    primary_rate_state = row["primary_rate_state"]
    signalling_state = row["signalling_state"]
    msg = f"Alarm has: Primary Rate State {primary_rate_state} and Signalling State {signalling_state}."
    return json.dumps({"additional_info": msg})


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_type() -> str:
    """Return the clear_type / event_type."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def ci_id(row: pd.Series) -> str:
    """Return the CI ID value."""
    server_identity: str = row["server_identity"]
    return server_identity


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def manager() -> str:
    """Return the manager."""
    return "SNMP_162"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def node(row: pd.Series) -> str:
    """Return the node."""
    trap_description: str = row["trap_description"]
    server_ip_address: str = row["server_ip_address"]
    if utils.is_heartbeat(trap_description):
        return server_ip_address
    else:
        application_name: str = row["application_name"]
        if server_ip_address:
            return f"{application_name} - {server_ip_address}"
        else:
            server_identity: str = row["server_identity"]
            return f"{application_name} - {server_identity}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def node_alias(row: pd.Series) -> str:
    """Return the node alias."""
    trap_description: str = row["trap_description"]
    if utils.is_heartbeat(trap_description):
        return row["server_ip_address"]
    else:
        return row["server_identity"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def scope() -> str:
    """Return the scope."""
    return enums.Scope.TE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def severity(row: pd.Series) -> int:
    """Return the severity."""
    alarm_priority: int = int(row["alarm_priority"])
    return utils.conversion_to_severity(alarm_priority)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def summary(row: pd.Series) -> str:
    """Return the summary of the event."""
    trap_description: str = row["trap_description"]
    if utils.is_heartbeat(trap_description):
        return "DICA Heartbeat Message"
    else:
        return trap_description


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def top_level(row: pd.Series) -> str:
    """Return the top level."""
    trap_description: str = row["trap_description"]
    if utils.is_heartbeat(trap_description):
        return "A1332"
    else:
        return "DICA"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def alarm_type(row: pd.Series) -> str:
    """Return the alarm type."""
    alarm_priority: int = int(row["alarm_priority"])
    return utils.conversion_to_alarmtype(alarm_priority)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def handle_time() -> datetime:
    """Return the handle time."""
    return olympus_utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def wake_up_time(row: pd.Series) -> datetime:
    """Return the wake up time."""
    return raise_time(row) + timedelta(0, delay())


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def delay() -> int:
    """Return the delay for Solman events snooze mechanism.

    Currently all the events should be snooze during 300 seconds.
    """
    return 60


def raise_time(row: pd.Series) -> datetime:
    """Return the raise time."""
    alarm_datetime: str = row["alarm_dateTime"]
    return datetime.strptime(alarm_datetime, "%Y-%m-%d %H:%M:%S")


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Run the DICA detailed design."""
    return transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    # harcoded values
    df["agent_id"] = agent_id
    df["manager"] = manager()
    df["action_class"] = scope()
    df["handle_time"] = handle_time()
    df["clear_type"] = clear_type()

    # computed values
    df["severity"] = df.apply(severity, axis=1)  # Ensure that this is called before actionable
    df["additional_data"] = df.apply(additional_data, axis=1)
    df["node"] = df.apply(node, axis=1)
    df["node_alias"] = df.apply(node_alias, axis=1)
    df["top_level"] = df.apply(top_level, axis=1)
    df["ci_id"] = df.apply(ci_id, axis=1)
    df["raise_time"] = df.apply(raise_time, axis=1)
    df["wake_up_time"] = df.apply(wake_up_time, axis=1)
    df["summary"] = df.apply(summary, axis=1)
    df["actionable"] = df.apply(actionable_alarm, axis=1)
    df["event_type"] = df.apply(alarm_type, axis=1)

    return df
