"""Patterns module for mon-struxureware.

This module contains all compiled regular expression patterns used in the mon-struxureware module.
All patterns are pre-compiled for better performance and organized by their usage context.
"""

import re

# Alert Group Classification Patterns

EXTERNAL_D_PATTERN = re.compile(r"^EXTERNAL - D[12]")
INVERTER_MAJOR_PATTERN = re.compile(r"^INVERTER - MAJOR")
INVERTER_GENERAL_PATTERN = re.compile(r"^INVERTER")
RECTIFIER_MAJOR_PATTERN = re.compile(r"^RECTIFIER - MAJOR")
RECTIFIER_GENERAL_PATTERN = re.compile(r"^RECTIFIER")
SYSTEM_MAJOR_PATTERN = re.compile(r"^SYSTEM - MAJOR")
SYSTEM_GENERAL_PATTERN = re.compile(r"^SYSTEM")

# Zone and Location Patterns

ZONE_DETECTION_PATTERN = re.compile(r"ZONE\w* - .")
ZONE_CAPTURE_PATTERN = re.compile(r"^(ZON<PERSON>\w* - .)")
ZONE_FAULT_PATTERN = re.compile(r"ZONE\w+ - F\w+\.\w+:")

# General Text Parsing Patterns

FIRST_WORD_PATTERN = re.compile(r"^(\S+)")


def classify_alert_group(alert_group: str) -> str:
    """Classify the alert group and return the appropriate monitored element name.

    Parameters
    ----------
        alert_group: The upper-cased alert group string to classify

    Returns
    -------
        The classified monitored element name
    """
    if EXTERNAL_D_PATTERN.match(alert_group):
        return alert_group

    if INVERTER_MAJOR_PATTERN.match(alert_group):
        return "INVERTER - MAJOR"
    if RECTIFIER_MAJOR_PATTERN.match(alert_group):
        return "RECTIFIER - MAJOR"
    if SYSTEM_MAJOR_PATTERN.match(alert_group):
        return "SYSTEM - MAJOR"

    if INVERTER_GENERAL_PATTERN.match(alert_group):
        return "INVERTER - MINOR"
    if RECTIFIER_GENERAL_PATTERN.match(alert_group):
        return "RECTIFIER - MINOR"
    if SYSTEM_GENERAL_PATTERN.match(alert_group):
        return "SYSTEM - MINOR"

    if ZONE_DETECTION_PATTERN.search(alert_group):
        if match := ZONE_CAPTURE_PATTERN.match(alert_group):
            return match.group(1)
        return "N/A"

    if match := FIRST_WORD_PATTERN.match(alert_group):
        return match.group(1)

    return "N/A"


def is_zone_fault_pattern(summary: str) -> bool:
    """Check if the summary matches a zone fault pattern for delay calculation.

    Parameters
    ----------
        summary: The summary string to check

    Returns
    -------
        True if it matches the zone fault pattern, False otherwise
    """
    return bool(ZONE_FAULT_PATTERN.match(summary))
