"""OID mapping for Stonebranch."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Stonebranch."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "snmptrap.opsTaskAgent": "ops_task_agent",
            "snmptrap.opsTaskExecId": "ops_task_exec_id",
            "snmptrap.opsTaskName": "ops_task_name",
            "snmptrap.opsTaskSeverity": "ops_task_severity",
            "snmptrap.opsTaskStatusCode": "ops_task_status_code",
            "snmptrap.opsTaskType": "ops_task_type",
            "tags": "tags",
            "type": "type",
            "@timestamp": "timestamp",
            "snmptrap.opsConnectorSeverity": "ops_connector_severity",
            "snmptrap.opsConnectorName": "ops_connector_name",
            "snmptrap.opsConnectorMode": "ops_connector_mode",
            "snmptrap.opsConnectorType": "ops_connector_type",
            "message": "message",
        }

    @property
    def drop_if_all_nan(self) -> list[str]:
        """Return fields to drop if all values are NaN."""
        return ["ops_task_name", "ops_connector_name", "ops_task_type"]
