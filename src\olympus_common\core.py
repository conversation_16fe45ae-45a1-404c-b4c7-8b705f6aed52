"""Generic core module."""

import logging
import time
from functools import wraps
from typing import Callable

from olympus_common import profiling
from olympus_common.datareaders import TDataReader
from olympus_common.datawriters import TDataWriter
from olympus_common.elastic_apm import elastic_apm, trace_scan
from olympus_common.enums import MeasureType
from olympus_common.logger import Logger
from olympus_common.monitoring.monitoring_manager import MonitoringManager


class Application:
    """Represent an Application to provide a framework for client-apps to run their detail design.

    Profiling can be enabled by setting the `profiling_enabled` parameter to True.
    This will create a cProfile file with the name `run_profile_{timestamp}.cprofile` in the logs_directory.
    To view the results, install snakeviz and use it. (snakeviz <path_to_cprofile_file>)
    """

    def __init__(
        self,
        datareader: TDataReader,
        datawriter: TDataWriter,
        logger: Logger | None = None,
        monitoring_manager: MonitoringManager | None = None,
        profiling_enabled: bool = False,
    ) -> None:
        self.datareader = datareader
        self.datawriter = datawriter
        self.logger = self._setup_logging(logger)
        self.monitoring_manager = monitoring_manager
        self.profiling_enabled = profiling_enabled

    def run_forever(self, sleep_time: int = 15):
        """Run the decorated function in a while True loop."""

        def _decorator(fn: Callable[[list[dict]], list[dict]]):
            @wraps(fn)
            def _inner():
                while True:
                    self._run(fn)
                    logging.debug(f"Sleeping {sleep_time} seconds.")
                    time.sleep(sleep_time)

            return _inner

        return _decorator

    def run_once(self):
        """Run the decorated function once."""

        def _decorator(fn: Callable[[list[dict]], list[dict]]):
            @wraps(fn)
            def _inner():
                self._run(fn)

                if self.monitoring_manager:
                    self.monitoring_manager.stop()

            return _inner

        return _decorator

    @trace_scan(transaction_type=MeasureType.CUSTOM.value)
    def _run(self, fn: Callable[[list[dict]], list[dict]]) -> bool:
        """Read data, run the provided `fn` and call appropriate handlers.

        The error handlers are called when the `fn` raises any Exception when it's called.
        The success handlers are called when the `fn` executes without raising anything.

        Parameters
        ----------
        fn : Callable[[list[dict]], list[dict]]
            The function which should be called.

        Returns
        -------
        bool
            True if the function was called and executed without error, False otherwise.
        """
        if self.profiling_enabled:
            profiler = profiling.start()

        data = self.datareader.read_data()
        if not data:
            logging.debug(f"No data for {self.datareader}")
            if self.profiling_enabled:
                profiling.stop(profiler)
            return False

        try:
            results = fn(data)
        except Exception as exc:
            logging.exception(exc)
            success = False
            self.datawriter.error(data, exc)
            self.datareader.error(data, exc)
            elastic_apm.capture_exception()
            if self.monitoring_manager:
                self.monitoring_manager.send_error(str(exc))
            raise RuntimeError(f"Error while running function {fn.__name__}: {exc}") from exc
        else:
            success = True
            try:
                self.datawriter.success(results)
                self.datareader.success(data)
                if self.monitoring_manager:
                    self.monitoring_manager.send_success("mon-messages-processed", f"{len(results)} messages processed")
                    self.monitoring_manager.send_clear()

            except Exception as exc:
                logging.exception(exc)
                success = False
                self.datareader.error(data, exc)
                elastic_apm.capture_exception()
                if self.monitoring_manager:
                    self.monitoring_manager.send_error(str(exc))
                raise RuntimeError(f"Error while writing data: {exc}") from exc
        finally:
            if self.profiling_enabled:
                profiling.stop(profiler, self.logger.folder)

        if success and self.monitoring_manager and not self.monitoring_manager.started:
            # Only start the monitoring manager on success and only if it is not already started. This ensures that
            # the monitoring manager is started only once and not on every run.
            # It also prevents sending any heartbeats if the application fails to run. Whereas if we call this in the
            # __init__ of the Application, it would start the monitoring manager immediately, even if the application
            # fails to run.
            self.monitoring_manager.start()

        logging.debug(f"Ran function {fn.__name__} ({success=})")
        return success

    @staticmethod
    def _setup_logging(logger: Logger | None) -> Logger:
        """Set up the logging for the Application by calling `setup` on the `logger`.

        Parameters
        ----------
        logger : Logger | None
            If logger is falsy, a default `Logger` is instantiated.
        """
        logger = logger or Logger()
        logger.setup()
        return logger
