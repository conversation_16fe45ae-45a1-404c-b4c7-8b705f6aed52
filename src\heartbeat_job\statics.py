"""Static variables used in heartbeat_job."""

GET_MISSING_HEARTBEATS_QUERY = """
WITH
	HEARTBEAT_STATUS AS (
		SELECT
			AHB.AGENT_ID,
			AHB.CI_ID,
			AHB.METRIC_NAME,
			AHB.METRIC_TYPE,
			AHB.HEARTBEAT_TYPE,
			CASE
				WHEN AHB.HEARTBEAT_TYPE = 'period' THEN (
					SELECT
						RAISE_TIME
					FROM
						S2110_OCCURRENCE
					WHERE
						AGENT_ID = AHB.AGENT_ID
						AND CI_ID = AHB.CI_ID
						AND METRIC_NAME = AHB.METRIC_NAME
						AND METRIC_TYPE = AHB.METRIC_TYPE
						AND EVENT_TYPE = 'heartbeat'
					ORDER BY
						RAISE_TIME DESC
					LIMIT
						1
				)
				WHEN AHB.HEARTBEAT_TYPE = 'idle_time' THEN (
					SELECT
						RAISE_TIME
					FROM
						S2110_OCCURRENCE
					WHERE
						AGENT_ID = AHB.AGENT_ID
						AND EVENT_TYPE IN ('problem', 'clear')
						AND NOT (
							METRIC_NAME = AHB.METRIC_NAME
							AND METRIC_TYPE = AHB.METRIC_TYPE
						)
					ORDER BY
						RAISE_TIME DESC
					LIMIT
						1
				)
			END AS LAST_TIME,
			AHB.PERIOD_SECONDS,
			AHB.IDLE_TIME_SECONDS,
			(
				TIMEZONE ('UTC', CURRENT_TIMESTAMP) - MAKE_INTERVAL(SECS => AHB.PERIOD_SECONDS)
			) AS PERIOD_THRESHOLD,
			(
				TIMEZONE ('UTC', CURRENT_TIMESTAMP) - MAKE_INTERVAL(SECS => AHB.IDLE_TIME_SECONDS)
			) AS IDLE_TIME_THRESHOLD,
			AG.NAME AS AGENT_NAME,
			AG.ACTION_CLASS
		FROM
			S2110_AGENT_HEARTBEAT AHB
			JOIN S2110_AGENT AG ON AHB.AGENT_ID = AG.ID
		WHERE
			AHB.IS_ACTIVE = TRUE
	)
SELECT
	*
FROM
	HEARTBEAT_STATUS
WHERE
    (
        HEARTBEAT_TYPE = 'period'
        AND LAST_TIME < PERIOD_THRESHOLD
    )
    OR (
        HEARTBEAT_TYPE = 'idle_time'
        AND LAST_TIME < IDLE_TIME_THRESHOLD
    )
"""
