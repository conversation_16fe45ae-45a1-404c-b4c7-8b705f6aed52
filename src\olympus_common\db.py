"""Module to provide database capabilities to an application."""

import json
import logging
from collections import defaultdict
from dataclasses import asdict, dataclass
from datetime import datetime
from typing import Any, Optional, Self, Sequence

from sqlalchemy import (
    ColumnElement,
    Computed,
    ForeignKey,
    and_,
    create_engine,
    delete,
    literal_column,
    or_,
    select,
    update,
)
from sqlalchemy.dialects.postgresql import JSONB, insert
from sqlalchemy.orm import DeclarativeBase, Mapped, Session, mapped_column, relationship, sessionmaker

from olympus_common import enums
from olympus_common.config import DatabaseConfig
from olympus_common.dataclass import TDataclass
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import AlarmJobStatus, AlarmType, MeasureType
from olympus_common.utils import now_naive


@dataclass
class Base(DeclarativeBase):
    """Base class for all dataclasses.

    This class adds an id field as primary key.
    """

    type_annotation_map = {dict[str, Any]: JSONB}
    id: Mapped[int] = mapped_column(primary_key=True)

    def asdict(self, extra_keys: set[str] | None = None) -> dict[str, Any]:
        """Return a dictionary representation of this object.

        The provided extra_keys will be popped from the dictionary if they are present.

        This is a replacement for dataclasses.asdict to account for UNSET fields.
        """
        d = asdict(self)
        keys2pop: set[str] = set()  # Pop later to avoid RuntimeError: dictionary changed size during iteration
        if extra_keys is not None:
            keys2pop.update(extra_keys)
        for k, v in d.items():
            if v is _Unset:
                keys2pop.add(k)
        for k in keys2pop:
            d.pop(k, None)
        return d

    @classmethod
    def columns(cls: type[TDataclass]) -> list[str]:
        """Return a list of columns for this class."""
        return list(cls.__dataclass_fields__.keys())

    @classmethod
    def column_types(cls: type[TDataclass]) -> dict[str, type]:
        """Return a dictionary where the keys are the field name and the values are the respective type."""
        return {name: field.type for name, field in cls.__dataclass_fields__.items()}  # type: ignore[misc]

    @classmethod
    def from_dict(cls, orig_dict: dict[str, Any], **kwargs) -> Self:
        """Construct a class instance from the provided orig_dict, overridden with the provided kwargs.

        kwargs are used to override (or add) certain fields to the BackendTable. This can be useful if for example the
        provided orig_dict does not include the scope_ref field. We could then call the function in the following
        manner: BackendTable.from_dict(mydict, scope_ref="foo"), which will return an BackendTable with scope_ref="foo",
        if the scope_ref="foo" was missing and the orig_dict did not include the scope_ref key, this method would error
        because it is missing a required value.

        Columns which are not present on the BackendTable object, but are present in the provided orig_dict will be
        silently dropped.

        Columns which are present on BackendTable, but not present in the provided orig_dict or in the provided kwargs
        will raise an error upon initializing the BackendTable.

        Notes
        -----
        The provided kwargs will overwrite values in the orig_dict. This means that if your orig_dict has a scope_ref
        key, but you pass scope_ref as a keyword argument, the passed keyword argument's value will be used instead of
        the orig_dict's value.

        This method can also be useful if you have a pd.Series. You could then call this method like so:
        BackendTable.from_dict(row.as_dict(), extra_kwarg="foo")
        """
        filtered_row_dict = {key: value for key, value in orig_dict.items() if key in cls.columns()}
        for column, value in filtered_row_dict.items():
            column_type = cls.column_types().get(column)
            if column_type == Mapped[Optional[dict[str, Any]]] and type(value) is str:
                # For JSON columns, the value received from pandas is serialized, so we de-serialize it.
                filtered_row_dict[column] = json.loads(value) if value else None
            elif column_type in [Mapped["Enrichment"], Mapped[Optional["Enrichment"]]]:
                filtered_row_dict[column] = Enrichment.from_dict(value) if value else None
            elif column_type in [Mapped["Occurrence"], Mapped[Optional["Occurrence"]]]:
                filtered_row_dict[column] = Occurrence.from_dict(value) if value else None
            elif column_type in [Mapped["Agent"], Mapped[Optional["Agent"]]]:
                filtered_row_dict[column] = Agent.from_dict(value) if value else None
            elif column_type in [Mapped["Incident"], Mapped[Optional["Incident"]]]:
                filtered_row_dict[column] = Incident.from_dict(value) if value else None
            elif column_type in [Mapped["Release"], Mapped[Optional["Release"]]]:
                filtered_row_dict[column] = Release.from_dict(value) if value else None
        combined = {**filtered_row_dict, **kwargs}
        return cls(**combined)

    @classmethod
    def _item(cls, data: dict | Self) -> Self:
        """Convert the provided data to an instance of this class if it's a dictionary.

        If it's not a dictionary, we return the provided data.
        """
        if isinstance(data, dict):
            return cls.from_dict(data)
        return data

    @CaptureSpan(MeasureType.DB_QUERY.value)
    def insert_object(self, session: Session) -> Self | None:
        """Insert this object in the database."""
        asdict = self.asdict(extra_keys={"identifier_hash"})
        object_type = type(self)
        return session.scalar(insert(object_type).on_conflict_do_nothing().returning(object_type), asdict)

    @classmethod
    @CaptureSpan(MeasureType.DB_QUERY.value)
    def insert_objects(cls, session: Session, data: list[Self]):
        """Insert the provided objects in the database."""
        data_dicts = [item.asdict(extra_keys={"identifier_hash"}) for item in data]
        return session.scalars(insert(cls).on_conflict_do_nothing().returning(cls), data_dicts)

    @CaptureSpan(MeasureType.DB_QUERY.value)
    def update_object(self, session: Session) -> Self | None:
        """Update this object in the database."""
        asdict = self.asdict(extra_keys={"identifier_hash"})
        object_type = type(self)
        return session.scalar(update(object_type).returning(object_type).where(object_type.id == self.id), asdict)

    @classmethod
    @CaptureSpan(MeasureType.DB_QUERY.value)
    def update_objects(cls, session: Session, data: list[Self]):
        """Update the provided objects in the database."""
        data_dicts = [item.asdict(extra_keys={"identifier_hash"}) for item in data]
        return session.execute(update(cls), data_dicts)

    @CaptureSpan(MeasureType.DB_QUERY.value)
    def delete_object(self, session: Session) -> None:
        """Delete this object from the database."""
        asdict = self.asdict()
        object_type = type(self)
        session.scalar(delete(object_type).returning(object_type).where(object_type.id == self.id), asdict)

    @classmethod
    @CaptureSpan(MeasureType.DB_QUERY.value)
    def delete_objects(cls, session: Session, ids: list[int]):
        """Delete these objects from the database."""
        session.execute(delete(cls).where(cls.id.in_(ids)))


@dataclass
class Agent(Base):
    """Represent an agent."""

    __tablename__ = "s2110_agent"

    name: Mapped[Optional[str]] = mapped_column()
    ucmdb_name: Mapped[Optional[str]] = mapped_column()
    ci_enrichment: Mapped[bool] = mapped_column(default=True)
    metric_enrichment: Mapped[bool] = mapped_column(default=True)
    topology_enrichment: Mapped[bool] = mapped_column(default=True)
    action_class: Mapped[Optional[str]] = mapped_column()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_by_name(cls, name: str, session: Session) -> Optional["Agent"]:
        """Select an agent based on its name."""
        statement = select(Agent).where(literal_column("name") == name)
        return session.execute(statement).scalar()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_agent_id_from_name(cls, name: str, session: Session) -> int:
        """Get the agent ID from the agent name.

        If no agent is found, 0 is returned.
        """
        if not (agent := cls.get_by_name(name=name, session=session)):
            return 0
        return agent.id

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_agent_id_from_names(cls, names: list[str], session: Session) -> Sequence[int]:
        """Get the agent IDs from the agent names."""
        stmt = select(Agent.id).where(Agent.name.in_(names))
        return session.scalars(stmt).all()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_by_id(cls, id: int, session: Session) -> Optional["Agent"]:
        """Select an agent based on its name."""
        statement = select(Agent).where(literal_column("id") == id)
        return session.execute(statement).scalar()

    def update_object(self, session: Session) -> Self | None:
        """Do nothing when attempting to update this object.

        This is a configuration object and should not be updated.
        """
        return None

    def delete_object(self, session: Session) -> None:
        """Do nothing when attempting to delete this object.

        This is a configuration object and should not be deleted.
        """
        return None


@dataclass
class AgentHeartbeat(Base):
    """Represent an agent heartbeat."""

    __tablename__ = "s2110_agent_heartbeat"

    agent_id: Mapped[Optional[int]] = mapped_column(ForeignKey("s2110_agent.id"))
    agent: Mapped["Agent"] = relationship("Agent", foreign_keys=[agent_id], uselist=False)
    ci_id: Mapped[Optional[str]] = mapped_column()
    metric_type: Mapped[Optional[str]] = mapped_column()
    metric_name: Mapped[Optional[str]] = mapped_column()
    heartbeat_type: Mapped[Optional[str]] = mapped_column()
    period_seconds: Mapped[Optional[int]] = mapped_column()
    idle_time_seconds: Mapped[Optional[int]] = mapped_column()
    is_active: Mapped[bool] = mapped_column(default=True)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_by_agent(cls, agent_id: int, session: Session) -> Optional[Sequence["AgentHeartbeat"]]:
        """Select the heartbeats of an agent."""
        statement = select(AgentHeartbeat).where(literal_column("agent_id") == agent_id)
        return session.execute(statement).scalars().all()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_heartbeat_cis(cls, agent_id: int, session_manager: "SessionManager") -> Optional[list[str]]:
        """Get the heartbeats of an agent."""
        with session_manager.get_session() as session:
            if heartbeats := AgentHeartbeat.get_by_agent(agent_id=agent_id, session=session):
                return [heartbeat.ci_id or "" for heartbeat in heartbeats]

            return None

    def update_object(self, session: Session) -> Self | None:
        """Do nothing when attempting to update this object.

        This is a configuration object and should not be updated.
        """
        return None

    def delete_object(self, session: Session) -> None:
        """Do nothing when attempting to delete this object.

        This is a configuration object and should not be deleted.
        """
        return None


@dataclass
class Occurrence(Base):
    """Represent the occurrence of an alarm."""

    __tablename__ = "s2110_occurrence"
    __uniquekey__ = "s2110_occ_uk"

    alarm_id: Mapped[Optional[int]] = mapped_column(ForeignKey("s2110_alarm.id"))
    summary: Mapped[Optional[str]] = mapped_column()
    severity: Mapped[Optional[int]] = mapped_column()
    event_type: Mapped[Optional[str]] = mapped_column()
    raise_time: Mapped[datetime] = mapped_column()
    clear_time: Mapped[Optional[datetime]] = mapped_column()
    additional_data: Mapped[Optional[dict[str, Any]]] = mapped_column(default={})
    handle_time: Mapped[Optional[datetime]] = mapped_column()
    ci_id: Mapped[Optional[str]] = mapped_column()
    agent_id: Mapped[Optional[int]] = mapped_column(ForeignKey("s2110_agent.id"))
    metric_type: Mapped[Optional[str]] = mapped_column()
    metric_name: Mapped[Optional[str]] = mapped_column()
    identifier: Mapped[Optional[str]] = mapped_column()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_one(cls, data: dict, session: Session) -> None:
        """Insert the provided data.

        The provided dict must have keys corresponding to an Event.
        """
        cls.insert_many([data], session=session)
        return

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_many(cls, data: Sequence[dict], session: Session) -> None:
        """Insert each record in the provided data.

        The provided dicts must have keys corresponding to both an Occurrence and an Alarm.
        All active alarms are fetched before the loop to avoid fetching them at each iteration.
        To configure the cache refresh interval, use the DatabaseConfig.cache_refresh_interval attribute.
        """
        if not data:
            logging.info("No data provided for Occurrence insertion.")
            return None

        cls._insert_heartbeats(data, session)
        non_heartbeats = [record for record in data if record.get("event_type") != AlarmType.HEARTBEAT.value]
        if not non_heartbeats:
            logging.info("No non-heartbeat records provided for Occurrence insertion.")
            return None  # Short-circuit to avoid unnecessary processing and queries

        batches = cls._prepare_batches(session, non_heartbeats)
        cls._execute_batches(session, **batches)
        logging.info(f"Processed {len(non_heartbeats)} non-heartbeat records in total.")
        return None

    @classmethod
    def _execute_batches(
        cls,
        session: Session,
        active_alarms: dict[str, "Alarm"],
        occurrences_to_insert: defaultdict[str, list[Self]],
        alarms_to_insert: list["Alarm"],
        alarms_to_update: list["Alarm"],
    ):
        inserted_alarms = cls._insert_new_alarms(session, alarms_to_insert)
        cls._insert_new_occurrences(session, active_alarms, occurrences_to_insert, alarms_to_update, inserted_alarms)
        cls._update_alarms(session, alarms_to_update, inserted_alarms)
        cls._insert_alarm_jobs(session, inserted_alarms)

    @classmethod
    def _insert_heartbeats(cls, data: Sequence[dict], session: Session):
        """Insert heartbeats from the provided data into the database as a single batch."""
        heartbeats = [cls._item(record) for record in data if record.get("event_type") == AlarmType.HEARTBEAT.value]
        inserted_heartbeats: list[Self] = []
        if heartbeats:
            inserted_heartbeats = cls.insert_objects(session=session, data=heartbeats).all()
            logging.info(f"Processed {len(heartbeats)} heartbeats in total.")
        return inserted_heartbeats

    @classmethod
    def _update_alarms(cls, session: Session, alarms_to_update: list["Alarm"], inserted_alarms: list["Alarm"]):
        if not alarms_to_update:
            return

        inserted_alarm_ids = {alarm.id for alarm in inserted_alarms}
        Alarm.update_objects(session=session, data=alarms_to_update)
        cls._resend_existing_alarms_to_ui(session, alarms_to_update, inserted_alarm_ids)

    @classmethod
    def _resend_existing_alarms_to_ui(
        cls, session: Session, alarms_to_update: list["Alarm"], inserted_alarm_ids: set[int]
    ):
        """Resend alarms that already existed before this batch processing to update the metrics etc."""
        resend_jobs = []
        for alarm in alarms_to_update:
            if alarm.id not in inserted_alarm_ids:
                alarm_job = AlarmJob(job_name="ui_sending", job_status=AlarmJobStatus.TO_DO, alarm_id=alarm.id)
                resend_jobs.append(alarm_job)
        if resend_jobs:
            AlarmJob.insert_objects(session=session, data=resend_jobs)

    @classmethod
    def _insert_alarm_jobs(cls, session: Session, inserted_alarms: list["Alarm"]):
        alarm_jobs_to_insert = [AlarmJob(alarm_id=alarm.id, job_name="enrichment") for alarm in inserted_alarms]
        inserted_alarm_jobs: list[AlarmJob] = []
        if alarm_jobs_to_insert:
            inserted_alarm_jobs = AlarmJob.insert_objects(session=session, data=alarm_jobs_to_insert).all()
        return inserted_alarm_jobs

    @classmethod
    def _insert_new_occurrences(
        cls,
        session: Session,
        active_alarms: dict[str, "Alarm"],
        occurrences_to_insert: defaultdict[str, list[Self]],
        alarms_to_update: list["Alarm"],
        inserted_alarms: list["Alarm"],
    ):
        if not occurrences_to_insert:
            return

        inserted_alarm_keys = {alarm.key(): alarm for alarm in inserted_alarms}
        cls._update_alarm_id(active_alarms, occurrences_to_insert, inserted_alarm_keys)
        occurrences_list = [  # flatten the occurrences list
            occurrence for occurrences in occurrences_to_insert.values() for occurrence in occurrences
        ]
        inserted_occurrences = cls.insert_objects(session=session, data=occurrences_list).all()

        grouped_occurrences = defaultdict(list)
        for occurrence in inserted_occurrences:
            grouped_occurrences[occurrence.alarm_id].append(occurrence)

        cls._update_occurrence_ids_in_alarms(alarms_to_update, inserted_alarms, grouped_occurrences)
        return inserted_occurrences

    @classmethod
    def _update_occurrence_ids_in_alarms(
        cls,
        alarms_to_update: list["Alarm"],
        inserted_alarms: list["Alarm"],
        grouped_occurrences: defaultdict[int, list[Self]],
    ):
        inserted_alarms_map = {alarm.id: alarm for alarm in inserted_alarms}
        alarms_to_update_map = {alarm.id: alarm for alarm in alarms_to_update}
        for alarm_id, occurrences in grouped_occurrences.items():
            if not occurrences:
                logging.warning(f"No occurrences for alarm with ID {alarm_id}.")
                continue
            current_alarm = alarms_to_update_map.get(alarm_id)
            is_inserted_alarm = False
            if not current_alarm:
                current_alarm = inserted_alarms_map.get(alarm_id)
                is_inserted_alarm = True

            if not current_alarm:
                logging.warning(f"Alarm with ID {alarm_id} not found for updating last occurrence IDs.")
                continue

            current_alarm.last_occurrence_id = occurrences[-1].id
            problem_ids = [occ.id for occ in occurrences if occ.event_type == AlarmType.PROBLEM.value]
            if problem_ids:
                current_alarm.last_problem_id = problem_ids[-1]
            clear_ids = [occ.id for occ in occurrences if occ.event_type == AlarmType.RESOLUTION.value]
            if clear_ids:
                current_alarm.last_clear_id = clear_ids[-1]

            if is_inserted_alarm:
                alarms_to_update.append(current_alarm)

    @classmethod
    def _update_alarm_id(
        cls,
        active_alarms: dict[str, "Alarm"],
        occurrences_to_insert: defaultdict[str, list[Self]],
        inserted_alarm_keys: dict[str, "Alarm"],
    ):
        """Update the alarm_id in occurrences_to_insert based on active_alarms and inserted_alarm_keys."""
        for key, occurrences in occurrences_to_insert.items():
            if key in inserted_alarm_keys:
                alarm = inserted_alarm_keys[key]
            else:
                alarm = active_alarms[key]

            if not alarm:
                logging.warning(f"Alarm with key {key} not found for occurrences insertion.")
                continue

            for occ in occurrences:
                occ.alarm_id = alarm.id

    @classmethod
    def _insert_new_alarms(cls, session: Session, alarms_to_insert: list["Alarm"]):
        inserted_alarms: list[Alarm] = []
        if alarms_to_insert:
            inserted_alarms = Alarm.insert_objects(session=session, data=alarms_to_insert).all()
        return inserted_alarms

    @classmethod
    def _prepare_batches(cls, session: Session, data: list[dict]):
        """Prepare batches of occurrences and alarms for insertion.

        This method processes the provided data records, preparing them for batch insertion into the database.
        """
        agent_ids = _get_agent_ids(session, data)
        active_alarms = _get_active_alarms(session, agent_ids=agent_ids)
        occurrences_to_insert: defaultdict[str, list[Occurrence]] = defaultdict(list)  # Use alarm-key as key
        alarms_to_insert: list[Alarm] = []
        alarms_to_update: list[Alarm] = []
        new_alarms: dict[str, Alarm] = {}
        for i, data_record in enumerate(data, start=1):
            occurrence = cls._item(data_record)
            alarm = Alarm._item(data_record)

            if not alarm.agent_id:
                raise ValueError(f"An agent is required for the alarm: {alarm}")

            # For clears, the raise time is always the clear time
            if occurrence.event_type == AlarmType.RESOLUTION.value and occurrence.clear_time:
                occurrence.raise_time = occurrence.clear_time

            alarm_key = alarm.key()

            existing_alarm = cls._get_existing_alarm(session, active_alarms, data_record, alarm, alarm_key)
            if not existing_alarm:
                existing_alarm = new_alarms.get(alarm_key)

            if existing_alarm:
                clear_type = alarm.clear_type
                alarm = existing_alarm
                alarm.clear_type = clear_type
                if occurrence.event_type == AlarmType.PROBLEM.value:
                    alarm.tally += 1

                # Only add alarms to update if they already exist in the database
                if alarm_key not in new_alarms:
                    alarms_to_update.append(alarm)
            else:
                # Create new alarm if not a clear without matching alarm
                if occurrence.event_type == AlarmType.RESOLUTION.value and not data_record.get(
                    "persist_clears_without_alarm", False
                ):
                    continue

                alarm.first_raise_time = occurrence.raise_time
                alarm.tally = 1
                alarms_to_insert.append(alarm)
                new_alarms[alarm_key] = alarm

            occurrences_to_insert[alarm_key].append(occurrence)

            if i % 1000 == 0:
                logging.debug(f"Prepared {i} records for batch processing.")
        return {
            "active_alarms": active_alarms,
            "occurrences_to_insert": occurrences_to_insert,
            "alarms_to_insert": alarms_to_insert,
            "alarms_to_update": alarms_to_update,
        }

    @classmethod
    def _get_existing_alarm(
        cls, session: Session, active_alarms: dict[str, "Alarm"], data_record: dict, alarm: "Alarm", alarm_key: str
    ):
        alarm_id = data_record.get("alarm_id", None)
        if alarm_id:
            existing_alarm = Alarm.get_active_alarm_by_id(session=session, alarm_id=alarm_id)
        elif active_alarms:
            existing_alarm = active_alarms.get(alarm_key)
        else:
            existing_alarm = Alarm.get_active_alarm_on_metric(
                session=session,
                agent_id=alarm.agent_id,
                ci_id=alarm.ci_id,
                metric_type=alarm.metric_type,
                metric_name=alarm.metric_name,
                identifier=alarm.identifier,
            )

        return existing_alarm

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_occurrences_of_agent(
        cls, session: Session, agent_id: int, event_type: str | None = ""
    ) -> Optional[Sequence["Occurrence"]]:
        """Retrieve the active alarm on the CI and metric."""
        statement = select(Occurrence).where(and_(Occurrence.agent_id == agent_id, Occurrence.event_type == event_type))
        return session.execute(statement).scalars().all()

    def key(self) -> tuple:
        """Create a unique key so occurrences can be stored in a set for faster lookup."""
        return (
            self.event_type,
            self.raise_time,
            self.ci_id,
            self.agent_id,
            self.metric_type,
            self.metric_name,
            self.identifier,
        )


def _get_agent_ids(session: Session, data: Sequence[dict]) -> list[int]:
    agent_ids = list({agent_id for record in data if (agent_id := record.get("agent_id"))})

    agent_names = list({agent_name for record in data if (agent_name := record.get("agent_name"))})
    if agent_names:
        all_agent_ids = set(Agent.get_agent_id_from_names(agent_names, session))
        agent_ids.extend(agent_id for agent_id in all_agent_ids if agent_id)

    if not agent_ids:
        logging.warning("No agent IDs or names found in the provided data. Fetching all active alarms.")
    return agent_ids


@CaptureSpan(span_type=MeasureType.DB_QUERY.value)
def _get_active_alarms(session: Session, agent_ids: list[int]) -> dict[str, "Alarm"]:
    """Return a dictionary of active alarms from the provided data."""
    alarms = Alarm.get_active_alarms(session, agent_ids=agent_ids)
    return {alarm.key(): alarm for alarm in alarms}


@dataclass
class Alarm(Base):
    """Represent an alarm."""

    __tablename__ = "s2110_alarm"

    last_occurrence_id: Mapped[int] = mapped_column(ForeignKey("s2110_occurrence.id"))
    last_occurrence: Mapped["Occurrence"] = relationship(
        "Occurrence", foreign_keys=[last_occurrence_id], uselist=False, cascade="merge"
    )
    last_enrichment: Mapped[Optional["Enrichment"]] = relationship(
        # Documentation can be found here:
        # https://docs.sqlalchemy.org/en/20/orm/join_conditions.html#composite-secondary-joins
        "Enrichment",
        secondary="join(AlarmEnrichment, Enrichment, AlarmEnrichment.enrichment_id == Enrichment.id)",
        primaryjoin="and_(Alarm.id==AlarmEnrichment.alarm_id, AlarmEnrichment.last_enrichment==True)",
        secondaryjoin="AlarmEnrichment.enrichment_id == Enrichment.id",
        uselist=False,
        viewonly=True,
        cascade="merge",
    )
    last_problem_id: Mapped[Optional[int]] = mapped_column(ForeignKey("s2110_occurrence.id"))
    last_problem: Mapped[Optional["Occurrence"]] = relationship(
        "Occurrence", foreign_keys=[last_problem_id], uselist=False, cascade="merge"
    )
    last_clear_id: Mapped[Optional[int]] = mapped_column(ForeignKey("s2110_occurrence.id"))
    last_clear: Mapped[Optional["Occurrence"]] = relationship(
        "Occurrence", foreign_keys=[last_clear_id], uselist=False, cascade="merge"
    )
    active_incident: Mapped[Optional["Incident"]] = relationship(
        # Documentation can be found here:
        # https://docs.sqlalchemy.org/en/20/orm/join_conditions.html#composite-secondary-joins
        "Incident",
        secondary="join(AlarmIncident, Incident, AlarmIncident.incident_id == Incident.id)",
        primaryjoin="and_(Alarm.id==AlarmIncident.alarm_id, AlarmIncident.is_link_active==True)",
        secondaryjoin="AlarmIncident.incident_id == Incident.id",
        uselist=False,
        viewonly=True,
        cascade="merge",
    )
    agent_id: Mapped[Optional[int]] = mapped_column(ForeignKey("s2110_agent.id"))
    agent: Mapped[Optional["Agent"]] = relationship("Agent", foreign_keys=[agent_id], uselist=False, cascade="merge")
    ci_id: Mapped[Optional[str]] = mapped_column()
    manager: Mapped[Optional[str]] = mapped_column()
    node: Mapped[Optional[str]] = mapped_column()
    action_class: Mapped[Optional[str]] = mapped_column()
    event_id: Mapped[Optional[str]] = mapped_column()
    node_alias: Mapped[Optional[str]] = mapped_column()
    event_url: Mapped[Optional[str]] = mapped_column()
    metric_type: Mapped[Optional[str]] = mapped_column()
    metric_name: Mapped[Optional[str]] = mapped_column()
    actionable: Mapped[Optional[bool]] = mapped_column()
    active_release: Mapped[Optional["Release"]] = relationship(
        "Release",
        secondary="join(AlarmRelease, Release, AlarmRelease.release_id == Release.id)",
        primaryjoin="and_(Alarm.id==AlarmRelease.alarm_id, AlarmRelease.is_link_active==True)",
        secondaryjoin="AlarmRelease.release_id == Release.id",
        uselist=False,
        viewonly=True,
        cascade="merge",
    )
    wake_up_time: Mapped[datetime] = mapped_column()
    tally: Mapped[int] = mapped_column(default=1)
    is_active: Mapped[bool] = mapped_column(default=True)
    first_raise_time: Mapped[Optional[datetime]] = mapped_column()
    platform: Mapped[Optional[str]] = mapped_column()
    clear_type: Mapped[Optional[str]] = mapped_column()
    top_level: Mapped[Optional[str]] = mapped_column()
    closing_time: Mapped[Optional[datetime]] = mapped_column()
    identifier: Mapped[Optional[str]] = mapped_column()
    identifier_hash: Mapped[Optional[str]] = mapped_column(Computed("md5(identifier)"), nullable=True)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_active_alarms(cls, session: Session, agent_ids: list[int] | None = None) -> Sequence["Alarm"]:
        """Retrieve the active alarms.

        If agent_ids is provided, only the alarms for those agents will be returned.
        """
        statement = select(cls).where(Alarm.is_active == 1)
        if agent_ids:
            statement = statement.where(Alarm.agent_id.in_(agent_ids))
        return session.execute(statement).scalars().all()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_alarms_by_id(cls, session: Session, alarm_ids: list[int]) -> Sequence["Alarm"]:
        """Retrieve the alarm objects from the database."""
        return session.execute(select(Alarm).where(Alarm.id.in_(alarm_ids))).scalars().all()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_alarm_by_id(cls, session: Session, alarm_id: int) -> Optional["Alarm"]:
        """Retrieve an alarm by its ID."""
        statement = select(Alarm).where((Alarm.id == alarm_id))
        return session.execute(statement).scalar()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_active_alarm_by_id(cls, session: Session, alarm_id: int) -> Optional[Self]:
        """Retrieve an active alarm by its ID."""
        statement = select(cls).where(and_(cls.id == alarm_id, cls.is_active == 1))
        return session.execute(statement).scalar()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_active_alarm_on_metric(
        cls,
        session: Session,
        agent_id: int | None = 0,
        ci_id: str | None = "",
        metric_type: str | None = "",
        metric_name: str | None = "",
        agent_name: str | None = "",
        identifier: str | None = "",
    ) -> Optional["Alarm"]:
        """Retrieve the active alarm on the CI, metric and identifier.

        If agent_id is provided, agent_name will be ignored.
        Only truthy values will be added to the where clause.
        """
        if not agent_id and not agent_name and not identifier:
            return None

        statement = select(Alarm)
        kwargs = {
            Alarm.agent_id: agent_id,
            Alarm.ci_id: ci_id,
            Alarm.metric_type: metric_type,
            Alarm.metric_name: metric_name,
            Alarm.identifier: identifier,
        }
        if not agent_id and agent_name:
            statement = statement.join(Agent, Alarm.agent_id == Agent.id).where(Agent.name == agent_name)
            kwargs.pop(Alarm.agent_id)

        for column, value in kwargs.items():
            if not value:
                continue  # Only add a where clause for provided values.
            statement = statement.where(column == value)

        statement = statement.where(Alarm.is_active == 1)
        return session.execute(statement).scalar()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_active_alarms_on_ci(
        cls, session: Session, agent_id: int | None = 0, ci_id: str | None = ""
    ) -> Optional[Sequence["Alarm"]]:
        """Retrieve the active alarm on the CI and metric."""
        statement = select(Alarm).where(
            and_(
                Alarm.agent_id == agent_id,
                Alarm.ci_id == ci_id,
                Alarm.is_active == 1,
            )
        )
        return session.execute(statement).scalars().all()

    def insert_one(self, session: Session) -> Optional["Alarm"]:
        """Insert a single alarm."""
        if not (alarm := self.insert_object(session)):
            return None

        alarm_job = AlarmJob(job_name="enrichment", job_status=AlarmJobStatus.TO_DO)
        alarm_job.alarm_id = alarm.id
        alarm_job.insert_object(session)

        return alarm

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def delete_one(self, session: Session) -> None:
        """Delete a single alarm."""
        self.cascade_jobs(session)
        self.delete_object(session)

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def cascade_jobs(self, session: Session) -> None:
        """Cascade delete all jobs related to this alarm.

        Do note that the session is not committed after this operation, so the caller must do it.
        """
        stmt = delete(AlarmJob).where(AlarmJob.alarm_id == self.id)
        session.execute(stmt)

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def resend_to_ui(self, session: Session) -> None:
        """Re-send an alarm to the UI."""
        alarm_job = AlarmJob(job_name="ui_sending", job_status=AlarmJobStatus.TO_DO, alarm_id=self.id)
        alarm_job.insert_object(session)

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def reenrich(self, session: Session) -> None:
        """Re-enrich an alarm by updating an existing alarm_job."""
        enrichment_job = AlarmJob(alarm_id=self.id, job_name="enrichment", job_status=AlarmJobStatus.TO_DO.value)

        enrichment_job.insert_object(session)

    def _make_light_object(self) -> Self:
        """Make an object light-weight for DB operations.

        "Making light" means removing the relationships to avoid inserting them in the database.
        """
        # self.enrichment_job = None  # TODO: Why is this commented?
        self.last_enrichment = None
        self.last_alarm_incident = None
        self.last_clear = None
        self.last_problem = None

        return self

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_object(self, session: Session) -> Self | None:
        """Insert this object in the database."""
        self._make_light_object()
        return super().insert_object(session)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_objects(cls, session: Session, data: list[Self]):
        """Insert the provided objects in the database."""
        light_data = [alarm._make_light_object() for alarm in data]
        return super().insert_objects(session, light_data)

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def update_object(self, session: Session) -> Self | None:
        """Update this object in the database."""
        self._make_light_object()
        return super().update_object(session)

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def delete_object(self, session: Session) -> None:
        """Delete this object in the database."""
        self._make_light_object()
        return super().delete_object(session)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def cascade_jobs_for_many(cls, session: Session, ids: list[int]):
        """Cascade delete all jobs related to the provided alarm ids.

        Do note that the session is not committed after this operation, so the caller must do it.
        """
        stmt = delete(AlarmJob).where(AlarmJob.alarm_id.in_(ids))
        session.execute(stmt)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def delete_objects(cls, session: Session, ids: list[int]):
        """Delete this object in the database."""
        cls.cascade_jobs_for_many(session, ids)
        return super().delete_objects(session, ids)

    def key(self) -> str:
        """Create a unique key so alarms can be stored in a dict for faster lookup."""
        key = f"{self.agent_id}{self.ci_id}{self.metric_type}{self.metric_name}"
        if self.identifier:
            key = f"{key}{self.identifier}"
        return key

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def has_missing_enrichment(self) -> bool:
        """Check if the alarm has a missing enrichment.

        An alarm is considered to have a missing enrichment if:
            - The agent has enrichment enabled (ci_enrichment, metric_enrichment, or topology_enrichment is True)
            - The last enrichment is not set
        """
        agent = self.agent
        if not agent:  # This should not happen, but our model defines agent as optional, so this check is required.
            raise ValueError(f"Alarm with {self.id=} does not have an agent.")
        enrichment_flags = (agent.ci_enrichment, agent.metric_enrichment, agent.topology_enrichment)
        return any(enrichment_flags) and not self.last_enrichment


@dataclass
class AlarmJob(Base):
    """Represent an alarm job."""

    __tablename__ = "s2110_alarm_job"

    alarm_id: Mapped[int] = mapped_column(ForeignKey("s2110_alarm.id"))
    job_name: Mapped[str] = mapped_column()
    job_status: Mapped[Optional[int]] = mapped_column(default=0)
    job_error: Mapped[Optional[str]] = mapped_column()
    job_time: Mapped[Optional[datetime]] = mapped_column()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_alarm_job(cls, alarm_id: int, job_name: str, session: Session) -> Optional["AlarmJob"]:
        """Get the status of an alarm job."""
        statement = select(AlarmJob).where(
            and_(
                AlarmJob.alarm_id == alarm_id,
                AlarmJob.job_name == job_name,
            )
        )

        return session.execute(statement).scalar()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_alarm_jobs(cls, alarm_id: int, session: Session) -> Sequence["AlarmJob"]:
        """Get the jobs of an alarm."""
        statement = select(AlarmJob).where(AlarmJob.alarm_id == alarm_id)

        return session.execute(statement).scalars().all()

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def update(self, session: Session) -> Sequence[int]:
        """Update the affected alarm jobs and return their ids.

        We only update jobs with the following status:
            - TO_DO
        """
        update_statement = (
            update(AlarmJob)
            .where(AlarmJob.get_default_where_clause(self.job_name))
            .where(AlarmJob.alarm_id == self.alarm_id)
            .values(
                **{
                    "job_status": self.job_status,
                    "job_error": self.job_error,
                    "job_time": self.job_time,
                }
            )
            .returning(AlarmJob.id)
        )
        return session.execute(update_statement).scalars().all()

    @classmethod
    def get_default_where_clause(cls, job_name: str) -> ColumnElement[bool]:
        """Get the default where clause for the provided job name."""
        return and_(AlarmJob.job_name == job_name, AlarmJob.job_status == AlarmJobStatus.TO_DO.value)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def delete_many(cls, alarm_id: int, session: Session) -> None:
        """Delete all jobs of an alarm."""
        if not (alarm_jobs := cls.get_alarm_jobs(alarm_id=alarm_id, session=session)):
            return

        for alarm_job in alarm_jobs:
            alarm_job.delete_object(session)

        return None


@dataclass
class Enrichment(Base):
    """Represent the enrichment of an alarm."""

    __tablename__ = "s2110_enrichment"

    enrichment_time: Mapped[Optional[datetime]] = mapped_column()
    location_category: Mapped[Optional[str]] = mapped_column()
    brand: Mapped[Optional[str]] = mapped_column()
    ci_uuid: Mapped[Optional[str]] = mapped_column()
    ci_type: Mapped[Optional[str]] = mapped_column()
    critical_ci: Mapped[Optional[str]] = mapped_column()
    identification: Mapped[Optional[str]] = mapped_column()
    ip_address: Mapped[Optional[str]] = mapped_column()
    location_address: Mapped[Optional[str]] = mapped_column()
    location_attribute: Mapped[Optional[str]] = mapped_column()
    floc_id: Mapped[Optional[str]] = mapped_column()
    ci_enrichment: Mapped[bool] = mapped_column()
    top_level_impact: Mapped[bool] = mapped_column()
    instructions: Mapped[Optional[str]] = mapped_column()
    metric_enrichment: Mapped[bool] = mapped_column()
    top_level: Mapped[Optional[str]] = mapped_column()
    severity: Mapped[Optional[int]] = mapped_column()
    actionable: Mapped[Optional[bool]] = mapped_column()
    linked_environments: Mapped[Optional[str]] = mapped_column()
    floc_class: Mapped[Optional[str]] = mapped_column()
    subgroups: Mapped[Optional[str]] = mapped_column()
    model: Mapped[Optional[str]] = mapped_column()
    source: Mapped[Optional[str]] = mapped_column()
    metric_category: Mapped[Optional[str]] = mapped_column()
    action_class: Mapped[Optional[str]] = mapped_column()
    metric_description: Mapped[Optional[str]] = mapped_column()
    metric_short_name: Mapped[Optional[str]] = mapped_column()
    location_type: Mapped[Optional[str]] = mapped_column()
    dashboards: Mapped[Optional[str]] = mapped_column()
    metric_service: Mapped[Optional[str]] = mapped_column()
    metric_sub_service: Mapped[Optional[str]] = mapped_column()
    main_service: Mapped[Optional[str]] = mapped_column()
    main_sub_service: Mapped[Optional[str]] = mapped_column()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_one(cls, data: dict, session: Session) -> None:
        """Insert the provided data."""
        if not data:
            return

        alarm_id = data.get("alarm_id")
        if not isinstance(alarm_id, int):
            return

        enrichment = cls._item(data)
        if not (inserted_enrichment := enrichment.insert_object(session)):
            return

        enrichment = inserted_enrichment
        # Set the existing AlarmEnrichment to last_enrichment = False.
        statement = (
            update(AlarmEnrichment).where(AlarmEnrichment.alarm_id == alarm_id).values(**{"last_enrichment": False})
        )
        session.execute(statement)

        # Add link on the AlarmEnrichment table.
        alarm_enrichment = AlarmEnrichment(alarm_id=alarm_id, enrichment_id=enrichment.id, last_enrichment=True)
        alarm_enrichment.insert_object(session)

        return

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_many(cls, data: Sequence[dict], session: Session) -> None:
        """Insert each record in the provided data."""
        if not data:
            return None

        for data_record in data:
            Enrichment.insert_one(data=data_record, session=session)

        return None

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_enrichment_by_alarm_id(cls, alarm_id: int, session: Session) -> Optional["Self"]:
        """Retrieve the enrichment object based on the alarm id."""
        statement = (
            select(cls)
            .join(
                AlarmEnrichment,
                and_(cls.id == AlarmEnrichment.enrichment_id, AlarmEnrichment.last_enrichment == 1),
            )
            .join(Alarm, AlarmEnrichment.alarm_id == Alarm.id)
            .where(Alarm.is_active == 1)
            .where(Alarm.id == alarm_id)
        )

        result = session.execute(statement).scalar()

        return result


@dataclass
class AlarmEnrichment(Base):
    """Represent an alarm job."""

    __tablename__ = "s2110_alarm_enrichment"

    alarm_id: Mapped[int] = mapped_column(ForeignKey("s2110_alarm.id"))
    enrichment_id: Mapped[int] = mapped_column(ForeignKey("s2110_enrichment.id"))
    enrichment: Mapped["Enrichment"] = relationship(
        "Enrichment", foreign_keys=[enrichment_id], uselist=False, cascade="merge"
    )
    last_enrichment: Mapped[bool] = mapped_column()


@dataclass
class Incident(Base):
    """Represent an incident."""

    __tablename__ = "s2110_incident"

    sap_id: Mapped[str] = mapped_column()
    execution_id: Mapped[Optional[int]] = mapped_column()
    instruction_id: Mapped[Optional[int]] = mapped_column()
    sap_status: Mapped[Optional[str]] = mapped_column()
    air_status: Mapped[Optional[str]] = mapped_column()
    user_id: Mapped[Optional[str]] = mapped_column()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_one(cls, data: "dict | Self", session: Session) -> "Self | None":
        """Insert the provided data."""
        if not data:
            return None

        incident = cls._item(data)

        # Check if the incident already exists.
        if not incident.sap_id:
            # Do nothing if no incident number is provided.
            return None

        statement = select(cls).where(Incident.sap_id == incident.sap_id)
        if existing_incident := session.execute(statement).scalar():
            # Do nothing if it already exists.
            return existing_incident

        return incident.insert_object(session)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_by_sap_id(cls, sap_id: str, session: Session) -> Optional["Incident"]:
        """Select the incident corresponding to a SAP ID."""
        statement = select(Incident).where(Incident.sap_id == sap_id)
        return session.execute(statement).scalar()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def update_sap_status(cls, sap_id: str | None, sap_status: str | None, session: Session):
        """Update the sap_status."""
        statement = update(Incident).where(Incident.sap_id == sap_id).values(sap_status=sap_status)
        return session.execute(statement)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_all_unresolved_incidents(cls, session: Session) -> Sequence["Incident"]:
        """Get all unresolved incidents."""
        statement = select(Incident).where(Incident.sap_status != enums.IncidentStatus.RESOLVED.value)
        return session.execute(statement).scalars().all()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def is_incident_resolved(cls, sap_id: str, session: Session) -> bool:
        """Check if an incident is resolved."""
        statement = select(Incident).where(
            and_(Incident.sap_id == sap_id, Incident.sap_status == enums.IncidentStatus.RESOLVED.value)
        )
        return session.execute(statement).scalar() is not None

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def update_air_data(
        cls, id: int, execution_id: int | None, instruction_id: int | None, air_status: str | None, session: Session
    ) -> None:
        """Update the sap_status."""
        statement = (
            update(Incident)
            .where(Incident.id == id)
            .values(execution_id=execution_id, instruction_id=instruction_id, air_status=air_status)
        )
        session.execute(statement)

        return None


@dataclass
class AlarmIncident(Base):
    """Represent a link between an alarm and an incident."""

    __tablename__ = "s2110_alarm_incident"

    alarm_id: Mapped[int] = mapped_column(ForeignKey("s2110_alarm.id"))
    incident_id: Mapped[int] = mapped_column(ForeignKey("s2110_incident.id"))
    link_time: Mapped[Optional[datetime]] = mapped_column()
    is_origin_alarm: Mapped[bool] = mapped_column(default=True)
    is_link_active: Mapped[bool] = mapped_column(default=True)
    link_user_id: Mapped[Optional[str]] = mapped_column()
    unlink_user_id: Mapped[Optional[str]] = mapped_column()
    unlink_time: Mapped[Optional[datetime]] = mapped_column()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_one(cls, data: "dict | Self", session: Session) -> None:
        """Insert the provided data."""
        if not data:
            return

        alarm_incident = cls._item(data)

        # Does the link already exist and is active?
        select_statement = select(cls).where(
            and_(
                AlarmIncident.alarm_id == alarm_incident.alarm_id,
                AlarmIncident.incident_id == alarm_incident.incident_id,
                AlarmIncident.is_link_active == 1,
            )
        )

        if session.execute(select_statement).scalar():
            # Do nothing if link exists.
            return

        # Set the existing AlarmIncident to is_link_active = False.
        statement = (
            update(AlarmIncident)
            .where(and_(AlarmIncident.alarm_id == alarm_incident.alarm_id, AlarmIncident.is_link_active == 1))
            .values(
                **{
                    "is_link_active": False,
                    "unlink_user_id": alarm_incident.link_user_id,
                    "unlink_time": alarm_incident.link_time,
                }
            )
        )
        session.execute(statement)

        # Add link on the AlarmIncident table.
        new_alarm_incident = AlarmIncident(
            alarm_id=alarm_incident.alarm_id,
            incident_id=alarm_incident.incident_id,
            link_time=alarm_incident.link_time,
            is_origin_alarm=alarm_incident.is_origin_alarm,
            is_link_active=alarm_incident.is_link_active,
            link_user_id=alarm_incident.link_user_id,
        )
        new_alarm_incident.insert_object(session)

        return

    def disable(self, session: Session) -> None:
        """Disable the link between an Alarm and an Incident."""
        statement = (
            update(AlarmIncident)
            .where(AlarmIncident.alarm_id == self.alarm_id)
            .where(AlarmIncident.incident_id == self.incident_id)
            .values(**{"is_link_active": False, "unlink_user_id": self.unlink_user_id, "unlink_time": self.unlink_time})
        )
        session.execute(statement)

        return

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_alarms_linked_to_incident(cls, sap_id: str, session: Session) -> Optional[Sequence[Alarm]]:
        """Retrieve the active alarms linked to an incident."""
        statement = (
            select(Alarm)
            .join(AlarmIncident, and_(Alarm.id == AlarmIncident.alarm_id, AlarmIncident.is_link_active == 1))
            .join(Incident, AlarmIncident.incident_id == Incident.id)
            .where(Alarm.is_active == 1)
            .where(Incident.sap_id == sap_id)
        )

        return session.execute(statement).scalars().all()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def is_alarm_linked_to_incident(cls, alarm_id: int, session: Session) -> bool:
        """Check if an alarm is linked to an incident."""
        statement = select(AlarmIncident).where(
            and_(AlarmIncident.alarm_id == alarm_id, AlarmIncident.is_link_active == 1)
        )
        result = session.execute(statement).first()
        return result is not None


@dataclass
class Release(Base):
    """Represent a release."""

    __tablename__ = "s2110_release"
    sap_id: Mapped[str] = mapped_column()
    external_id: Mapped[Optional[str]] = mapped_column()
    comment: Mapped[Optional[str]] = mapped_column()
    start_time: Mapped[Optional[datetime]] = mapped_column()
    end_time: Mapped[Optional[datetime]] = mapped_column()
    sap_status: Mapped[Optional[str]] = mapped_column()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_one(cls, data: dict | Self, session: Session) -> Self | None:
        """Insert the provided data."""
        if not data:
            return None

        release = cls._item(data)

        # Check if the release already exists.
        if release.sap_id:
            statement = select(cls).where(Release.sap_id == release.sap_id)
        elif release.external_id:
            statement = select(cls).where(Release.external_id == release.external_id)
        else:
            # Do nothing if no release number nor external ID is provided.
            return None

        if existing_release := session.execute(statement).scalar():
            # Do nothing if it already exists.
            return existing_release

        return release.insert_object(session)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_by_release_id(cls, sap_id, session: Session) -> Self | None:
        """Select the release corresponding to a release ID."""
        statement = select(cls).where(Release.sap_id == sap_id)
        return session.execute(statement).scalar()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def update_sap_status(cls, sap_id: str | None, sap_status: str | None, session: Session):
        """Update release status."""
        statement = update(Release).where(Release.sap_id == sap_id).values(sap_status=sap_status)
        return session.execute(statement)

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_all_uncompleted_release(cls, session: Session) -> Sequence["Release"]:
        """Get all uncompleted releases (not cancelled and not completed)."""
        statement = select(Release).where(
            and_(
                Release.sap_status != enums.ReleaseStatus.COMPLETED.value,
                Release.sap_status != enums.ReleaseStatus.CANCELLED.value,
            )
        )
        return session.execute(statement).scalars().all()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def is_release_approved(cls, sap_id: str | None, session: Session) -> bool:
        """Check if a release is approved."""
        statement = select(Release).where(
            and_(Release.sap_id == sap_id, Release.sap_status == enums.ReleaseStatus.APPROVED.value)
        )
        return session.execute(statement).scalar() is not None


@dataclass
class AlarmRelease(Base):
    """Represent a link between an alarm and a release."""

    __tablename__ = "s2110_alarm_release"
    alarm_id: Mapped[int] = mapped_column(ForeignKey("s2110_alarm.id"), nullable=False)
    release_id: Mapped[int] = mapped_column(ForeignKey("s2110_release.id"), nullable=False)
    link_time: Mapped[Optional[datetime]] = mapped_column()
    is_link_active: Mapped[bool] = mapped_column(default=True)
    link_user_id: Mapped[Optional[str]] = mapped_column()
    unlink_user_id: Mapped[Optional[str]] = mapped_column()
    unlink_time: Mapped[Optional[datetime]] = mapped_column()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def insert_one(cls, data: dict | Self, session: Session) -> None:
        """Insert the provided data."""
        if not data:
            return

        alarm_release = cls._item(data)

        # Does the link already exist and is active?
        select_statement = select(cls).where(
            and_(
                cls.alarm_id == alarm_release.alarm_id,
                cls.release_id == alarm_release.release_id,
                cls.is_link_active == 1,
            )
        )

        if session.execute(select_statement).scalar():
            # Do nothing if link exists.
            return

        # Set the existing AlarmRelease to is_link_active = False.
        statement = (
            update(AlarmRelease)
            .where(AlarmRelease.alarm_id == alarm_release.alarm_id)
            .values(
                **{
                    "is_link_active": False,
                    "unlink_user_id": alarm_release.link_user_id,
                    "unlink_time": alarm_release.link_time,
                }
            )
        )
        session.execute(statement)

        # Add link on the AlarmRelease table.
        new_alarm_release = AlarmRelease(
            alarm_id=alarm_release.alarm_id,
            release_id=alarm_release.release_id,
            link_time=alarm_release.link_time,
            is_link_active=alarm_release.is_link_active,
            link_user_id=alarm_release.link_user_id,
        )
        new_alarm_release.insert_object(session)

        return

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_active_release(
        cls, alarm_id: Optional[int], sap_id: Optional[int], external_id: Optional[str], session: Session
    ) -> Optional["AlarmRelease"]:
        """Retrieve the active release linked to an alarm."""
        statement = (
            select(AlarmRelease)
            .join(Release, AlarmRelease.release_id == Release.id)
            .where(AlarmRelease.alarm_id == alarm_id)
            .where(or_(Release.sap_id == sap_id, Release.external_id == external_id))
            .where(AlarmRelease.is_link_active == 1)
        )
        return session.execute(statement).scalar()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def get_alarms_linked_to_release(cls, sap_id: Optional[str], session: Session) -> Optional[Sequence[Alarm]]:
        """Retrieve the active alarms linked to an incident."""
        statement = (
            select(Alarm)
            .join(AlarmRelease, and_(Alarm.id == AlarmRelease.alarm_id, AlarmRelease.is_link_active == 1))
            .join(Release, AlarmRelease.release_id == Release.id)
            .where(Alarm.is_active == 1)
            .where(Release.sap_id == sap_id)
        )

        return session.execute(statement).scalars().all()

    @classmethod
    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def is_alarm_linked_to_release(cls, alarm_id: int, session: Session) -> bool:
        """Check if an alarm is linked to a release."""
        statement = select(AlarmRelease).where(
            and_(AlarmRelease.alarm_id == alarm_id, AlarmRelease.is_link_active == 1)
        )
        result = session.execute(statement).first()
        return result is not None

    def disable(self, session: Session) -> None:
        """Disable the link between an Alarm and an Incident."""
        statement = (
            update(AlarmRelease)
            .where(AlarmRelease.alarm_id == self.alarm_id)
            .where(AlarmRelease.release_id == self.release_id)
            .values(**{"is_link_active": False, "unlink_user_id": self.unlink_user_id, "unlink_time": self.unlink_time})
        )
        session.execute(statement)

        return


@dataclass
class ReleaseCI(Base):
    """Represent a link between a release and a CI."""

    __tablename__ = "s2110_release_ci"
    release_id: Mapped[int] = mapped_column(ForeignKey("s2110_release.id"), nullable=False)
    ci_id: Mapped[Optional[str]] = mapped_column()
    floc_id: Mapped[Optional[str]] = mapped_column()


@dataclass
class UserAction(Base):
    """Represent a User Action coming from our frontend."""

    __tablename__ = "s2110_user_action"
    action_type: Mapped[Optional[str]] = mapped_column()
    creation_time: Mapped[Optional[datetime]] = mapped_column(default=now_naive)
    comment: Mapped[Optional[str]] = mapped_column()
    user_id: Mapped[Optional[str]] = mapped_column()


@dataclass
class AlarmAction(Base):
    """Represent a link between Alarms and UserActions."""

    __tablename__ = "s2110_alarm_action"
    alarm_id: Mapped[int] = mapped_column(ForeignKey("s2110_alarm.id"), nullable=False)
    action_id: Mapped[int] = mapped_column(ForeignKey("s2110_user_action.id"), nullable=False)
    is_first_ack: Mapped[Optional[bool]] = mapped_column(default=False)


class SessionManager:
    """Manage database sessions and engine creation.

    This class will only create an engine once and use sessionmaker to provide sessions fast.
    """

    def __init__(self, db_url, session_kwargs: dict | None = None, **engine_kwargs):
        self._engine = None
        self._sessionmaker = None
        self.db_url = db_url
        self.engine_kwargs = engine_kwargs
        self.session_kwargs = session_kwargs or {}

    @CaptureSpan(span_type=enums.MeasureType.DB_QUERY)
    def get_engine(self):
        """Create and return a SQLAlchemy engine."""
        if self._engine is None:
            self._engine = create_engine(self.db_url, **self.engine_kwargs)
            self._sessionmaker = sessionmaker(bind=self._engine, **self.session_kwargs)
        return self._engine

    @CaptureSpan(span_type=enums.MeasureType.DB_QUERY)
    def get_session(self) -> Session:
        """Create and return a new SQLAlchemy session that automatically closes when used as context-manager."""
        if self._sessionmaker is None:
            self.get_engine()  # ensures sessionmaker is created, we can safely type ignore the following line
        return self._sessionmaker()  # type:ignore[misc]

    @CaptureSpan(span_type=enums.MeasureType.DB_QUERY)
    def begin(self):
        """Begin a new session that automatically commits/rollbacks and closes when used a context-manager."""
        if self._sessionmaker is None:
            self.get_engine()  # ensures sessionmaker is created
        return self._sessionmaker.begin()


@CaptureSpan(span_type=enums.MeasureType.DB_QUERY)
def create_session_manager(application_name: str, echo: bool = False) -> SessionManager:
    """Create a database session manager."""
    dbconfig = DatabaseConfig()
    connect_args = {
        "options": f"-c DateStyle=ISO -csearch_path={dbconfig.schema}",
        "application_name": application_name,
    }
    return SessionManager(db_url=dbconfig.to_conninfo(), connect_args=connect_args, echo=echo)


@CaptureSpan(span_type=MeasureType.DB_QUERY.value)
def get_alarm_field(alarm: Alarm | dict, field: str, enrichment_dict: dict | None = None) -> Any:
    """Get any field for an Alarm.

    The field is taken in priority from its last enrichment,
    then from its last problem,
    then from its last occurrence,
    then from the alarm itself.
    """
    if isinstance(alarm, Alarm):
        if enrichment_dict:
            value = enrichment_dict.get(field, None)
            if value is not None:
                return value

        if alarm.last_enrichment:
            value = getattr(alarm.last_enrichment, field, None)
            if value is not None:
                return value

        if alarm.last_problem:
            value = getattr(alarm.last_problem, field, None)
            if value is not None:
                return value

        if alarm.last_occurrence:
            value = getattr(alarm.last_occurrence, field, None)
            if value is not None:
                return value

        return getattr(alarm, field, None)

    elif isinstance(alarm, dict):
        if enrichment_dict:
            value = enrichment_dict.get(field, None)
            if value is not None:
                return value

        if alarm["last_enrichment"]:
            value = alarm["last_enrichment"].get(field, None)
            if value is not None:
                return value

        if alarm["last_problem"]:
            value = alarm["last_problem"].get(field, None)
            if value is not None:
                return value

        if alarm["last_occurrence"]:
            value = alarm["last_occurrence"].get(field, None)
            if value is not None:
                return value

        return alarm.get(field, None)


class _Unset:
    """Sentinel value for unset fields."""


TUnset = type[_Unset]

TObject = Alarm | Occurrence | Enrichment | AlarmJob | AlarmEnrichment
TObjectType = type[TObject]

TBaseType = type[Base]
