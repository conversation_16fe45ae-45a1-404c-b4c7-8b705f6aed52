{"extra_env": {"OLYMPUS_AGENT_ID": "32"}, "agent_ids": [32], "mock": {"mon_datalines.utils.socket.gethostbyaddr": {"side_effect": "gethostbyaddr_side_effect"}}, "data": [{"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.39.640.***********.11", "host": "************", "SNMPv2-MIB::sysUpTime.0": "338 days, 18:33:31.04", "type": "snmp_trap", "@timestamp": "2025-02-13T20:23:28.651764950Z", "message": "#<SNMP::SNMPv2_Trap:0x1786afd0 @varbind_list=[#<SNMP::VarBind:0x14879d45 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x625187cc @value=2927001104>>, #<SNMP::VarBind:0x400b5c79 @name=[*******.*******.4.1.0], @value=[*******.4.1.776.39.640.***********.11]>, #<SNMP::VarBind:0x669e0ad5 @name=[*******.*******.1.1.0], @value=#<SNMP::Integer:0x4edbcef6 @value=0>>, #<SNMP::VarBind:0x205a5f06 @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x3a2827e0 @value=0>>, #<SNMP::VarBind:0x2cf65ad2 @name=[*******.4.1.776.11.10.0], @value=\"aster5.alarmInfo.currentAlarms.alarmSyncLoss on\">, #<SNMP::VarBind:0x3c4a9dc4 @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x4e1a4724 @value=4>>, #<SNMP::VarBind:0x35f67e20 @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x2bc88286 @value=4>>], @request_id=1362100542, @error_index=0, @error_status=0, @source_ip=\"************\">", "@version": "1", "snmptrap.tlsTrapSeverityLevel": "4", "snmptrap.ifIndex0": "0", "snmptrap.tlsTrapDescription": "aster5.alarmInfo.currentAlarms.alarmSyncLoss on", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.tlsSeverityLevel": "4", "event.kafka.timestamp": "2025-02-13T20:23:28.755Z", "event.kafka.offset": 78984, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "ffa4dc71-6bf6-4d9b-b583-bcc9db40f141", "event.logstash.instance_name": "iictmiapls016", "snmptrap.ifIndex1": null}, "output": {"s2110_alarm": [{"ci_id": "sta_fgzh_fgsp_a", "metric_name": "alarmSyncLoss", "metric_type": "", "event_id": "", "actionable": true}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "sta_fgzh_fgsp_a", "metric_name": "alarmSyncLoss", "metric_type": "", "summary": "aster5.alarmInfo.currentAlarms.alarmSyncLoss on", "clear_time": null, "severity": 5}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.39.640.***********.10", "host": "************", "snmptrap": {"tlsTrapSeverityLevel": "4", "ifIndex0": "0", "tlsTrapDescription": "aster5.alarmInfo.currentAlarms.notResponding on", "tlsCntTreeGlobalIndex": "0", "tlsSeverityLevel": "4"}, "SNMPv2-MIB::sysUpTime.0": "338 days, 18:22:51.04", "type": "snmp_trap", "@timestamp": "2025-02-13T20:12:49.395994037Z", "event": {"kafka": {"timestamp": "2025-02-13T20:12:49.497Z", "offset": 78960, "key": null, "partition": 0, "consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "topic": "a1298-modem_management-telindus_datalines_events-prd"}, "uuid": "932df1f8-85de-473e-92a6-ad667109b822", "logstash": {"instance_name": "iictmiapls016"}}, "message": "#<SNMP::SNMPv2_Trap:0x59e4d51d @varbind_list=[#<SNMP::VarBind:0x5edfeb21 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x1eb5132 @value=2926937104>>, #<SNMP::VarBind:0x7e58302f @name=[*******.*******.4.1.0], @value=[*******.4.1.776.39.640.***********.10]>, #<SNMP::VarBind:0xb025f11 @name=[*******.*******.1.1.0], @value=#<SNMP::Integer:0x2aa52bb3 @value=0>>, #<SNMP::VarBind:0x711c9d14 @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x4e7a0250 @value=0>>, #<SNMP::VarBind:0x57193659 @name=[*******.4.1.776.11.10.0], @value=\"aster5.alarmInfo.currentAlarms.notResponding on\">, #<SNMP::VarBind:0x546277ba @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x4103a41f @value=4>>, #<SNMP::VarBind:0x5f7546ed @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x414018a4 @value=4>>], @request_id=1362100518, @error_index=0, @error_status=0, @source_ip=\"************\">", "@version": "1"}, "output": {"s2110_alarm": [{"ci_id": "sta_fgzh_fgsp_a", "metric_name": "notResponding", "metric_type": "", "event_id": ""}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "sta_fgzh_fgsp_a", "metric_name": "notResponding", "metric_type": "", "summary": "aster5.alarmInfo.currentAlarms.notResponding on", "clear_time": null, "severity": 5}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.39.640.***********.10", "host": "************", "SNMPv2-MIB::sysUpTime.0": "338 days, 18:33:31.04", "type": "snmp_trap", "@timestamp": "2025-02-13T20:23:28.652505410Z", "message": "#<SNMP::SNMPv2_Trap:0x5f3bc2b5 @varbind_list=[#<SNMP::VarBind:0x3609d51f @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x519e9de6 @value=2927001104>>, #<SNMP::VarBind:0x50b1d30a @name=[*******.*******.4.1.0], @value=[*******.4.1.776.39.640.***********.10]>, #<SNMP::VarBind:0x5c8723d0 @name=[*******.*******.1.1.0], @value=#<SNMP::Integer:0x245ea472 @value=0>>, #<SNMP::VarBind:0x18a7cb2 @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x5f7dc60a @value=0>>, #<SNMP::VarBind:0x3a81f796 @name=[*******.4.1.776.11.10.0], @value=\"aster5.alarmInfo.currentAlarms.notResponding off\">, #<SNMP::VarBind:0x6b2db980 @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x399027bb @value=0>>, #<SNMP::VarBind:0x86cfb15 @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x3d6490c9 @value=0>>], @request_id=1362100543, @error_index=0, @error_status=0, @source_ip=\"************\">", "@version": "1", "snmptrap.tlsTrapSeverityLevel": "0", "snmptrap.ifIndex0": "0", "snmptrap.tlsTrapDescription": "aster5.alarmInfo.currentAlarms.notResponding off", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.tlsSeverityLevel": "0", "event.kafka.timestamp": "2025-02-13T20:23:28.755Z", "event.kafka.offset": 78985, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "852ec937-4b9d-444d-bc3e-a5d06bc13714", "event.logstash.instance_name": "iictmiapls016", "snmptrap.ifIndex1": null}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "sta_fgzh_fgsp_a", "metric_name": "notResponding", "metric_type": "", "summary": "aster5.alarmInfo.currentAlarms.notResponding off", "clear_time": "2025-02-13T20:23:28", "severity": 1}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.50.656.***********.11", "host": "************", "SNMPv2-MIB::sysUpTime.0": "359 days, 17:07:19.00", "type": "snmp_trap", "@timestamp": "2025-02-13T20:26:29.650570938Z", "message": "#<SNMP::SNMPv2_Trap:0x578b9f3b @varbind_list=[#<SNMP::VarBind:0x6c64b1c8 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x6a92a746 @value=3107923900>>, #<SNMP::VarBind:0x61eb8f68 @name=[*******.*******.4.1.0], @value=[*******.4.1.776.50.656.***********.11]>, #<SNMP::VarBind:0x5854ab9e @name=[*******.*******.1.1.1], @value=#<SNMP::Integer:0x1177abad @value=1>>, #<SNMP::VarBind:0x6ea09652 @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x538dc421 @value=0>>, #<SNMP::VarBind:0x2ef88d95 @name=[*******.4.1.776.11.10.0], @value=\"aster5/line.alarmInfo.currentAlarms.retrain on\">, #<SNMP::VarBind:0x27e7124 @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x6806653f @value=2>>, #<SNMP::VarBind:0x551708d2 @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x3fe9a3c0 @value=2>>], @request_id=1362100552, @error_index=0, @error_status=0, @source_ip=\"************\">", "@version": "1", "snmptrap.tlsTrapSeverityLevel": "2", "snmptrap.ifIndex0": null, "snmptrap.tlsTrapDescription": "aster5/line.alarmInfo.currentAlarms.retrain on", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.tlsSeverityLevel": "2", "event.kafka.timestamp": "2025-02-13T20:26:29.751Z", "event.kafka.offset": 78993, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "f8d63e8e-5aba-4d7e-bcb4-53481f48367f", "event.logstash.instance_name": "iictmiapls016", "snmptrap.ifIndex1": "1"}, "output": {"s2110_alarm": [{"ci_id": "sta_fgzh_fgsp_c", "metric_name": "retrain", "metric_type": "", "event_id": "", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "sta_fgzh_fgsp_c", "metric_name": "retrain", "metric_type": "", "summary": "aster5/line.alarmInfo.currentAlarms.retrain on", "clear_time": null, "severity": 3}]}}, {"input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.50.656.***********.14", "host": "************", "SNMPv2-MIB::sysUpTime.0": "359 days, 18:39:17.00", "type": "snmp_trap", "@timestamp": "2025-02-13T21:58:25.436155474Z", "message": "#<SNMP::SNMPv2_Trap:0x3b05b728 @varbind_list=[#<SNMP::VarBind:0x5868ac3b @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x1a1af8a1 @value=3108475700>>, #<SNMP::VarBind:0xcb0b03d @name=[*******.*******.4.1.0], @value=[*******.4.1.776.50.656.***********.14]>, #<SNMP::VarBind:0x6605114e @name=[*******.*******.1.1.1], @value=#<SNMP::Integer:0x7b9160c1 @value=1>>, #<SNMP::VarBind:0x368350e2 @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x3f3b3f57 @value=0>>, #<SNMP::VarBind:0x71fac69f @name=[*******.4.1.776.11.10.0], @value=\"aster5/line.alarmInfo.currentAlarms.remotePower on\">, #<SNMP::VarBind:0x11d2709 @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x2d357388 @value=4>>, #<SNMP::VarBind:0x4b9220fe @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x6c7bc92d @value=3>>], @request_id=1362100934, @error_index=0, @error_status=0, @source_ip=\"************\">", "@version": "1", "snmptrap.tlsTrapSeverityLevel": "3", "snmptrap.ifIndex0": null, "snmptrap.tlsTrapDescription": "aster5/line.alarmInfo.currentAlarms.remotePower on", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.tlsSeverityLevel": "4", "event.kafka.timestamp": "2025-02-13T21:58:25.536Z", "event.kafka.offset": 79351, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "52b1a73c-addb-4622-97db-633624ab089b", "event.logstash.instance_name": "iictmiapls016", "snmptrap.ifIndex1": "1"}, "output": {"s2110_alarm": [{"ci_id": "sta_fgzh_fgsp_c", "metric_name": "remotePower", "metric_type": "", "event_id": "", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "sta_fgzh_fgsp_c", "metric_name": "remotePower", "metric_type": "", "summary": "aster5/line.alarmInfo.currentAlarms.remotePower on", "clear_time": null, "severity": 4, "additional_data": {"responsible_department": "Szone"}}]}}]}