"""OID mapping for Scada."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Scada."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "prio": "priority",
            "tunnel": "tunnel_abbreviation",
            "alarm_id": "alarm_id",
            "status": "status",
            "alarmDescFR": "alarm_description_fr",
            "assetID": "asset_id",
            "alarmDescEN": "alarm_description_en",
            "riskEN": "risk_en",
            "procedure": "procedure",
            "timestamp": "timestamp",
        }
