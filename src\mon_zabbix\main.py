"""Entrypoint for the application."""

from mon_zabbix import dd
from mon_zabbix.config import config
from mon_zabbix.oid_mapper import OIDMapper
from olympus_common import defaults

application = defaults.databasewriter_kafkareader_app(config, suffix_service_name=config.zabbix_cc)


@application.run_forever(config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Run the dd.

    This function is the starting point of the dd and should be used to run the dd.
    """
    return defaults.transform(data, config, dd.run, sort_keys=["event_id"], sort_ascending=True, oid_mapper=OIDMapper)
