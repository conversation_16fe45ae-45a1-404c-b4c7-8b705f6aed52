"""Microsoft Graph API for mail2incidents."""

import logging
import os
import re
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone

import requests
from fastapi import HTTPException
from reportlab.lib.pagesizes import A4  # type: ignore
from reportlab.platypus import Paragraph, SimpleDocTemplate  # type: ignore

from mail2incidents.config import config  # type: ignore
from mail2incidents.sap import sap_incident  # type: ignore
from mail2incidents.statics import FORM_URLENCODED_HEADER, data_filter, data_needed  # type: ignore


@dataclass
class MSGraphAPI:
    """Class to get data from Microsoft Graph API."""

    session: requests.Session = requests.Session()
    session.headers.update({"Content-Type": FORM_URLENCODED_HEADER})
    token_expiration: datetime = datetime.min

    def get_token(self) -> str:
        """Get a new access token from Microsoft Graph API."""
        url = f"{config.graph_config.authority}/{config.graph_config.tenant_id}/oauth2/v2.0/token"
        body = {
            "client_id": config.graph_config.client_id,
            "client_secret": config.graph_config.client_secret,
            "scope": config.graph_config.scope,
            "grant_type": "client_credentials",
        }
        response = self.session.post(url=url, data=body, timeout=30)
        response.raise_for_status()
        result = response.json()
        self.token_expiration = datetime.now() + timedelta(minutes=50)
        self.session.headers["Authorization"] = f"Bearer {result.get('access_token')}"
        return result.get("access_token")

    def _login(self) -> None:
        """Add token in the header if it is not yet present or expired."""
        if self.token_expiration <= datetime.now():
            self.get_token()

    def _request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make a request to Microsoft Graph API, login if needed."""
        self._login()
        response = self.session.request(method, url, **kwargs)
        response.raise_for_status()
        return response

    def process(self) -> None:
        """Use mail2incidents."""
        emails_json = self.get_emails()
        for message in emails_json["value"]:
            try:
                self.process_email(message)
            except Exception as err:
                logging.exception(f"Error during email processing : {err}")
                self.change_folder(message["id"])
                raise HTTPException(status_code=500) from err

    def get_emails(self) -> dict:
        """Get unread emails from inbox."""
        url = (
            f"{config.graph_config.endpoint_graph}{config.graph_config.email}/mailFolders/"
            f"{config.graph_config.inbox_id}/messages?{data_filter}&{data_needed}"
        )
        response = self._request("get", url, headers={"Prefer": "outlook.body-content-type='text'"})
        return response.json()

    def process_email(self, message: dict) -> None:
        """Process a single email message."""
        email_subject_lim = message["subject"][:40]  # Max 40 characters for the short description
        email_subject = message["subject"]
        email_body = message["body"]["content"].replace("\r", "")
        email_id = message["id"]
        email_has_attachments = message["hasAttachments"]

        self.handle_unread_email(email_subject, email_subject_lim, email_body, message, email_id, email_has_attachments)

    def handle_unread_email(
        self,
        email_subject: str,
        email_subject_lim: str,
        email_body: str,
        message: dict,
        email_id: str,
        email_has_attachments: bool,
    ) -> None:
        """Handle an unread email message."""
        if re.match(r"^(INC|inc)[0-9]{7}\b", email_subject):
            inc_num = email_subject[:10]
            entities = sap_incident.get_entities_3m()
            qmnum = sap_incident.check_inc_in_entities_and_get_qmnum(entities, inc_num)
            if not qmnum:
                qmnum = self.handle_new_entity(email_subject_lim, email_body)
            else:
                self.handle_existing_entity(qmnum)
            self.set_email_as_processed(email_id, email_subject, email_has_attachments, qmnum, message)
            return
        self.change_folder(email_id)

    def handle_new_entity(self, email_subject_lim, email_body) -> str:
        """Handle new entity."""
        new_qmnum = sap_incident.create_entity_and_get_qmnum(email_body, email_subject_lim)
        return new_qmnum

    def handle_existing_entity(self, qmnum) -> None:
        """Handle existing entity."""
        sap_incident.update_incident_status(qmnum)
        sap_incident.release_main_order_incident(qmnum)
        sap_incident.add_feedback(qmnum)

    def set_email_as_processed(
        self,
        email_id,
        email_subject,
        email_has_attachments,
        qmnum,
        message,
    ) -> None:
        """Process retrieved email."""
        name_pdf = self.convert_email_to_pdf(message)
        sap_incident.add_email_to_entity(qmnum, name_pdf)
        if email_has_attachments:
            attachments = self.get_attachment(email_id)
            for attachment in attachments:
                sap_incident.add_attachment_to_entity(qmnum, attachment)
        self.change_subject_email(email_id, email_subject, qmnum)
        self.mark_email_as_read(email_id)

    def mark_email_as_read(self, id_email) -> requests.Response:
        """Mark email as read."""
        url = f"{config.graph_config.endpoint_graph}{config.graph_config.email}/messages/{id_email}"
        return self._request("patch", url, headers={"Content-Type": "application/json"}, json={"isRead": True})

    def change_subject_email(self, id_email, subject_email, notif_number) -> requests.Response:
        """Add notification number to subject email."""
        url = f"{config.graph_config.endpoint_graph}{config.graph_config.email}/messages/{id_email}"
        return self._request(
            "patch",
            url,
            headers={"Content-Type": "application/json"},
            json={"subject": f"{notif_number} - {subject_email}"},
        )

    def change_folder(self, id_email) -> requests.Response:
        """Change email to 'To process' folder, if not processed by code."""
        url = f"{config.graph_config.endpoint_graph}{config.graph_config.email}/messages/{id_email}/move"
        return self._request(
            "post",
            url,
            headers={"Content-Type": "application/json"},
            json={"destinationId": f"{config.graph_config.destination_folder_id}"},
        )

    def convert_email_to_pdf(self, email_data) -> str:
        """Use to create PDF from e-mail."""
        timestamp = datetime.strptime(email_data["sentDateTime"], r"%Y-%m-%dT%H:%M:%SZ")
        timestamp = timestamp.replace(tzinfo=timezone.utc).astimezone()
        formatted_timestamp = timestamp.strftime("%d-%m-%Y %H:%M:%S")
        pdf_email = []
        # Ajouter des flowables
        pdf_email.append(Paragraph(f"De : {email_data['sender']['emailAddress']['address']}"))
        pdf_email.append(Paragraph(f"A : {email_data['toRecipients'][0]['emailAddress']['address']}"))
        pdf_email.append(Paragraph(f"Date : {formatted_timestamp}"))
        pdf_email.append(Paragraph(f"Sujet : {email_data['subject']}"))
        pdf_email.append(Paragraph(" "))
        content = email_data["body"]["content"]
        content = content.replace("<br>", "<br />")
        content = content.replace("</br>", "")
        pdf_email.append(Paragraph(content))

        file_name = f"email_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.pdf"
        project_root = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(project_root, file_name)

        doc_pdf = SimpleDocTemplate(file_path, pagesize=A4)
        doc_pdf.build(pdf_email)

        return file_name

    def get_attachment(self, message_id) -> list:
        """Get the attachments."""
        project_root = os.path.dirname(os.path.abspath(__file__))
        url = f"{config.graph_config.endpoint_graph}{config.graph_config.email}/messages/{message_id}/attachments"
        response = self._request("get", url)
        attachments = response.json().get("value", [])

        for attachment in attachments:
            if "@odata.mediaContentType" in attachment:
                attachment_id = attachment["id"]
                file_name = attachment["name"]
                file_path = os.path.join(project_root, file_name)
                url = (
                    f"{config.graph_config.endpoint_graph}{config.graph_config.email}/messages/{message_id}/"
                    f"attachments/{attachment_id}/$value"
                )
                response = self._request("get", url)

                if response.status_code == 200:
                    with open(file_path, "wb") as file:
                        file.write(response.content)
                    print(f"Downloaded : {file_name}")
                else:
                    print(f"Error during download : {response.status_code}")
        return [attachment.get("name") for attachment in attachments if "name" in attachment]


graph_api = MSGraphAPI()
