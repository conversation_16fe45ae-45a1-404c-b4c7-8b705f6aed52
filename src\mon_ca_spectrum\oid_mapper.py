"""OID mapping for Spectrum."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Spectrum."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "alarm.status": "status",
            "alarm.model_type": "mtype",
            # "time": "time",
            "alarm.model_name": "mname",
            "alarm.state": "alarm_state",
            # "date": "date",
            "message": "event_message",
            "alarm.clearable": "clearable",
            "device.type": "dtype",
            "alarm.landscape": "landscape",
            "alarm.security_string": "secstr",
            "alarm.cause_code": "raw_cause_code",  # Now has 0x prepended.
            "alarm.model_type_handle": "mthandle",
            "alarm.severity": "raw_severity",
            "alarm.ackd": "ackd",
            "alarm.type": "raw_event_type",  # Now a string
            "alarm.repair_person": "repair_person",
            "source.ip": "ipaddress",
            "alarm.model_handle": "mhandle",
            "alarm.id": "raw_event_id",
            "@timestamp": "@timestamp",  # Use as date/time
        }
