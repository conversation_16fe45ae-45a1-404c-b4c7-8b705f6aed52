"""Details Design module for mon-scom."""

import datetime

import pandas as pd

from mon_scom import patterns, static, utils
from mon_scom.config import config
from olympus_common import enums
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Run the SCOM Details Design."""
    if config.debug:
        pd.set_option("display.max_columns", None)

    transformed = _transform(df, agent_id)

    # Drop rows with all NA's
    transformed = _drop_empty_rows(transformed)

    return transformed


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    # DisplayString is NaN sometimes so we first fillna
    df = df.fillna("N/A")

    # Hardcoded values
    df["agent_id"] = agent_id
    df["manager"] = _manager()
    df["action_class"] = _scope()
    df["additional_data"] = _additional_data()
    df["handle_time"] = _handle_time()

    # Computed values
    df[
        [
            "wake_up_time",
            "event_id",
            "event_type",
            "actionable",
            "raise_time",
            "clear_time",
            "clear_type",
            "metric_type",
            "metric_class",
            "metric_name",
            "alert_key",
            "alert_group",
            "node",
            "node_alias",
            "ci_id",
            "summary",
            "severity",
        ]
    ] = df.apply(_parse_scom, axis=1)

    return df


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_data() -> str | None:
    """Return the extended attributes for the event."""
    return None


# This table maps the severity of non-error alerts in SCOM to a severity
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _alert_to_severity(alert: str) -> int:
    match alert:
        case "Information":
            return enums.Severity.INDETERMINATE.value
        case "Warning":
            return enums.Severity.WARNING.value
        case _:
            return enums.Severity.INDETERMINATE.value


# This maps the severity of those alerts on par with its monitor health to a severity
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _monitor_health_to_severity(monitor_health: str) -> int:
    match monitor_health:
        case "Success":
            return enums.Severity.CLEARED.value
        case "Uninitialized":
            return enums.Severity.INDETERMINATE.value
        case "Warning":
            return enums.Severity.WARNING.value
        case _:
            return enums.Severity.INDETERMINATE.value


# This maps the severity of error kind of alerts with specified priority in SCOM to a severity
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _error_to_severity(error: str) -> int:
    match error:
        case "High":
            return enums.Severity.CRITICAL.value
        case "Normal":
            return enums.Severity.CRITICAL.value
        case "Low":
            return enums.Severity.CRITICAL.value
        case _:
            return enums.Severity.INDETERMINATE.value


# ignored: summary (semi), severity (semi), actionable !!!
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _parse_scom(row: pd.Series) -> pd.Series:
    raise_time_utc_str = row["TimeRaised"]
    raise_time_utc = (
        datetime.datetime.strptime(raise_time_utc_str, "%d/%m/%Y %H:%M:%S").replace(tzinfo=None)
        if raise_time_utc_str != ""
        else None
    )

    clear_time_utc_str = row["TimeResolved"]
    clear_time_utc = (
        datetime.datetime.strptime(clear_time_utc_str, "%d/%m/%Y %H:%M:%S").replace(tzinfo=None)
        if clear_time_utc_str != ""
        else None
    )
    raise_time = raise_time_utc
    wake_up_time = raise_time
    clear_time = clear_time_utc
    resolution_state = str(row["ResolutionState"])
    name = row["Name"]
    severity_str = row["Severity"]
    severity = enums.Severity.INDETERMINATE.value
    priority = row["Priority"]
    id = row["Id"]
    netbios_computername = row["NetbiosComputerName"]
    description = row["Description"]
    display_string = row["DisplayString"]
    monitoring_object_display_name: str = row["MonitoringObjectDisplayName"]
    monitoring_object_health_state = row["MonitoringObjectHealthState"]
    context_name = utils.get_context_name(row["Context"])
    managementpack_category_type = row["Category"]
    context_binary_packname = utils.get_context_part(row["Context"], "BinaryPathName")
    context_logging_computer = utils.get_context_part(row["Context"], "LoggingComputer")
    context_event_description = utils.get_context_part(row["Context"], "EventDescription")
    context_data = utils.get_context_part(row["Context"], "Data")
    context_param5 = utils.get_context_param_x(row["Context"], 5)
    iis_pool_site = False
    summary = ""
    cust_summary = ""
    men = ""
    node = ""
    node_alias = ""
    element_name = ""
    platform = ""
    cust_platform = ""
    metric_type = ""
    metric_name = ""
    cust_metric_name = ""
    ci_id = ""
    cust_ci_id = ""
    alert_key = ""
    alert_key_upper = ""
    alert_group = ""
    cust_alert_group = ""
    alert_group_upper = ""
    process = ""
    service = ""
    service_name = ""
    event_type = enums.AlarmType.PROBLEM.value
    clear_type = enums.ClearType.AUTOMATICALLY.value
    actionable: bool = True

    metric_class = "200200"

    # Map SCOM alert severity to severity
    if severity_str == "MatchMonitorHealth":
        # Severity is based on monitor health
        if monitoring_object_health_state == "Error":
            # Severity is based on alert priority
            severity = _error_to_severity(priority)
        else:
            severity = _monitor_health_to_severity(monitoring_object_health_state)

    elif severity_str == "Error":
        # Severity is based on alert priority
        severity = _error_to_severity(priority)
    else:
        severity = _alert_to_severity(severity_str)

    # Determine the type of this event
    if resolution_state == "255":
        event_type = enums.AlarmType.RESOLUTION.value
    elif resolution_state == "0":
        event_type = enums.AlarmType.PROBLEM.value
    else:
        event_type = enums.AlarmType.PROBLEM.value

    # Construct summary of event
    if description == "null":
        summary = f"{name} - {context_event_description}"
    elif description == "":
        summary = f"{name} - {context_event_description}"
    else:
        summary = f"{name} - {description}"

    # Problem and Resolution
    if resolution_state == "0":
        # Problem
        event_type = enums.AlarmType.PROBLEM.value
        raise_time = raise_time_utc

    elif resolution_state == "255":
        # Resolution
        event_type = enums.AlarmType.RESOLUTION.value
        severity = enums.Severity.INDETERMINATE.value
        raise_time = raise_time_utc
        clear_time = clear_time_utc

    else:
        # shouldn't exist
        event_type = enums.AlarmType.PROBLEM.value

    node, node_alias, ci_id = patterns.extract_netbios_node(netbios_computername)

    event_id = id

    if patterns.IIS_DETECTION_PATTERN.search(display_string):
        match = patterns.IIS_ALERT_KEY_EXTRACTION_PATTERN.search(display_string)
        alert_key = match.group(1) if match else display_string

        if context_name == "":
            element_name = monitoring_object_display_name
        else:
            element_name = context_name

        platform = f"IIS (Windows) - {element_name}"

    elif patterns.CLUSTER_ROLE_MOVE_PATTERN.search(description):
        alert_key = "Cluster Switch"
    elif patterns.SKYPE_DETECTION_PATTERN.search(display_string):
        alert_key = "Skype"
    else:
        alert_key = display_string

    # remove trailing spaces, tabs or dots
    # to interpret \t as a tab we have to enclose the string in single quotes and not double quotes
    # \s isn't supported to reference whitespaces

    alert_key = alert_key.strip(" \t.")
    alert_key_upper = alert_key.upper()

    alert_group = managementpack_category_type

    if alert_key_upper == "":
        metric_name = "NA"
        metric_type = "NA"
    else:
        key = alert_key_upper if alert_key_upper in static.METRIC_LOOKUP_DICT else "Default"
        metric_lookup = static.METRIC_LOOKUP_DICT[key]

        men = metric_lookup["metric_name"]
        metric_type = metric_lookup["metric_type"]

    if metric_name == "":
        metric_name = men

    # Use the values provided by
    # Custom::__AlertGroup__=<>::__CI_ID__=<>::__MonitoredElementName__=<>
    # ::__Platform__=<>::__Summary__=<> #######

    cust_alert_group = utils.get_description_part(description, "AlertGroup")

    if cust_alert_group != "":
        alert_group = cust_alert_group
        alert_group_upper = alert_group.upper()

        key = alert_group_upper if alert_group_upper in static.CUSTOM_METRIC_LOOKUP_DICT else "Default"
        custom_metric_lookup = static.CUSTOM_METRIC_LOOKUP_DICT[key]

        metric_type = custom_metric_lookup["metric_type"]
        metric_class = custom_metric_lookup["class"]

    cust_metric_name = utils.get_description_part(description, "MonitoredElementName")

    if cust_metric_name != "":
        metric_name = cust_metric_name

    cust_ci_id = utils.get_description_part(description, "CI_ID")

    if cust_ci_id != "":
        ci_id = cust_ci_id
        alert_key = ci_id + metric_name

    cust_platform = utils.get_description_part(description, "Platform")

    if cust_platform != "":
        platform = cust_platform

    cust_summary = utils.get_description_part(description, "Summary")

    if cust_summary != "":
        summary = cust_summary

    # AlertKey dependent settings

    # Logical Disk alarms
    if alert_key_upper.startswith("LOGICAL DISK FREE SPACE IS LOW") or patterns.CLUSTER_DISK_ALERT_PATTERN.search(
        alert_key_upper
    ):
        platform = "Windows"
        alert_group = f"{alert_group}-{platform}"
        # Add $monitoringObjectDisplayName to alert_key to add the disk
        alert_key = f"{alert_key}-{monitoring_object_display_name}"

    # Custom Application Process failed alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION PROCESS FAILED"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "ProcessName")

        alert_group = f"{alert_group}-{platform}"

    # Custom Application Service failed alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION SERVICE FAILED"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "ServiceName")

        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # Custom Application Share failed alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION SHARE FAILED"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "ShareName")

        alert_group = f"{alert_group}-{platform}"

    # Custom Application EventLog failed alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION EVENTLOG FAILED"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "EventLogName")
        alert_group = f"{alert_group}-{platform}"

    # Custom Application FileContent alarms
    elif patterns.CUSTOM_APP_FILECONTENT_PATTERN.search(alert_key_upper):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "FileContentPathName")
        alert_group = f"{alert_group}-{platform}"

    # Custom Application FileWatcher alarms
    elif patterns.CUSTOM_APP_FILEWATCHER_PATTERN.search(alert_key_upper):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "FileWatcherPathName")
        alert_group = f"{alert_group}-{platform}"

    # IIS Application Pool is unavailable
    # IIS Web Site is unavailable
    # IIS FTP Site is unavailable
    elif alert_key_upper.startswith("APPLICATION POOL IS UNAVAILABLE"):
        ci_id = f"IIS_Webapp_{element_name}_{node}"
        metric_class = "200203"
        iis_pool_site = True

    elif alert_key_upper.startswith("WEB SITE IS UNAVAILABLE"):
        ci_id = f"IIS_Website_{element_name}_{node}"
        iis_pool_site = True

    elif alert_key_upper.startswith("FTP SITE IS UNAVAILABLE"):
        ci_id = f"IIS_FTPSite_{element_name}_{node}"
        iis_pool_site = True

    if iis_pool_site:
        if platform == "":
            platform = monitoring_object_display_name
        alert_group = f"{alert_group}-{platform}"

    # IIS 'Web Server is unavailable' alarms
    elif alert_key_upper.startswith("WEB SERVER IS UNAVAILABLE"):
        ci_id = "IIS_" + node

        if platform == "":
            match = patterns.IIS_WEB_SERVER_PLATFORM_PATTERN.search(context_binary_packname)
            platform = match.group(1) if match else ""

        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # IIS 'The FTP service is unavailable' alarms
    elif alert_key_upper.startswith("THE FTP SERVICE IS UNAVAILABLE") or alert_key_upper.startswith(
        "FTP SERVER IS UNAVAILABLE"
    ):
        ci_id = "IIS_FTP_" + node
        platform = f"IIS (Windows) - {metric_name}"
        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # IIS 'server role is unavailable' alarms
    elif alert_key_upper.startswith("SERVER ROLE IS UNAVAILABLE"):
        ci_id = "IIS_" + node
        if platform == "":
            platform = context_name

        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # Cluster Switch alarms
    elif alert_key_upper.startswith("CLUSTER SWITCH"):
        alert_group = "Cluster Switch"
        severity = enums.Severity.WARNING.value  # Warning regardless the $severity value in the SCOM event
        if ci_id in static.NA_VALUES:
            ci_id = "A1580"

    # Health Service Heartbeat Failure
    elif alert_key_upper.startswith("HEALTH SERVICE HEARTBEAT FAILURE"):
        platform = "Windows"
        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # Print Spooler:  Service Status Monitor
    elif alert_key_upper.startswith("PRINT SPOOLER:  SERVICE STATUS MONITOR"):
        ci_id = "PRINTERSPOOLER_" + node
        metric_name = context_name
        platform = f"PRINTERSPOOLER (Windows) - {metric_name}"
        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # Exchange Health Set
    elif alert_key_upper.startswith("EXCHANGE HEALTH SET"):
        ci_id = "EXCHANGE_" + node
        metric_name = "ExchangeHealthSet"
        platform = "Exchange (Windows) - ExchangeHealthSet"
        alert_key = f"{alert_key}-{event_id}"

    # Skype
    elif alert_key_upper.startswith("SKYPE"):
        ci_id = "SKYPE_" + node
        metric_name = "skype"
        platform = "Exchange (Windows) - skype"
        alert_key = f"{alert_key}-{event_id}"

    # Active Directory
    elif alert_key_upper.startswith("AD"):
        match = patterns.AD_CI_ID_PATTERN.search(alert_key_upper)
        ci_id = match.group(1).strip() if match else ""
        platform = "Windows"
        alert_key = f"{alert_key}-{event_id}"

    # MSSQL
    elif alert_key_upper.startswith("MSSQL"):
        if patterns.MSSQL_LOG_FULL_PATTERN.search(alert_key_upper):
            # e.g.
            # description = "Event ID: 9002. The transaction log for database \'MS487_Zonal\'
            # is full due to \'LOG_BACKUP\'."
            # displayString = "MSSQL on Windows: Database log file is full.
            # Back up the transaction log for the database to free up some log space"
            # monitoringObjectDisplayName = "IICTZISQWS001\\ISICTR02A"
            # monitoringObjectFullName = "Microsoft.SQLServer.Windows.DBEngine:IICTZISQWS001.msnet.railb.be.ISICTR02A"
            # CI_ID = ISICTR02A\MS487_Zonal
            # Node = IICTZISQWS001
            computer, instance = patterns.extract_mssql_computer_instance(monitoring_object_display_name)
            database = patterns.extract_mssql_database_name(monitoring_object_display_name)
            ci_id = f"{instance}\\{database}"
            node = computer
            node_alias = node
            summary = description
            # Add ci_id to alert_key since node differs from ci_id
            alert_key = f"{alert_key}-{ci_id}"

        elif patterns.MSSQL_DATABASE_PATTERN.search(alert_key_upper):
            # e.g.
            # description = Database "Test_IICT311" on SQL Server instance "ISICTR03A",
            # computer "IICTCISQWC202.msnet.railb.be
            # displayString = "MSSQL on Windows: Database is in offline/recovery pending/suspect/emergency state"
            # monitoringObjectFullName
            # = Microsoft.SQLServer.Windows.Database:IICTCISQWC202.msnet.railb.be.ISICTR03A;Test_IICT311
            # CI_ID = ISICTR03A\Test_IICT311
            # Node = IICTCISQWC202
            computer, instance, database = patterns.extract_mssql_fullname_parts(monitoring_object_display_name)
            ci_id = f"{instance}\\{database}"
            node = computer
            node_alias = node
            summary = description
            # Add ci_id to alert_key since node differs from ci_id
            alert_key = f"{alert_key}-{ci_id}"

        else:
            # e.g. monitoringObjectDisplayName : IICTZISQWC002\ISA11520A
            # CI_ID = ISA11520A
            # Node = IICTZISQWC002
            ci_id = utils.re_search_group(r"([^\\]*)$", monitoring_object_display_name, "")
            node = utils.re_search_group(r"(^[^\\]*)", monitoring_object_display_name, "")
            node_alias = node
            # Add ci_id to alert_key since node differs from ci_id
            alert_key = f"{alert_key}-{ci_id}"

    # MonitoredElementName
    if metric_name == "<VAR>":
        if patterns.CLUSTER_DISK_ALERT_PATTERN.search(alert_key_upper):
            # Example : MonitoringObjectDisplayName=Cluster Disk 23_\\?\Volume{809630aa-d440-49cd-ba23-e81411f28df6}
            #           @MonitoredElementName=Cluster Disk 23
            match = patterns.CLUSTER_DISK_NAME_EXTRACTION_PATTERN.search(monitoring_object_display_name)
            if match:
                metric_name = match.group(1)
        elif patterns.DISK_GENERAL_PATTERN.search(alert_key_upper):
            # Example : DisplayString=Logical Disk Free Space is low
            #           MonitoringObjectDisplayName=O:\MountPoint2
            metric_name = monitoring_object_display_name
        else:
            metric_name = platform

    # SET NODE VALUE
    if node in static.NA_VALUES:
        context_logging_computer = context_logging_computer.upper()
        match = patterns.NETBIOS_MS_DOMAIN_PATTERN.search(context_logging_computer)
        if match:
            node = match.group(1)
            node_alias = node

            if ci_id in static.NA_VALUES:
                ci_id = node
        else:
            node = context_logging_computer
            node_alias = node
            if ci_id in static.NA_VALUES:
                ci_id = node

    if node in static.NA_VALUES:
        monitoring_object_display_name = monitoring_object_display_name.upper()
        match = patterns.NETBIOS_MS_DOMAIN_PATTERN.search(monitoring_object_display_name)
        if match:
            node = match.group(1)
            node_alias = node
            if ci_id in static.NA_VALUES:
                ci_id = node
        else:
            node = monitoring_object_display_name
            node_alias = node
            if ci_id in static.NA_VALUES:
                ci_id = node

    if alert_key_upper.startswith("A868 HEARTBEAT CONNECTION CHECK INFRABEL TO TIVOLI"):
        event_type = enums.AlarmType.HEARTBEAT.value
        severity = enums.Severity.INDETERMINATE.value
        clear_type = enums.ClearType.MANUALLY.value
        actionable = False
        if node in ["IICTCIAPWV436", "IICTCIAPWV437"]:
            # Only the production nodes should be monitored for heartbeat.
            ci_id = "SCOM_HB"
        else:
            ci_id = node
        alert_key = "A868 HeartBeat Alarm connection check Infrabel to Tivoli"
        alert_group = node
        metric_name = "Heartbeat"
        metric_type = "/ApplicationEvent/"
        # For heartbeats, the TimeRaised field is always populated with the same value,
        # so we have to use the LastModified field instead.
        raise_time_utc_str = row["LastModified"]
        raise_time = (
            datetime.datetime.strptime(raise_time_utc_str, "%d/%m/%Y %H:%M:%S").replace(tzinfo=None)
            if raise_time_utc_str != ""
            else None
        )

    if alert_group_upper == "HEARTBEAT":
        event_type = enums.AlarmType.HEARTBEAT.value
        severity = enums.Severity.INDETERMINATE.value
        clear_type = enums.ClearType.MANUALLY.value
        actionable = False
        if node in ["IICTCIAPWV436", "IICTCIAPWV437"]:
            # Only the production nodes should be monitored for heartbeat.
            ci_id = "SCOM_HB"
        else:
            ci_id = node
        alert_key = alert_key + "HeartBeat Alarm"
        alert_group = node
        metric_name = "Heartbeat"
        metric_type = "/ApplicationEvent/"
        # For heartbeats, the TimeRaised field is always populated with the same value,
        # so we have to use the LastModified field instead.
        raise_time_utc_str = row["LastModified"]
        raise_time = (
            datetime.datetime.strptime(raise_time_utc_str, "%d/%m/%Y %H:%M:%S").replace(tzinfo=None)
            if raise_time_utc_str != ""
            else None
        )

    # SET Resolution Type

    if managementpack_category_type == "EventCollection":
        clear_type = enums.ClearType.MANUALLY.value
    elif not (
        alert_key_upper.startswith("A868 HEARTBEAT ALARM CONNECTION CHECK INFRABEL TO TIVOLI")
        or alert_group_upper.startswith("HEARTBEAT")
    ):
        clear_type = enums.ClearType.AUTOMATICALLY.value

    if managementpack_category_type == "SecurityHealth":
        match = patterns.SECURITY_HEALTH_NODE_PATTERN.search(monitoring_object_display_name)
        node = match.group(1) if match else ""
        node_alias = node
        if ci_id in static.NA_VALUES:
            ci_id = node

    # SET Summary
    if description == "NULL" or description == "":
        summary = patterns.EVENT_DESCRIPTION_PREFIX_PATTERN.sub("", context_event_description)
    else:
        description = patterns.EVENT_DESCRIPTION_PREFIX_PATTERN.sub("", context_event_description)
        if summary == "":
            summary = patterns.NAMED_OBJECT_REPLACEMENT_PATTERN.sub(
                r"\1" + monitoring_object_display_name + r"\2", description
            )

    summary = f"{summary} / {context_data}"

    # SET Platform Value For SERVICE && PROCESS Alarms

    process = display_string.upper()

    if patterns.PROCESS_DETECTION_PATTERN.search(process):
        if patterns.VBS_FILE_PATTERN.search(context_param5):
            if platform == "":
                platform_val = patterns.extract_platform_value(context_param5, patterns.PLATFORM_NOLOGO_VBS_PATTERN)
                platform = platform_val + ".vbs" if platform_val else ""

            alert_group = f"{alert_group}-{platform}"
            metric_class = "200201"

        elif patterns.JS_FILE_PATTERN.search(context_param5):
            if platform == "":
                platform_val = patterns.extract_platform_value(context_param5, patterns.PLATFORM_NOLOGO_JS_PATTERN)
                platform = platform_val + ".js" if platform_val else ""

            alert_group = f"{alert_group} - {platform}"
            metric_class = "200201"

        elif patterns.JAVA_FILE_PATTERN.search(context_param5):
            if platform == "":
                platform_val = patterns.extract_platform_value(context_param5, patterns.PLATFORM_NOLOGO_JAVA_PATTERN)
                platform = platform_val + ".java" if platform_val else ""

            alert_group = f"{alert_group}-{platform}"
            metric_class = "200201"

        elif patterns.BAT_FILE_PATTERN.search(context_param5):
            if platform == "":
                platform_val = patterns.extract_platform_value(context_param5, patterns.PLATFORM_NOLOGO_BAT_PATTERN)
                platform = platform_val + ".bat" if platform_val else ""

            alert_group = f"{alert_group}-{platform}"
            metric_class = "200201"

        elif patterns.JAR_FILE_PATTERN.search(context_param5):
            if platform == "":
                platform_val = patterns.extract_platform_value(context_param5, patterns.PLATFORM_NOLOGO_JAR_PATTERN)
                platform = platform_val + ".jar" if platform_val else ""

            alert_group = f"{alert_group} - {platform}"
            metric_class = "200201"

        if metric_name == "<VAR>":
            metric_name = platform

    service = display_string.upper()
    service_name = context_name

    if patterns.SERVICE_STOPPED_PATTERN.search(service):
        metric_class = "200202"
        if metric_name == "lanmanserver":
            platform = "Windows"

        if platform == "":
            if service_name != "":
                platform = service_name

            elif metric_name != "":
                platform = metric_name

        if platform == "":
            platform = "Service Name is Missing from Event"
        else:
            alert_group = f"{alert_group}-{platform}"

        if metric_name == "<VAR>":
            if patterns.DISK_GENERAL_PATTERN.search(alert_key_upper):
                metric_name = monitoring_object_display_name
            else:
                metric_name = platform

    if metric_name == "NA":
        if patterns.SERVICE_STOPPED_PATTERN.search(service):
            if metric_name == "lanmanserver":
                platform = "Windows"

            if metric_type == "":
                metric_type = "/System/ServiceAvailability/"

            if metric_name == "":
                match = patterns.SERVICE_NAME_PATTERN.search(service_name)
                metric_name = match.group(1) if match else ""

            if platform == "":
                platform = metric_name

    if platform == "":
        platform = "Windows"

    if metric_name == "":
        metric_name = "NA"

    if ci_id in static.NA_VALUES:
        ci_id = "NA"

    # Still if Node has FQDN Name

    if node != "" and patterns.FQDN_MSNET_PATTERN.search(node):
        match = patterns.NETBIOS_MS_DOMAIN_PATTERN.search(node)
        if match:
            node = match.group(1)
            node_alias = node
            if ci_id in static.NA_VALUES:
                ci_id = node

    # details($*)
    if severity < 2:
        actionable = False

    wake_up_time = raise_time

    return pd.Series(
        (
            wake_up_time,
            event_id,
            event_type,
            actionable,
            raise_time,
            clear_time,
            clear_type,
            metric_type,
            metric_class,
            metric_name,
            alert_key,
            alert_group,
            node,
            node_alias,
            ci_id,
            summary,
            severity,
        )
    )


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _drop_empty_rows(df: pd.DataFrame) -> pd.DataFrame:
    """Drop the empty rows of the dataframe."""
    return df[(df["metric_name"] != static.NA_STR) | (df["metric_type"] != static.NA_STR)]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the manager for the DD."""
    return "mon-scom"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time() -> datetime.datetime:
    """Determine the handle time, i.e. the time when the alarm is handled by Olympus."""
    return olympus_utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the scope related to the EMS."""
    return enums.Scope.IT.value
