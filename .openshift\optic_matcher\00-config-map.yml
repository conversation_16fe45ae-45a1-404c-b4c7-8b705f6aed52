kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-optic-matcher-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/a2110_olympus/run.py
  DB_SCHEMA: "#{databaseSchema}#"
  DEBUG: "0"
  LOGS_FOLDER: "/data/logs"
  MUST_DO_IMPORT: "1"
  MUST_DO_MATCH: "1"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  OPTIC_DB_HOST: "#{opticDbHost}#"
  OPTIC_DB_PORT: "#{opticDbPort}#"
  OPTIC_DB_NAME: "#{opticDbName}#"
  OPTIC_DB_USER: "#{opticDbUser}#"
  GRAPH_SENDER: "#{sender}#"
  GRAPH_RECIPIENTS: >-
    #{recipients}#
  GRAPH_CC_RECIPIENTS: >-
    #{ccRecipients}#
  OLYMPUS_AGENT_ID: "0"
  ELASTIC_APM_SERVER_URL: "#{elasticApmServerUrl}#"
  ELASTIC_APM_SERVICE_NAME: "#{olympusServiceName}#"
  ELASTIC_APM_VERIFY_SERVER_CERT: "#{elasticApmVerifyServerCert}#"
  ENABLE_ELASTIC_APM: "#{enableElasticApm}#"
  ELASTIC_APM_ENVIRONMENT: "#{elasticApmEnvironment}#"
  ELASTIC_APM_LOG_LEVEL: "#{elasticApmLogLevel}#"
