"""OID mapping for vRealize."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for vRealize."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "trap_type",
            "snmptrap.vmwareAlertAliveServerName": "alert_alive_server_name",
            "snmptrap.vmwareAlertType": "alert_type",
            "snmptrap.vmwareAlertSubtype": "alert_subtype",
            "snmptrap.vmwareAlertHealth": "alert_health",
            "snmptrap.vmwareAlertRisk": "alert_risk",
            "snmptrap.vmwareAlertEfficiency": "alert_efficiency",
            "snmptrap.vmwareAlertMetricName": "alert__metric_name",
            "snmptrap.vmwareAlertResourceKind": "alert_resource_kind",
            "snmptrap.vmwareAlertDefinitionName": "alert_definition_name",
            "snmptrap.vmwareAlertDefinitionDesc": "alert_definition_description",
            "snmptrap.vmwareAlertImpact": "alert_impact",
            "snmptrap.vmwareAlertEntityName": "alert_entity_name",
            "snmptrap.vmwAlertNotificationRules": "alert_notification_rules",
            "snmptrap.vmwareAlertEntityType": "alert_entity_type",
            "snmptrap.vmwareAlertTimestamp": "alert_timestamp",
            "snmptrap.vmwareAlertCriticality": "alert_criticality",
            "snmptrap.vmwareAlertRootCause": "alert_root_cause",
            "snmptrap.vmwareAlertURL": "alert_url",
            "snmptrap.vmwareAlertID": "alert_id",
            "snmptrap.vmwareAlertMessage": "alert_message",
        }
