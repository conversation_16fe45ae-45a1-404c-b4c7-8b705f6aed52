"""Entrypoint for the application."""

import logging

from olympus_common.db import create_session_manager
from olympus_common.defaults import get_logger
from optic_matcher import statics
from optic_matcher.config import config
from optic_matcher.jobs import import_from_optic, match_alarms
from optic_matcher.mailing import send_graph_email


def match_optic_alarms() -> None:
    """Match Optic alarms with Olympus alarms."""
    backend_session_manager = create_session_manager(config.service_name)
    if config.must_do_import:
        import_from_optic(backend_session_manager)

    if config.must_do_match:
        with backend_session_manager.begin() as session:
            match_alarms(session)


def main() -> None:
    """Execute the main function for when the project is run."""
    get_logger(config.debug, config.logger_config, setup=True)
    try:
        match_optic_alarms()
    except Exception as exc:
        send_graph_email(
            exc,
            config.graph_config.recipients,
            statics.EMAIL_TEXT,
            statics.EMAIL_SUBJECT,
            config.graph_config.sender,
            config.graph_config.cc_recipients,
        )
        logging.exception(exc)
        raise Exception from exc
