"""Tests for the `main` module."""

import json
from pathlib import Path

from fastapi.testclient import Test<PERSON>lient
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from tests.utils import remove_middleware


def test_main(mocker: MockerFixture):
    """Test the webhook endpoint with test data."""
    from mon_big_data.main import app

    remove_middleware(app, "AuthMiddleware")

    mocked_database_writer = mocker.patch("mon_big_data.main.DatabaseWriter")
    client = TestClient(app)
    url = "http://127.0.0.1:8000/webhook"
    path = Path(__file__).parent / "data" / "01.json"
    test_content = json.loads(path.read_text())
    testdata = test_content.get("data", [])

    for item in testdata:
        item_input = item["input"]
        response = client.post(url, json=item_input)
        assert response.status_code == 200
        assert mocked_database_writer.return_value.success.called
        _assert_output(mocked_database_writer.return_value.success.call_args.args[0][0], item["output"])


def _assert_output(output: dict, expected: dict):
    """Assert that the output matched the expected keys' values.

    We don't do a simple equality check because the output may contain additional keys that we don't check/care about.
    """
    for key, value in expected.items():
        assert key in output, f"Key '{key}' not found in output"
        assert output[key] == value, f"Value for key '{key}' does not match: expected {value}, got {output[key]}"
