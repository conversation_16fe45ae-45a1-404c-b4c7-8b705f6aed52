"""Configuration module for ing-zabbix."""

from dataclasses import dataclass
from pathlib import Path

from olympus_common.config import KafkaProducerServiceConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import Singleton, pathstring


@dataclass(frozen=True)
class ZabbixConfig(metaclass=Singleton):
    """Represent the configuration for Zabbix."""

    user: str = env_field("ZABBIX_USER")
    password: str = env_field("ZABBIX_PASSWORD")
    endpoint: str = env_field("ZABBIX_ENDPOINT")
    drp_endpoint: str = env_field("ZABBIX_DRP_ENDPOINT", default="N/A")
    master_endpoint: str = env_field("MASTER_ZABBIX_ENDPOINT")
    master_drp_endpoint: str = env_field("MASTER_ZABBIX_DRP_ENDPOINT", default="N/A")
    heartbeat_ci: str = env_field("HEARTBEAT_CI")
    zabbix_cc: str = env_field("ZABBIX_CC")
    zabbix_server_alias: str = env_field("ZABBIX_SERVER_ALIAS", default="N/A")
    master_zabbix_server_alias: str = env_field("MASTER_ZABBIX_SERVER_ALIAS", default="N/A")
    master_user: str = env_field("MASTER_ZABBIX_USER")
    master_password: str = env_field("MASTER_ZABBIX_PASSWORD")


@dataclass(frozen=True)
class Config(KafkaProducerServiceConfig, metaclass=Singleton):
    """Represent the configuration for the Zabbix ingestion."""

    checkpoints_folder: Path = env_field("CHECKPOINTS_FOLDER", astype=Path, default=pathstring("checkpoints"))
    checkpoint_filename: str = "last_time.txt"
    zabbix: ZabbixConfig = dataclass_field(ZabbixConfig)

    @property
    def logformat(self) -> str:
        """Return the preferred logformat."""
        return (
            "%(asctime)s - %(filename)s(PATH:%(pathname)s) (function = %(funcName)s)"
            ", line %(lineno)d; %(levelname)s : %(message)s"
        )


config = Config()  # Initialize the singleton at import time.
