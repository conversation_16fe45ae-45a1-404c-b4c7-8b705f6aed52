"""Patterns module for mon-occ.

This module contains all compiled regular expression patterns used in the mon-occ module.
All patterns are pre-compiled for better performance and organized by their usage context.
"""

import re

# CI Name parsing Patterns

CI_NAME_SPLIT_PATTERN = re.compile(r"[:/]")
CI_VERSION_PATTERN = re.compile(r"-(\d+)/")
CI_ENVIRONMENT_PATTERN = re.compile(r"-([A-Za-z0-9]{3,4})-")

# Network and Host Patterns

IPV4_PATTERN = re.compile(r"(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)")
IPV4_SIMPLE_PATTERN = re.compile(r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}")

# Metric and Monitoring Patterns

USER_DEFINED_METRIC_PATTERN = re.compile(r"Metric=User-Defined", re.IGNORECASE)
RULE_NUMBER_PATTERN = re.compile(r"Rule\d+")
RULE_REPLACEMENT = "Rule*"

# Platform Patterns

PLATFORM_INFO_PATTERN = re.compile(r"Operating System=([^,]+),\s*Platform=([^,]+)")

# Utility Functions


def extract_ci_name_parts(ci_name: str) -> tuple[str, list[str], list[str]]:
    """Extract instance, version, and environment from CI name.

    Args:
        ci_name: The CI name string to parse

    Returns
    -------
        Tuple of (instance, version_list, environment_list)
    """
    instance = CI_NAME_SPLIT_PATTERN.split(ci_name)[-1]
    version = CI_VERSION_PATTERN.findall(ci_name)
    env = CI_ENVIRONMENT_PATTERN.findall(ci_name)
    return instance, version, env


def is_ipv4_address(host: str) -> bool:
    """Check if the host string is an IPv4 address.

    Args:
        host: The host string to check

    Returns
    -------
        True if the host is an IPv4 address, False otherwise
    """
    return bool(IPV4_SIMPLE_PATTERN.match(host))


def extract_platform_info(event_platform: str) -> tuple[str, str] | None:
    """Extract operating system and platform information.

    Args:
        event_platform: The event platform string to parse

    Returns
    -------
        Tuple of (operating_system, platform) or None if no match
    """
    if match := PLATFORM_INFO_PATTERN.search(event_platform):
        return match.group(1), match.group(2)
    return None


def substitute_rule_pattern(text: str) -> str:
    """Replace rule numbers with generic rule pattern.

    Args:
        text: The text containing rule patterns

    Returns
    -------
        Text with rule numbers replaced by 'Rule*'
    """
    return RULE_NUMBER_PATTERN.sub(RULE_REPLACEMENT, text)
