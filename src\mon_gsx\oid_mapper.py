"""OID mapping for GSX."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for GSX."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "snmptrap.trapMessage": "trapMessage",
            "snmptrap.gsxStationName": "gsxStationName",
            "snmptrap.className": "className",
            "snmptrap.alertType": "alertType",
            "snmptrap.resource": "resource",
            "snmptrap.subResource": "subResource",
            "snmptrap.status": "status",
            "snmptrap.severity": "severity",
            "snmptrap.date": "date",
            "snmptrap.alertID": "alertID",
            "snmptrap.EntityID": "EntityID",
            "snmptrap.Reminder": "Reminder",
            "snmptrap.Escalation": "Escalation",
            "snmptrap.Test": "Test",
            "snmptrap.StatisticsReset": "StatisticsReset",
            "snmptrap.SCOMSeverity": "SCOMSeverity",
            "snmptrap.AlertDate": "AlertDate",
            "@timestamp": "timestamp",
        }
