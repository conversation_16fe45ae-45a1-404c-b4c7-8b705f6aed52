---
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  labels:
    app: mail2incidents-app-#{appEnv}#
  name: a2110-mail2incidents-route-#{appEnv}#
  namespace: a2110-olympus-monitoring
spec:
  host: a2110-mail2incidents-#{appEnv}#.#{openshiftEnv}#
  to:
    kind: Service
    name: a2110-mail2incidents-service-#{appEnv}#
    weight: 100
  port:
    targetPort: http
  wildcardPolicy: None
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
