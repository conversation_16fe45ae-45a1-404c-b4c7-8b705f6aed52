"""Detail design implementation for mon-scada kst bns."""

from datetime import datetime

import pandas as pd

from mon_scada import enums as scada_enums
from mon_scada.config import config
from mon_scada.utils import is_active_status, is_tunnel_csj
from olympus_common import enums
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.utils import parse_datetime


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def metric_name(row: pd.Series) -> str:
    """Return the metric name for the DD."""
    return row["alarm_id"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def ci_id(row: pd.Series) -> str:
    """Return the ci_id for the DD."""
    ci_id = ""
    if not is_tunnel_csj(row):
        if row["asset_id"][3] == ":":
            ci_id = row["asset_id"][4:]
    return ci_id


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def severity(row: pd.Series) -> int:
    """Return the severity for the DD."""
    status: str = str(row["status"])
    priority: str = row["priority"]

    if not is_active_status(status=status):
        return enums.Severity.CLEARED.value

    if not is_tunnel_csj(row):
        if priority.isdigit():
            priority_number: int = int(priority)
            if priority_number >= 50 and priority_number <= 99:
                return enums.Severity.MINOR.value
            elif priority_number >= 100 and priority_number <= 139:
                return enums.Severity.MAJOR.value
            elif priority_number >= 140 and priority_number <= 189:
                return enums.Severity.CRITICAL.value

    return enums.Severity.INDETERMINATE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_time(row: pd.Series) -> datetime:
    """Return the raise time for the DD."""
    timestamp: str = row["timestamp"]
    return parse_datetime(timestamp)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def top_level() -> str:
    """Return the top level for the DD."""
    match config.scada_cc:
        case scada_enums.ScadaCC.KST.value:
            return "A2076"
        case scada_enums.ScadaCC.BNS.value:
            return "A1724"
        case scada_enums.ScadaCC.MST.value:
            return "A2189"
        case _:
            raise ValueError(f"scada_cc {config.scada_cc} does not exist.")
