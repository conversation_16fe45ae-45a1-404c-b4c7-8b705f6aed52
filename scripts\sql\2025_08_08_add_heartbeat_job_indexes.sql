-- Create two new indexes for the s2110_occurrence table
-- These indexes are designed to optimize the performance of queries related to heartbeat_job
CREATE INDEX IF NOT EXISTS s2110_occurrence_non_heartbeat_time_idx 
ON s2110_occurrence (agent_id, raise_time DESC) tablespace s2110_index
WHERE event_type IN ('problem', 'clear');

CREATE INDEX IF NOT EXISTS s2110_occurrence_heartbeat_time_idx 
ON s2110_occurrence (agent_id, ci_id, metric_name, metric_type, raise_time DESC) tablespace s2110_index
WHERE event_type = 'heartbeat';

-- To drop these indexes:
-- DROP INDEX CONCURRENTLY s2110_occurrence_non_heartbeat_time_idx
-- DROP INDEX CONCURRENTLY s2110_occurrence_heartbeat_time_idx