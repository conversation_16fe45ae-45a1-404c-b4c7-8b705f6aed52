"""OID mapping for Openshift."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Openshift."""

    @property
    def unmapped_oids(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "SNMPv2-SMI::enterprises.44645.324.1.2.1": "applicationCode",
            "SNMPv2-SMI::enterprises.44645.324.1.2.2": "openshiftCluster",
            "SNMPv2-SMI::enterprises.44645.324.1.2.3": "ownerCompany",
            "SNMPv2-SMI::enterprises.44645.324.1.2.4": "openshiftNamespace",
            "SNMPv2-SMI::enterprises.44645.324.1.2.5": "openshiftService",
            "SNMPv2-SMI::enterprises.44645.324.1.2.6": "identifier",
            "SNMPv2-SMI::enterprises.44645.324.1.2.7": "alertName",
            "SNMPv2-SMI::enterprises.44645.324.1.2.8": "alertId",
            "SNMPv2-SMI::enterprises.44645.324.1.2.9": "alertStart",
            "SNMPv2-SMI::enterprises.44645.324.1.2.10": "alertEnd",
            "SNMPv2-SMI::enterprises.44645.324.1.2.11": "alertStatus",
            "SNMPv2-SMI::enterprises.44645.324.1.2.12": "alertSeverity",
            "SNMPv2-SMI::enterprises.44645.324.1.2.13": "alertMessage",
            "SNMPv2-SMI::enterprises.44645.324.1.2.14": "openshiftCluster2",
            "SNMPv2-SMI::enterprises.44645.324.1.2.99": "alertFullMessage",
            "@timestamp": "messageTimestamp",
        }

    @property
    def drop_if_all_nan(self) -> list[str]:
        """Return fields to drop if all values are NaN."""
        return ["openshiftCluster2", "alertName"]

    def get_oids(self) -> list[dict]:
        """Return oid."""
        return [self.unmapped_oids]
