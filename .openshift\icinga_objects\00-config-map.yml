kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-icinga-objects-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/a2110_olympus/run.py
  DEBUG: "0"
  LOGS_FOLDER: "/data/logs"
  ICINGA_API_ENDPOINT: "#{icingaApiEndpoint}#"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  RUN_CIS_JOB: "#{runCisJob}#"
  RUN_METRICS_JOB: "#{runMetricsJob}#"
  RUN_DEPENDENCIES_JOB: "#{runDependenciesJob}#"
  CAN_DELETE_HOST: "#{canDeleteHost}#"
  PURGE_ICINGA_BASE: "#{purgeIcingaBase}#"
  PROCESS_CHECK_MISSING: "#{processCheckMissing}#"
  LOGS_MAX_SIZE: "490"
  EXTERNAL_TOKEN_EXPIRATION: "3600"
  ELASTIC_APM_SERVER_URL: "#{elasticApmServerUrl}#"
  ELASTIC_APM_SERVICE_NAME: "#{olympusServiceName}#"
  ELASTIC_APM_VERIFY_SERVER_CERT: "#{elasticApmVerifyServerCert}#"
  ENABLE_ELASTIC_APM: "#{enableElasticApm}#"
  ELASTIC_APM_ENVIRONMENT: "#{elasticApmEnvironment}#"
  ELASTIC_APM_LOG_LEVEL: "#{elasticApmLogLevel}#"
  OLYMPUS_AGENT_ID: "0"
