"""Patterns module for mon-netact."""

import re

PATTERN_IF_PROBLEM_CODE = re.compile(r"^([^|:\(]*)")
PATTERN_IF_NOT_METRIC_NAME = re.compile(r"^([^|:]*)")


def _extract_regex(pattern: re.Pattern[str], text: str) -> str:
    """Extract the text using the pattern.

    If the pattern does not match, return the text as is.
    """
    match = pattern.match(text)
    if match:
        return match.group(1)
    else:
        return text
