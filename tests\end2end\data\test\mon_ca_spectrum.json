{"extra_env": {"OLYMPUS_AGENT_ID": "3"}, "agent_ids": [3], "data": [{"input": {"alarm.repair_person": "", "alarm.id": "2593848", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x2100b2", "alarm.landscape": "0x8700000", "alarm.cause_code": "0x210e13", "alarm.type": "Set", "device.type": "Catalyst 9800-80 WLC", "@timestamp": "2025-02-05T05:07:25Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x8781586", "server": "iictyiaplv077", "alarm.model_type": "SwCiscoIOS", "alarm.model_name": "iictpiwlc01.network.railb.be", "alarm.severity": "MINOR", "source.ip": "*********", "alarm.security_string": "", "message": "Wed 05 Feb, 2025 - 06:07:25 - A \"ciscoLwappApIfUpNotify\" event has occurred, from SwCiscoIOS device, named iictpiwlc01.network.railb.be.\n\nThis notification is generated when AP's Interface operational status goes up.\n\ncLApSysMacAddress = 996954310\ncLApDot11IfSlotId = 0\ncLApDot11IfSlotId.cLApSysMacAddress.cLApDot11IfSlotId = ************.***********\ncLApPortNumber = 2\ncLApIfUpDownFailureType = detectedFailure\ncLApIfUpDownCause = Unknown\ncLApIfUpDownFailureCode = \ncLApName = lpleuve16\n\n(event [0x00210e12])\nOnly displaying most recent of 2 event messages.\n", "event.kafka.key": null, "event.kafka.offset": 2946754, "event.kafka.timestamp": "2025-02-05T05:07:26.730Z", "event.kafka.partition": 2, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictniapls016", "event.uuid": "5030c464-5d8e-42f1-85c7-8bf74bba1e82"}, "output": {}, "comment": "No records expected since cause_code 0x210e13 is not monitored."}, {"input": {"alarm.repair_person": "", "alarm.id": "2593848", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x2100b2", "alarm.landscape": "0x8700000", "alarm.cause_code": "0x210e13", "alarm.type": "Clear", "device.type": "Catalyst 9800-80 WLC", "@timestamp": "2025-02-05T05:07:25Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x8781586", "server": "iictyiaplv077", "alarm.model_type": "SwCiscoIOS", "alarm.model_name": "iictpiwlc01.network.railb.be", "alarm.severity": "MINOR", "source.ip": "*********", "alarm.security_string": "", "message": "Wed 05 Feb, 2025 - 06:07:25 - A \"ciscoLwappApIfUpNotify\" event has occurred, from SwCiscoIOS device, named iictpiwlc01.network.railb.be.\n\nThis notification is generated when AP's Interface operational status goes up.\n\ncLApSysMacAddress = 996954310\ncLApDot11IfSlotId = 0\ncLApDot11IfSlotId.cLApSysMacAddress.cLApDot11IfSlotId = ************.***********\ncLApPortNumber = 2\ncLApIfUpDownFailureType = detectedFailure\ncLApIfUpDownCause = Unknown\ncLApIfUpDownFailureCode = \ncLApName = lpleuve16\n\n(event [0x00210e12])\nOnly displaying most recent of 2 event messages.\n", "event.kafka.key": null, "event.kafka.offset": 2946755, "event.kafka.timestamp": "2025-02-05T05:07:28.951Z", "event.kafka.partition": 2, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictniapls016", "event.uuid": "f1fe9511-6ec6-409f-9ea8-b9d76e2b4ce4"}, "output": {}, "comment": "No records expected since cause_code 0x210e13 is not monitored."}, {"input": {"alarm.repair_person": "", "alarm.id": "2594005", "date": "02/05/2025", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x2100b2", "alarm.landscape": "0x8700000", "alarm.cause_code": "0xfff0032f", "alarm.type": "Set", "device.type": "Catalyst 9800-80 WLC", "@timestamp": "2025-02-05T05:07:49Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x8781586", "server": "iictyiaplv077", "time": "06:07:49", "alarm.model_type": "SwCiscoIOS", "alarm.model_name": "iictpiwlc01.network.railb.be", "alarm.severity": "MAJOR", "source.ip": "*********", "alarm.security_string": "", "message": "Wed 05 Feb, 2025 - 06:07:49 - A \"bsnAPDisassociated\" event has occurred, from SwCiscoIOS device, named iictpiwlc01.network.railb.be.\n\nWhen Airespace AP disassociates from Airespace Switch, AP\n        disassociated notification will be sent with dot3 MAC address\n        of Airespace AP management system to remove Airespace AP from\n        this Airespace Switch\n\nbsnAPMacAddrTrapVariable = 104.113.97.114754\nbsnAPName = lpscharj7\nbsnAPName.bsnAPDot3MacAddress = ************.101.102.176\n\n(event [0xfff0032f])\n", "event.kafka.key": null, "event.kafka.offset": 2943497, "event.kafka.timestamp": "2025-02-05T05:07:50.578Z", "event.kafka.partition": 0, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "5a86fd55-4043-4078-a5ca-cf54a13c6e35"}, "output": {"s2110_alarm": [{"ci_id": "lpscharj7", "metric_name": "fff0032f", "metric_type": "/HardwareEvent/", "event_id": "2594005", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "lpscharj7", "metric_name": "fff0032f", "metric_type": "/HardwareEvent/", "summary": "The WLC no longer is able to communicate with the access point", "severity": 3, "additional_data": {"cause_code": "fff0032f"}}]}}, {"input": {"alarm.repair_person": "", "alarm.id": "2594992", "date": "02/05/2025", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x3b10002", "alarm.landscape": "0x8700000", "alarm.cause_code": "0x10810", "alarm.type": "Set", "device.type": "MX104", "@timestamp": "2025-02-05T05:09:17.897746532Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x877bc7e", "server": "iictyiaplv077", "time": "06:09:14", "alarm.model_type": "JuniperJUNOSRtr", "alarm.model_name": "ammonsx1v-m.network.railb.be", "alarm.severity": "MINOR", "source.ip": "***********", "alarm.security_string": "", "message": "Wed 05 Feb, 2025 - 06:09:14 -  RMON rising threshold trap received from model ammonsx1v-m.network.railb.be of type JuniperJUNOSRtr.  AlarmIndex 32770, AlarmVariable *******.4.1.263*******3.1.*******.0, AlarmSampleType 1, AlarmValue 92 and AlarmRisingThreshold 85. (event [0x00010810]) \n", "event.kafka.key": null, "event.kafka.offset": 2946757, "event.kafka.timestamp": "2025-02-05T05:09:17.749Z", "event.kafka.partition": 2, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictniapls016", "event.uuid": "de2b7018-bfe5-4e02-97ea-511929c69440"}, "output": {}, "comment": "No records expected since cause_code 0x10810 is not monitored."}, {"input": {"alarm.repair_person": "", "alarm.id": "2595144", "date": "02/05/2025", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x2100b2", "alarm.landscape": "0x8700000", "alarm.cause_code": "0xfff0032f", "alarm.type": "Update", "device.type": "Catalyst 9800-80 WLC", "@timestamp": "2025-02-05T05:09:40.307523231Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x8781586", "server": "iictyiaplv077", "time": "06:09:39", "alarm.model_type": "SwCiscoIOS", "alarm.model_name": "iictpiwlc01.network.railb.be", "alarm.severity": "MAJOR", "source.ip": "*********", "alarm.security_string": "", "message": "Wed 05 Feb, 2025 - 06:09:38 - An LP Associated event has occurred, from SwCiscoIOS device, named iictpiwlc01.network.railb.be\n\ntrap = *******.*******.513.0.4\nLPname = lpmonsx35\n\n(event [0xfff0039e])\nOnly displaying most recent of 2 event messages.\n", "event.kafka.key": null, "event.kafka.offset": 2946759, "event.kafka.timestamp": "2025-02-05T05:09:40.265Z", "event.kafka.partition": 2, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictniapls016", "event.uuid": "ca0c014e-1f1a-4745-bcb6-9224452f2eaf"}, "output": {"s2110_alarm": [{"ci_id": "lpmonsx35", "metric_name": "fff0032f", "metric_type": "/HardwareEvent/", "event_id": "2595144", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "lpmonsx35", "metric_name": "fff0032f", "metric_type": "/HardwareEvent/", "summary": "The WLC no longer is able to communicate with the access point", "severity": 3}]}}, {"input": {"alarm.repair_person": "", "alarm.id": "2596097", "date": "02/05/2025", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x1160088", "alarm.landscape": "0x8700000", "alarm.cause_code": "0x10801", "alarm.type": "Set", "device.type": "systemEDGE Host", "@timestamp": "2025-02-05T05:11:29.777545431Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x87e6492", "server": "iictyiaplv077", "time": "06:11:28", "alarm.model_type": "Host_systemEDGE", "alarm.model_name": "RsyslogServer", "alarm.severity": "MINOR", "source.ip": "*************", "alarm.security_string": "", "message": "Wed 05 Feb, 2025 - 06:11:28 - Unknown alert received from device RsyslogServer of type Host_systemEDGE. Device Time 0+00:00:00. (Trap type *******.*******.867.6.1) (event [0x00010801])\nTrap var bind data: \nOID:  *******.*******.0  Value:  Wed Feb 05 05:11:28 UTC 2025\nOID:  *******.*******.4.1.0  Value:  *******.*******.867.1\nOID:  *******.********.3.0  Value:  *************\nOID:  *******.*******.867.2  Value:  b35f9fa8-f7a1-42b1-86c0-3bda0f593d83\nOID:  *******.*******.867.3  Value:  SYSTEM-MANAGED-SERVICES\nOID:  *******.*******.867.4  Value:  OK\nOID:  *******.*******.867.5  Value:   Managed Service elasticsearch is RUNNING \nOID:  *******.*******.867.6  Value:  []\nOID:  *******.*******.867.7  Value:  1.0.0\nOID:  *******.*******.867.8  Value:  Wed Feb 05 05:10:57 UTC 2025\nOID:  *******.*******.867.9  Value:  Cisco DNA Center System\nOID:  *******.*******.867.10  Value:  Platform Services\nOID:  *******.*******.867.11  Value:  INFO\nOID:  *******.*******.867.12  Value:  SYSTEM\nOID:  *******.*******.867.13  Value:  3\nOID:  *******.*******.867.14  Value:  DNAC API\nOID:  *******.*******.867.15  Value:  {\"Message\":\" Managed Service elasticsearch is RUNNING \",\"Instance\":\"dnacenter.msnet.railb.be : \",\"State\":\"OK\",\"Tags\":\"Managed Services\"}\n", "event.kafka.key": null, "event.kafka.offset": 2943515, "event.kafka.timestamp": "2025-02-05T05:11:29.727Z", "event.kafka.partition": 0, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "33d8797f-cdad-4d11-bd2b-02ea9673f44b"}, "output": {}, "comment": "No records expected since cause_code 0x10810 is not monitored."}, {"input": {"alarm.repair_person": "", "alarm.id": "2586867", "date": "02/05/2025", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x220011", "alarm.landscape": "0x8700000", "alarm.cause_code": "0x5c40011", "alarm.type": "Update", "device.type": "Cisco Nexus 9372PX", "@timestamp": "2025-02-05T05:11:38.512617052Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x874f94d", "server": "iictyiaplv077", "time": "06:11:37", "alarm.model_type": "Gen_IF_Port", "alarm.model_name": "rsmechly.network.railb.be_Ethernet1/45", "alarm.severity": "MAJOR", "source.ip": "************", "alarm.security_string": "", "message": "Wed 05 Feb, 2025 - 06:11:36 - Performance threshold alarm with alarmID 365550_365547_1104230 has been cleared.  (event [0x05c40009])Only displaying most recent of 2 event messages.\n", "event.kafka.key": null, "event.kafka.offset": 2943518, "event.kafka.timestamp": "2025-02-05T05:11:38.468Z", "event.kafka.partition": 0, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "5953cce1-acda-441e-a903-1324f2edd573"}, "output": {}, "comment": "No records expected since cause_code 0x5c40011 is not monitored."}, {"input": {"alarm.repair_person": "", "alarm.id": "2605173", "date": "02/05/2025", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x3b7000e", "alarm.landscape": "0x8700000", "alarm.cause_code": "0xfff002e9", "alarm.type": "Set", "device.type": "BigIP vCMP", "@timestamp": "2025-02-05T05:30:28.256749662Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x871f4b7", "server": "iictyiaplv077", "time": "06:30:25", "alarm.model_type": "F5BigIPDev", "alarm.model_name": "f5mechl2-prod.msnet.railb.be", "alarm.severity": "MAJOR", "source.ip": "*************", "alarm.security_string": "", "message": "Wed 05 Feb, 2025 - 06:30:26 - <PERSON><PERSON> received from device f5mechl2-prod.msnet.railb.be of type F5BigIPDev. Device Time 7+14:39:24. (Trap type *******.4.1.3375.2.4.6.301)\nTrap var bind data: \nOID:  *******.*******.0  Value:  65756419\nOID:  *******.*******.4.1.0  Value:  *******.4.1.3375.2.4.0.301\nOID:  *******.4.1.3375.*******  Value:  No members available for pool /Infrabel/a1112-int_pool (slot1)\nOID:  *******.*******.4.3.0  Value:  *******.4.1.3375.2.4\n\nPoolName=/Infrabel/a1112-int_pool\n\n(event [0xfff002e9])\nOnly displaying most recent of 4 event messages.\n", "event.kafka.key": null, "event.kafka.offset": 2940286, "event.kafka.timestamp": "2025-02-05T05:30:28.092Z", "event.kafka.partition": 1, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictniapls015", "event.uuid": "7ae585f2-9406-4206-98b0-5e025004e2db"}, "output": {"s2110_alarm": [{"ci_id": "Infrabel/a1112-int_pool:f5mechl2-prod", "metric_name": "fff002e9", "metric_type": "/HardwareEvent/", "event_id": "2605173", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "Infrabel/a1112-int_pool:f5mechl2-prod", "metric_name": "fff002e9", "metric_type": "/HardwareEvent/", "summary": "No members available for pool_Infrabel/a1112-int_pool", "severity": 5, "additional_data": {"cause_code": "fff002e9", "f5_pool": "Infrabel/a1112-int_pool"}}]}}, {"input": {"alarm.repair_person": "", "alarm.id": "2577457", "date": "02/05/2025", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x11c0012", "alarm.landscape": "0x8700000", "alarm.cause_code": "0xfff00246", "alarm.type": "Update", "device.type": "Cisco Catalyst 6807-XL", "@timestamp": "2025-02-05T05:41:39.242321836Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x87733ec", "server": "iictyiaplv077", "time": "06:41:38", "alarm.model_type": "CAT_IF_Port", "alarm.model_name": "rsnamur1v.network.railb.be_Gi1/14", "alarm.severity": "MAJOR", "source.ip": "**********", "alarm.security_string": "", "message": "Wed 05 Feb, 2025 - 06:41:37 - Performance threshold alarm with alarmID 89019_89018_1104229 has been cleared.  (event [0x05c40009])Only displaying most recent of 2 event messages.\n", "event.kafka.key": null, "event.kafka.offset": 2946895, "event.kafka.timestamp": "2025-02-05T05:41:39.097Z", "event.kafka.partition": 2, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictniapls016", "event.uuid": "43cf12e1-e541-4220-ab8d-d00268ffb046"}, "output": {}, "comment": "No records expected since cause_code 0xfff00246 is not monitored."}, {"input": {"alarm.repair_person": "", "alarm.id": "1705162", "date": "02/04/2025", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x2100b2", "alarm.landscape": "0x8700000", "alarm.cause_code": "0x00220001", "alarm.type": "Update", "device.type": "Cat37xxStack", "@timestamp": "2025-02-04T07:30:21.788014784Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x872fe78", "server": "iictyiaplv077", "time": "08:30:21", "alarm.model_type": "SwCiscoIOS", "alarm.model_name": "rsantno5v.network.railb.be", "alarm.severity": "MINOR", "source.ip": "*************", "alarm.security_string": "", "event": {"kafka": {"key": null, "offset": 2940898, "timestamp": "2025-02-04T07:30:21.644Z", "partition": 2, "topic": "a1215-spectrum-events-prd", "consumer_group": "a1559-logstash-a1215-spectrum-events-prd"}, "logstash": {"instance_name": "iictniapls016"}, "uuid": "ad458803-8676-463b-ba29-97aef91c2669"}, "message": "Tue 04 Feb, 2025 - 08:30:19 A(n) SwCiscoIOS device, named rsantno5v.network.railb.be, has detected a Communication Link Down. ifIndex = 11512, ifAdminStatus = , ifOperStatus = , ifDescr = FastEthernet4/0/12, ifType = 6.\n\nAdditional Variables:\nsysObjectID = \nsysName = \nlocIfReason = down\nifReasonForStatusCes = \nifPhysLocationCes = \nifPhysRelPosCes = \nifIpAddrCes = \nifNameCes = \nifTunnelRemoteIpAddrCes = \nifName = \nifAlias = \nusdSnmpTrapTrapSeverity = \n\n(event [0x00220101])\nOnly displaying most recent of 25 event messages.\n"}, "output": {"s2110_alarm": [{"ci_id": "rsantno5", "metric_name": "00220001", "metric_type": "/HardwareEvent/", "event_id": "1705162", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "rsantno5", "metric_name": "00220001", "metric_type": "/HardwareEvent/", "summary": "Link Up/Down", "severity": 3}]}}, {"input": {"alarm.repair_person": "", "alarm.id": "1705162", "date": "01/01/1970", "@version": "1", "ackd": "FALSE", "alarm.status": "", "alarm.model_type_handle": "0x2100b2", "alarm.landscape": "0x8700000", "alarm.cause_code": "0x00220001", "alarm.type": "Clear", "device.type": "Cat37xxStack", "@timestamp": "2025-02-04T07:30:46Z", "alarm_state": "NEW", "alarm.clearable": "TRUE", "alarm.model_handle": "0x872fe78", "server": "iictyiaplv077", "time": "01:00:00", "alarm.model_type": "SwCiscoIOS", "alarm.model_name": "rsantno5v.network.railb.be", "alarm.severity": "MINOR", "source.ip": "*************", "alarm.security_string": "", "message": "Tue 04 Feb, 2025 - 08:30:19 A(n) SwCiscoIOS device, named rsantno5v.network.railb.be, has detected a Communication Link Down. ifIndex = 11512, ifAdminStatus = , ifOperStatus = , ifDescr = FastEthernet4/0/12, ifType = 6.\n\nAdditional Variables:\nsysObjectID = \nsysName = \nlocIfReason = down\nifReasonForStatusCes = \nifPhysLocationCes = \nifPhysRelPosCes = \nifIpAddrCes = \nifNameCes = \nifTunnelRemoteIpAddrCes = \nifName = \nifAlias = \nusdSnmpTrapTrapSeverity = \n\n(event [0x00220101])\nOnly displaying most recent of 25 event messages.\n", "event.kafka.key": null, "event.kafka.offset": 2970804, "event.kafka.timestamp": "2025-02-10T05:30:46.230Z", "event.kafka.partition": 1, "event.kafka.topic": "a1215-spectrum-events-prd", "event.kafka.consumer_group": "a1559-logstash-a1215-spectrum-events-prd", "event.logstash.instance_name": "iictniapls015", "event.uuid": "d6a52d6a-1f03-452a-a1d7-9397880b70a5"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "rsantno5", "metric_name": "00220001", "metric_type": "/HardwareEvent/", "summary": "Link Up/Down", "clear_time": "2025-02-04 07:30:46", "severity": 3, "raise_time": "2025-02-04 07:30:46"}]}}]}