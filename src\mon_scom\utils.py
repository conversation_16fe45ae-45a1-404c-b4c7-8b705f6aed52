"""Utils module for mon-scom."""

import datetime
import re

import defusedxml.ElementTree as ETree

from mon_scom import patterns
from olympus_common import enums
from olympus_common.elastic_apm import CaptureSpan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def get_acode(mon_obj_display_string: str) -> str:
    """Return the a code contains in the given string (MonitoringObjectDisplayName field in SCOM)."""
    acodes = patterns.ACODE_EXTRACTION_PATTERN.findall(mon_obj_display_string.upper())
    return acodes[0] if acodes else "N/A"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def get_context_name(context: str) -> str:
    """Return the context name as in IMB concentrator (Property Name)."""
    property_ = _to_xml_and_find(context, "Property")
    if property_:
        return property_.attrib.get("Name", "")
    else:
        return ""


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def get_context_part(context: str, part_name: str) -> str:
    """Return the context part from the given part name as in IMB concentrator."""
    object_name = _to_xml_and_find(context, part_name)
    if object_name:
        text = object_name.text
        return text.strip() if text else ""
    else:
        return ""


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def get_context_param_x(context: str, param_number: int) -> str:
    """Return the context param number x as in IMB concentrator (from xml object Params containing Param children)."""
    params = _to_xml_and_find(context, "Params")
    if params is None:
        return ""
    param_list = params.findall("Param")

    try:
        param_element = param_list[param_number]
    except IndexError:
        return ""  # Param at idx `param_number` does not exist.
    else:
        return param_element.text.strip() if param_element.text else ""


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def get_description_part(description: str, part_name: str) -> str:
    """Return the description text part for the given part name.

    Notes
    -----
    The description field has a certain format and the retrieving of the given part is made with a regex.
    """
    pattern = patterns.create_description_part_pattern(part_name)
    if str_match := pattern.findall(description):
        description_part = str_match[0]
    else:
        description_part = ""
    return description_part


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _to_xml_and_find(context: str, path: str):
    """Convert the context to an ETree and find the path.

    If there was no xml-data, None is returned.
    """
    root = to_xml(context)
    if root is None:
        return None
    return root.find(path)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def to_xml(context: str):
    """Convert the context to an ETree.

    If context is 'N/A', None is returned.
    """
    if context == "N/A":
        return None
    return ETree.fromstring(context)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def str_to_timestamp(date_str: str, fmt: str = "%d/%m/%Y %H:%M:%S") -> int | str:
    """Return the timestamp from the given date string.

    If the date string is empty, an empty string is returned.
    We convert the date string to a datetime object with the given fmt and then to a timestamp.
    """
    if not date_str:
        return ""
    return int(datetime.datetime.timestamp(datetime.datetime.strptime(date_str, fmt)))


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def escape_backslash(s: str) -> str:
    """Return the string with the backslashes escaped."""
    return s.replace("\\", "\\\\")


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def re_search_group(pattern: str, text: str, default: str):
    """Do a regex search and return the match if found."""
    if match := re.search(pattern, text):
        return match.group()
    return default
