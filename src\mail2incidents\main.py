"""
Entrypoint for the application.

This code reads unread incoming e-mails in the Ypto folder (here named "inbox") for the ICC.
If the e-mail can't be processed for any reason,
the e-mail is moved to a "To process" folder to be handled by the operators of the ICC.
Once the e-mail is processed (incident created or updated in SAP and e-mail and attachments added to the incident),
The e-mail is renamed by adding the qmnum at the start of the subject of the e-mail and marked as read.
"""

import logging

from fastapi import FastAPI, HTTPException

from mail2incidents.config import config  # type: ignore
from mail2incidents.msgraph import MSGraphAPI  # type: ignore
from olympus_common import auth, defaults

app = FastAPI(debug=config.debug)

if not config.debug:
    auth.add_security(app, config.jwk_config)


@app.get("/mail2incidents")
def main_endpoint():
    """Execute the main function for when the project is run."""
    try:
        graph_email = MSGraphAPI()
        graph_email.process()
    except Exception as err:
        logging.exception(f"Error during email processing : {err}")
        raise HTTPException(status_code=500) from err


def main():
    """Run mail2incidents."""
    defaults.run_fastapi_service(app, config)
