"""Custom datawriter module for icinga-events."""

import logging
from dataclasses import dataclass, field
from typing import Any

from icinga_events.config import config
from icinga_events.icinga_alarm import CustomIcingaAlarm
from icinga_events.utils import add_or_update_host, add_or_update_service, get_process_error
from olympus_common import enums, icinga, utils
from olympus_common.datawriters import DataWriter
from olympus_common.db import get_alarm_field
from olympus_common.elastic_apm import CaptureSpan, elastic_apm, trace_scan
from olympus_common.exceptions import OlympusError


@dataclass
class IcingaWriter(DataWriter):
    """Represent a IcingaWriter."""

    host_keys: list[str] = field(default_factory=list)
    service_keys: list[str] = field(default_factory=list)
    should_update_hosts: bool = False
    should_update_services: bool = True

    @trace_scan(enums.MeasureType.CUSTOM.value)
    def success(self, results: list[dict]) -> None:
        """Add the alarms in Icinga and set a flag to True if everything works fine.

        Add the hosts and events in Icinga if they don't already exist.
        Process a check result on the events.
        Update the process_status field in Alarm if the previous steps succeed.

        Notes
        -----
        The process is split in three loops to avoid Icinga being drowned with too many add requests.
        """
        if not results:
            logging.debug("Nothing to send to the IcingaAPI")
            return None

        icinga_alarms: list[CustomIcingaAlarm] = [CustomIcingaAlarm.from_dict(result) for result in results]
        client = icinga.IcingaClient(use_logging=True)
        valid_alarms: list[CustomIcingaAlarm] = []

        for idx, alarm in enumerate(icinga_alarms):
            try:
                if alarm.has_missing_enrichment():
                    results[idx]["process_status"] = enums.AlarmJobStatus.NOT_DONE.value
                    continue

                if config.app_env == "prod" and not get_alarm_field(alarm, "actionable"):
                    # In production, only send actionable alarms to Icinga.
                    results[idx]["process_status"] = enums.AlarmJobStatus.NOT_DONE.value
                    continue

                valid_alarms.append(alarm)
            except Exception as exc:
                results[idx]["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
                results[idx]["process_error"] = str(exc)
                elastic_apm.capture_exception()

        if not valid_alarms:
            return

        unique_hosts = {alarm.encoded_hostname for alarm in valid_alarms}
        unique_services = {alarm.encoded_servicename for alarm in valid_alarms}

        existing_hosts: set[str] = set()
        existing_services: set[str] = set()

        if unique_hosts:
            for encoded_hostname in unique_hosts:
                try:
                    if client.get(enums.IcingaObjectType.HOST.value, encoded_hostname):
                        existing_hosts.add(encoded_hostname)
                except Exception as exc:
                    logging.error(f"Error while getting host {encoded_hostname} : {exc}")

        if unique_services:
            for encoded_servicename in unique_services:
                try:
                    if client.get(enums.IcingaObjectType.SERVICE.value, encoded_servicename):
                        existing_services.add(encoded_servicename)
                except Exception as exc:
                    logging.error(f"Error while getting service {encoded_servicename} : {exc}")

        self._process_alarms(
            valid_alarms=valid_alarms,
            existing_hosts=existing_hosts,
            existing_services=existing_services,
            client=client,
            results=results,
        )

    @CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
    def error(self, _: list[dict], __: Exception) -> None:
        """Do nothing on error."""
        return

    @CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
    def _process_alarms(
        self,
        valid_alarms: list[CustomIcingaAlarm],
        existing_hosts: set[str],
        existing_services: set[str],
        client: icinga.IcingaClient,
        results: list[dict],
    ) -> None:
        """Process alarms with optimized batch operations."""
        processed_hosts: set[str] = set()
        processed_services: set[str] = set()
        host_operations: list[dict[str, Any]] = []
        service_operations: list[dict[str, Any]] = []

        for alarm in valid_alarms:
            if alarm.encoded_hostname not in processed_hosts:
                try:
                    host_exists = alarm.encoded_hostname in existing_hosts
                    operation = self._determine_host_operation(host_exists)
                    details = self._get_host_details(alarm, operation)
                    host_operations.append(
                        {
                            "client": client,
                            "hostname": alarm.hostname,
                            "encoded_hostname": alarm.encoded_hostname,
                            "details": details,
                            "operation": operation,
                            "ci_id": alarm.identification,
                            "status": enums.IcingaHostStatus.OK.value,
                        }
                    )
                    logging.info(f"host operation details: {host_operations}")
                    processed_hosts.add(alarm.encoded_hostname)
                except Exception as exc:
                    error_msg = f"Error while processing host {alarm.encoded_hostname} : {exc}"
                    logging.exception(error_msg)
                    elastic_apm.capture_exception()

            service_key = f"{alarm.hostname}!{alarm.servicename}"
            if service_key not in processed_services:
                try:
                    service_exists = alarm.encoded_servicename in existing_services
                    operation = self._determine_service_operation(service_exists)
                    details = self._get_service_details(alarm, operation)
                    service_operations.append(
                        {
                            "client": client,
                            "alarm_id": alarm.id,
                            "service_name": alarm.servicename,
                            "encoded_service_name": alarm.encoded_servicename,
                            "host_name": alarm.hostname,
                            "details": details,
                            "summary": get_alarm_field(alarm, "summary"),
                            "status": alarm.status,
                            "operation": operation,
                        }
                    )
                    logging.info(f"Service operation details: {service_operations}")
                    processed_services.add(service_key)
                except Exception as exc:
                    error_msg = f"Error while processing host {alarm.encoded_hostname} : {exc}"
                    logging.exception(error_msg)
                    elastic_apm.capture_exception()

        if host_operations and service_operations:
            self._execute_parallel_operations(
                host_operations=host_operations, service_operations=service_operations, results=results
            )

        client.show_counter_values()
        client.reset_counter()
        return

    @CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
    def _determine_host_operation(self, host_exists: bool) -> str:
        """Determine the operation type for a host."""
        if host_exists:
            if self.should_update_hosts:
                return enums.IcingaObjectOperation.UPDATE.value
            else:
                return enums.IcingaObjectOperation.NONE.value
        else:
            return enums.IcingaObjectOperation.ADD.value

    @CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
    def _determine_service_operation(self, service_exists: bool) -> str:
        """Determine the operation type for a service."""
        if service_exists:
            if self.should_update_services:
                return enums.IcingaObjectOperation.UPDATE.value
            else:
                return enums.IcingaObjectOperation.NONE.value
        else:
            return enums.IcingaObjectOperation.ADD.value

    @CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
    def _get_host_details(self, alarm: CustomIcingaAlarm, operation: str) -> dict[str, Any]:
        """Get the details for a host."""
        if operation == enums.IcingaObjectOperation.NONE.value:
            return {}
        details = alarm.details(needed_keys=self.host_keys)
        if operation == enums.IcingaObjectOperation.UPDATE.value and "groups" in details:
            del details["groups"]
        return details

    @CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
    def _get_service_details(self, alarm: CustomIcingaAlarm, operation: str) -> dict[str, Any]:
        """Get the details for a service."""
        if operation == enums.IcingaObjectOperation.NONE.value:
            return {}
        details = alarm.details(needed_keys=self.service_keys)
        if operation == enums.IcingaObjectOperation.UPDATE.value and "groups" in details:
            del details["groups"]
        return details

    @CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
    def _execute_parallel_operations(
        self,
        host_operations: list[dict[str, Any]],
        service_operations: list[dict[str, Any]],
        results: list[dict[str, Any]],
    ):
        """Execute host and service operations in parallel."""
        host_exceptions = []
        service_exceptions = []
        if host_operations:
            host_args = [
                (
                    operation["client"],
                    operation["hostname"],
                    operation["encoded_hostname"],
                    operation["details"],
                    operation["operation"],
                    operation["ci_id"],
                    operation["status"],
                )
                for operation in host_operations
            ]
            host_exceptions = utils.parallelize_process(host_args, add_or_update_host, config.thread_number)
            if not host_exceptions:
                for result in results:
                    result["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
                    result["process_error"] = "Empty host exceptions"
                raise OlympusError("Unexpected empty host exceptions.")

        if service_operations:
            host_exception_dict = {exc["id"]: exc["exception"] for exc in host_exceptions if exc["exception"]}
            service_args = [
                (
                    operation["client"],
                    operation["alarm_id"],
                    operation["service_name"],
                    operation["encoded_service_name"],
                    operation["host_name"],
                    operation["details"],
                    operation["summary"],
                    operation["status"],
                    operation["operation"],
                    [host_exception_dict.get(operation["host_name"])],
                )
                for operation in service_operations
            ]
            service_exceptions = utils.parallelize_process(service_args, add_or_update_service, config.thread_number)
            if not service_exceptions:
                for result in results:
                    result["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
                    result["process_error"] = "Empty service exceptions"
                raise OlympusError("Unexpected empty service exceptions.")
        self._update_results_status(results, service_exceptions)

    @CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
    def _update_results_status(self, results: list[dict], service_exceptions: list[dict | None]) -> None:
        """Update process status for all results based on the on service exceptions."""
        service_exceptions_sorted = sorted(service_exceptions, key=lambda service_exception: service_exception["id"])
        for result in results:
            process_error = get_process_error(id=result["id"], exceptions=service_exceptions_sorted)
            if "process_status" not in result or process_error is None:
                result["process_status"] = enums.AlarmJobStatus.DONE.value
            elif result["process_status"] != enums.AlarmJobStatus.NOT_DONE.value:
                result["process_status"] = enums.AlarmJobStatus.DONE.value
            else:
                result["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
                result["process_error"] = process_error
