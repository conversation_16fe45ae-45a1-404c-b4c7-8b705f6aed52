"""Module to provide generic utilities to an application."""

import concurrent.futures
import json
import logging
import re
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Callable, Sequence

from dateutil import parser
from dotenv import load_dotenv

from olympus_common.exceptions import OlympusError


class classproperty:  # noqa: N801
    """Represent a property to work on classes rather than instances.

    Parameters
    ----------
    func : Callable
        The function to wrap.

    Notes
    -----
    Decorate the function with `classmethod` to ensure correct type inference happens and linters pass.
    The `classmethod` decorator should be above the `classproperty` decorator.

    Examples
    --------
    class Foo:
        @classmethod
        @classproperty
        def myproperty(cls):
            return f"property of {cls.__name__}"

    >>> Foo.myproperty
    "property of Foo"
    """

    def __init__(self, func: Callable) -> None:
        self.fget = func

    def __get__(self, _, owner):
        """Call the property function and pass the class to it.

        self refers to class `classproperty` and the second argument would be the instance,
        but we do not use it, so it is named `_`, the final `owner` argument is the class which should be
        passed to the function.
        """
        return self.fget(owner)


class Singleton(type):
    """Represent a singleton object.

    To be used as a metaclass for object which should be singletons.
    When a second instance of the object is created without any arguments, the initial one is just returned.
    When a second instance of the object is created with arguments, an OlympusError is raised to let the dev know
    that they are re-initializing a Singleton.
    """

    _instances: dict = {}

    def __call__(cls, *args: Any, **kwargs: Any):
        """Ensure that initialisation is only performed once for all children of Singleton."""
        if cls in cls._instances:
            if args or kwargs:
                raise OlympusError(
                    f"{cls.__name__} is a Singleton object. It can only be initialized once. After the initialization, "
                    f"it is no longer allowed to initialize the class with {args=} or {kwargs=}"
                )
        else:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]

    @classmethod
    def already_initialised(cls, key: Any) -> bool:
        """Return whether the key already exists in Singleton's memory.

        This is a convenience function to avoid the need for users to access the private attribute _instances.
        """
        return key in cls._instances


def strtobool(val: str) -> bool:
    """Convert a string representation of truth to True or False.

    True values are 'y', 'yes', 'true', 'on', and '1'.
    False values are 'n', 'no', 'false', 'off', and '0'.

    Parameters
    ----------
    val : str
        The string to convert to a boolean.

    Returns
    -------
    bool
        The boolean value corresponding to the given string.

    Raises
    ------
    ValueError
        if 'val' is anything else than the allowed true and false values.

    Notes
    -----
    This method is an adapted copy of distutils.util.strtobool. Since the distutils module is deprecated we needed our
    own copy of this function. Values "t" and "f" are not considered valid in our function, while they were allowed in
    distutils' implementation.
    """
    val = val.lower()
    true_values = ("y", "yes", "true", "on", "1")
    false_values = ("n", "no", "false", "off", "0")
    if val in true_values:
        return True
    elif val in false_values:
        return False
    else:
        raise ValueError(f"Invalid truth value {val}. Expected one of {true_values + false_values}.")


def write_in_file(folder_path: Path, filename: str, content: str | bytes, inplace: bool = False) -> None:
    """Write the given content into the given file in the given folder.

    Notes
    -----
    The function will first ensure the folder and file exists. If not, it will create the missing ones.
    If the function receive neither byte nor str in content the content will be cast to string.

    Parameters
    ----------
    folder_path : Path
        The path for the folder in which the file will be located.
    filename : str
        The name of the file in which the content will be written.
    content : str | bytes
        The content that will be written into the file.
    inplace : bool, optional
        If True, the function will write the given content inplace of the potential existing one, by default False.
    """
    if not isinstance(content, (str, bytes)):
        content = str(content)

    # Ensure folder and file exist
    folder_path.mkdir(exist_ok=True, parents=True)
    file_path = folder_path / filename

    mode = "w" if inplace else "a"
    if isinstance(content, bytes):
        mode += "b"

    # Write the content
    with file_path.open(mode=mode) as f:
        f.write(content)


def read_file_content(folder_path: Path, filename: str, return_str: bool = True) -> bytes | str:
    """Read the content of the given file and return it as string or bytes following the return_str boolean.

    Notes
    -----
    The function will first ensure the folder and file exists. If not, it will create the missing ones.

    Parameters
    ----------
    folder_path : Path
        The path for the folder in which the file will be located.
    filename : str
        The name of the file in which the content will be read.
    return_str : bool, optional
        Define the return type. If True, return a string else return bytes, by default True

    Returns
    -------
    bytes | str
        The content of the file
    """
    # Ensure folder and file exist
    folder_path.mkdir(exist_ok=True, parents=True)
    file_path = folder_path / filename
    file_path.touch(exist_ok=True)
    # Read and return the file content
    return file_path.read_text() if return_str else file_path.read_bytes()


def str_to_set(text_str: str) -> set:
    """Transform the given str into a set if this is possible.

    Notes
    -----
    This function will raise from the used libraries if the transformation is not possible.

    Parameters
    ----------
    text_str : str
        The text that will be transformed into a set.

    Returns
    -------
    set
        The transformed text.
    """
    return set(json.loads(text_str)) if text_str else set()


def camel_to_snake(input_string: str) -> str:
    """Convert a string from camel case to snake case."""
    return re.sub(r"(?<!^)(?=[A-Z])", "_", input_string).lower()


def parallelize_process(arguments: Sequence, process: Callable, thread_number: int) -> list:
    """Allow any process to be parallelized.

    If first argument in arguments is a tuple given the tuple values as input for function calls else perform the calls
    with each element in the arguments as parameters.

    Notes
    -----
    The if arguments is to avoid having to check if arguments is None before each call of this function.

    Parameters
    ----------
    arguments : Sequence
        The sequence containing the arguments for the function calls.
    process : Callable
        The function to parallelize.
    thread_number : int
        The number of thread to use for the parallelization.

    Returns
    -------
    list | None
        If arguments is not None or empty return the list of returned values from the function calls.
    """
    if arguments:
        logging.info(f"Starting process: {process.__name__}")
        logging.info(f"Number of object to be parallelized: {len(arguments)}")
        if type(arguments[0]) is not tuple:
            with concurrent.futures.ThreadPoolExecutor(thread_number) as executor:
                result = executor.map(process, arguments)
        else:
            with concurrent.futures.ThreadPoolExecutor(thread_number) as executor:
                result = executor.map(lambda args: process(*args), arguments)
        logging.info(f"Closing process: {process.__name__}")
        return list(result)
    else:
        logging.warning("No arguments given to the parallelized function. Should this function be parallelized ?")
        return []


def pathstring(name: str | Path) -> str:
    """Return the string-path to the given name on the same level as the caller.

    Parameters
    ----------
    name : str | Path
        The name of the file or folder to get the path for.

    Returns
    -------
    str
        The path to the given name.

    Notes
    -----
    This function should not be called from olympus-common as it would not correctly return the path to the caller's
    file.

    Examples
    --------
    # file /dev/my_file.py
    >>> from olympus_common.utils import pathstring
    >>> pathstring("my_folder")
    "/dev/my_folder"
    """
    # Go up the frame-stack to find the first frame that is not in this file and use that as a parent-directory.
    # This means that `name` will have the same parent as the caller's file.
    frame = sys._getframe()
    current_file = __file__

    while frame.f_code.co_filename == current_file:
        assert frame.f_back is not None
        frame = frame.f_back
    frame_filename = frame.f_code.co_filename
    path = Path(frame_filename).absolute().parent
    return str(path / name)


def load_dotenv_from_service(name: str):
    """Load the dotenv file for the service.

    First, we load the root .env file, then the service's .env file.
    This makes it, so we can have a .env file in the root folder that is shared between all services, while also having
    service-specific .env files to define service-specific environment variables.
    """
    src_dir = Path(__file__).parent.parent
    root_dir = src_dir.parent
    service_dir = src_dir / name

    root_dotenv = root_dir / ".env"
    if root_dotenv.exists():
        load_dotenv(root_dotenv, override=True)

    service_dotenv = service_dir / ".env"
    if service_dotenv.exists():
        load_dotenv(service_dotenv, override=True)


def datetime_from_utc_to_local(utc_datetime: datetime) -> datetime:
    """Convert an UTC datetime to a local datetime."""
    now_timestamp = time.time()
    offset = datetime.fromtimestamp(now_timestamp) - datetime.fromtimestamp(now_timestamp, timezone.utc).replace(
        tzinfo=None
    )
    return utc_datetime + offset


def datetime_from_local_to_utc(local_datetime: datetime) -> datetime:
    """Convert an UTC datetime to a local datetime."""
    now_timestamp = time.time()
    offset = datetime.fromtimestamp(now_timestamp) - datetime.fromtimestamp(now_timestamp, timezone.utc).replace(
        tzinfo=None
    )
    return local_datetime - offset


def now_naive() -> datetime:
    """Return the current UTC time without timezone information.

    This is useful to always start from utc times regardless of the local machine's timezone.
    """
    return utc_now().replace(tzinfo=None)


def utc_now() -> datetime:
    """Return the current UTC time."""
    return datetime.now(tz=timezone.utc)


def now_timestamp() -> int:
    """Return the current UTC time as an integer timestamp."""
    return int(utc_now().timestamp())


def mb_to_bytes(nb: float | int | str) -> int:
    """Convert MB to Bytes by multiplying with 1024 twice."""
    return int(float(nb) * 1024 * 1024)


def parse_datetime(date_str: str) -> datetime:
    """Return the datetime object from the given date string, ignore info."""
    return parser.parse(date_str, ignoretz=True)


def is_valid_md5(md5_str: str) -> bool:
    """Check if the given string is a valid md5."""
    return len(md5_str) == 32 and md5_str.isalnum()
