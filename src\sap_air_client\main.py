"""Entrypoint for the application."""

import logging
from http import HTTPStatus

from apscheduler.schedulers.background import BackgroundScheduler
from elasticapm.contrib.starlette import ElasticAPM
from fastapi import FastAPI
from fastapi.concurrency import asynccontextmanager
from fastapi.responses import JSONResponse

from olympus_common import auth, defaults
from olympus_common.datareaders import KafkaReader
from olympus_common.elastic_apm import elastic_apm
from sap_air_client import jobs
from sap_air_client.config import config
from sap_air_client.models import (
    AlarmReleaseLink,
    AlarmsUserEntity,
    AlarmUserEntity,
    SapDataToUpdate,
    SapEntityIncident,
    UserActionAdd,
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Define the lifespan of the FastAPI app.

    In FastAPI v2, the `app.on_event` function is deprecated.
    Instead, we can use the `lifespan` function to define the lifespan of the FastAPI app.

    References
    ----------
    https://fastapi.tiangolo.com/advanced/events/
    """
    scheduler = BackgroundScheduler()
    kafka_reader = KafkaReader()
    scheduler.add_job(jobs.read_kafka, "interval", args=[kafka_reader], seconds=60)
    scheduler.add_job(jobs.update_incidents_status_sync, "interval", seconds=120, max_instances=1)
    scheduler.add_job(jobs.update_releases_status_sync, "interval", seconds=120, max_instances=1)
    logging.getLogger("apscheduler.executors.default").setLevel(logging.ERROR)
    scheduler.start()
    try:
        yield
    finally:
        # Shutdown background jobs and kafka consumers before stopping the uvicorn server
        kafka_reader.close_consumer()
        scheduler.shutdown()


app = FastAPI(debug=config.debug, openapi_url="/openapi.json", lifespan=lifespan if config.lifespan_enabled else None)

# The security middleware should be added before the elastic middleware,
# otherwise the elastic middleware will not be able to access the security middleware.
if not config.debug:
    auth.add_security(app, config.jwk_config)


if elastic_apm.config.enable_elastic_apm and elastic_apm.client:
    app.add_middleware(middleware_class=ElasticAPM, client=elastic_apm.client)


@app.get("/sap/entity/{entity_id}")
async def get_entity_route(entity_id: int):
    """Get a specific incident from SAP endpoint."""
    return await jobs.get_and_update_incident(entity_id)


@app.get("/sap/entity/icinga/{entity_id}")
async def get_entity_for_icinga_route(entity_id: int):
    """Get a specific incident from SAP endpoint."""
    return await jobs.get_incident_for_ui(entity_id)


@app.post("/sap_air/entity/create")
async def create_incident_route(entity: SapEntityIncident):
    """Create SAP incident and AIR procedure."""
    try:
        return await jobs.create_incident(entity)
    except Exception as exc:
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": exc.__str__()})


@app.post("/sap/entity/link")
async def link_incident_route(entity: SapDataToUpdate):
    """Add link information to sap incident and update icinga."""
    try:
        return await jobs.link_incident(entity)
    except Exception as exc:
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": exc.__str__()})


@app.post("/sap/entity/unlink")
async def unlink_incident_route(entity: SapDataToUpdate):
    """Add unlink information to sap incident and update icinga."""
    try:
        return await jobs.unlink_incident(entity)
    except Exception as exc:
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": exc.__str__()})


@app.post("/alarm/clear")
async def clear_alarm_route(entity: AlarmUserEntity):
    """Clear an alarm manually to Icinga."""
    return await jobs.clear_alarm(entity)


@app.post("/alarm/clear/multiple")
async def clear_alarms_route(entity: AlarmsUserEntity):
    """Clear multiple alarms manually to Icinga."""
    return await jobs.clear_alarms(entity)


@app.post("/alarm/enrich")
async def enrich_alarm_route(alarm_id: int):
    """(Re)enrich an alarm manually."""
    return await jobs.enrich_alarm(alarm_id)


@app.post("/sap/release/link")
async def link_release_route(entity: AlarmReleaseLink):
    """Link an alarm to a release."""
    try:
        return await jobs.link_release(entity)
    except Exception as exc:
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": exc.__str__()})


@app.post("/sap/release/unlink")
async def unlink_release_route(entity: AlarmReleaseLink):
    """Unlink an alarm from a release."""
    try:
        return await jobs.unlink_release(entity)
    except Exception as exc:
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": exc.__str__()})


@app.get("/sap/release/icinga/compact-details/{release_number}")
def get_release_details_compact_for_icinga_route(release_number: int):
    """Get a specific release with concise date from SAP endpoint."""
    return jobs.get_release_for_ui(release_number)


@app.post("/useraction/add")
def add_user_action_route(entity: UserActionAdd):
    """Add user action in the database.

    Some user-actions are created by the system on creation of certain entities. This endpoint is used to manually
    create user-actions like comment and acknowledge.
    """
    return jobs.add_user_action(entity)


@app.get("/")
def get():
    """Get a specific incident from SAP endpoint."""
    return "sap_air_client module for api call"


def main() -> None:
    """Run the module in mode server."""
    defaults.run_fastapi_service(app, config)
