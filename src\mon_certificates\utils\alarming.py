"""Alarming module for mon-certificates.

This file contains all the functions for the creation and management of the alarms and clears corresponding to the
certificates monitoring.
"""

import json
import uuid

import pandas as pd
from sqlalchemy.orm import Session

from mon_certificates import static
from mon_certificates.enums import CertificateType
from olympus_common import db as c_db
from olympus_common import enums as c_enums
from olympus_common import utils


def get_existing_cleared_cis(session: Session, agent_id: int) -> list[str | None]:
    """Get the CIs from existing clears from the backend DB."""
    clears = c_db.Occurrence.get_occurrences_of_agent(session, agent_id, c_enums.AlarmType.RESOLUTION.value)

    if clears is None:
        return []

    return [clear.ci_id for clear in clears]


def get_existing_alerted_cis(session: Session, agent_id: int) -> list[str | None]:
    """Get the CIs from existing alarms from the backend DB."""
    problems = c_db.Occurrence.get_occurrences_of_agent(session, agent_id, c_enums.AlarmType.PROBLEM.value)

    if problems is None:
        return []

    return [problem.ci_id for problem in problems]


def create_alarms(
    cert_type: CertificateType, df: pd.DataFrame, existing_alerted_cis: list[str | None], agent_id: int
) -> pd.DataFrame:
    """Create the list of alarm corresponding to the given certificate to expire that doesn't exist yet.

    Parameters
    ----------
    cert_type : CertificateType
        The type of the certificate (refer to the issuer).
    df : pd.DataFrame
        The dataframe containing the certificate that expire.
    existing_alerted_cis : list[str | None]
        The list of the already alerted CIs.
    agent_id : int
        The agent ID.

    Returns
    -------
    pd.DataFrame
        The dataframe containing the new alarms for the certificates monitoring.

    Raises
    ------
    ValueError
        If the given type of the certificate is not one of the following: ["NACADCA", "TALOS"].
    """
    existing_ci_id = [alerted_ci.upper() for alerted_ci in existing_alerted_cis if alerted_ci is not None]
    match cert_type:
        case CertificateType.NACADCA.value:
            group_column_name = "organizational_unit"
        case CertificateType.TALOS.value:
            group_column_name = "application_code"
        case _:
            raise ValueError(
                "The given type of the certificate is not one of the following: "
                f"{list(CertificateType.__members__.keys())}"
            )
    alarms = [
        _create_alarm_dict(
            cert_type,
            row["common_name"],
            row["not_after"],
            row[group_column_name],
            agent_id,
            c_enums.AlarmType.PROBLEM.value,
            row.get("not_before", None),
        )
        for _, row in df.iterrows()
        if row[group_column_name] != "N/A" and row["common_name"].upper() not in existing_ci_id
    ]

    df_alarms = pd.DataFrame(alarms)
    df_alarms = df_alarms.dropna(axis=0, how="all")

    return df_alarms


def create_clears(
    df_emergency: pd.DataFrame,
    cert_name_column: str,
    existing_alerted_cis: list[str | None],
    existing_cleared_cis: list[str | None],
    agent_id: int,
) -> list[dict]:
    """Create the alarm structure for the given certificate data.

    Parameters
    ----------
    df_emergency : pd.DataFrame
        The dataframe containing the alarms for which the lifetime left is into the emergency category.
    cert_name_column : str
        The column name corresponding to the CI ID.
    existing_alerted_cis : list[str | None]
        The list of the already alerted CIs.
    existing_cleared_cis : list[str | None]
        The list of the already cleared CIs.
    agent_id : int
        The agent ID.

    Returns
    -------
    list[dict]
        List of alarms with the correct structure.
    """
    emergency_alert_cis = df_emergency[cert_name_column].values.tolist()

    return [
        _create_alarm_dict(None, alerted_ci, None, None, agent_id, c_enums.AlarmType.RESOLUTION.value, None)
        for alerted_ci in existing_alerted_cis
        if alerted_ci not in emergency_alert_cis and alerted_ci not in existing_cleared_cis
    ]


def _create_alarm_dict(
    cert_type: CertificateType | None,
    cert_name: str | None,
    not_after: str | None,
    group_name: str | None,
    agent_id: int,
    event_type: str,
    not_before: str | None = None,
) -> dict:
    """Create the alarm structure for the given certificate data.

    Parameters
    ----------
    cert_type : CertificateType | None
        The type of the given certificate.
    cert_name : str | None
        The name of the certificate.
    not_after : str | None
        Date of expiration of the certificate.
    group_name : str | None
        Name of the certificate's group.
    agent_id : int
        The agent ID.
    event_type : str
        The type of the event (problem or resolution).
    not_before : str | None, optional
        Date before which the certificate is not used, by default None

    Returns
    -------
    dict
        Dictionary containing the alarm with the correct structure.
    """
    ci_id = cert_name
    metric_type = "/event/"  # hardcoded
    summary = (
        f"The certificate {cert_name} will expire the {not_after}. It left less than 14 days before expiration."
        if event_type == c_enums.AlarmType.PROBLEM.value
        else None
    )

    if event_type == c_enums.AlarmType.PROBLEM.value:
        additional_data_dict = {"not_after": not_after}
        if not_before:
            additional_data_dict["not_before"] = not_before
        if cert_type == CertificateType.NACADCA.value:
            additional_data_dict["organizational_unit"] = group_name
        additional_data = json.dumps(additional_data_dict)
    else:
        additional_data = None

    metric_name = "validity"
    severity = (
        c_enums.Severity.CRITICAL.value
        if event_type == c_enums.AlarmType.PROBLEM.value
        else c_enums.Severity.CLEARED.value
    )
    manager = static.MANAGER_NAME
    event_type = event_type
    raise_time = utils.now_naive()
    clear_time = raise_time if event_type == c_enums.AlarmType.RESOLUTION.value else None
    clear_type = c_enums.ClearType.AUTOMATICALLY.value
    action_class = c_enums.Scope.IT.value
    top_level = (
        group_name if event_type == c_enums.AlarmType.PROBLEM.value and cert_type == CertificateType.TALOS else None
    )

    event_id = str(uuid.uuid1())
    actionable = True

    return {
        "summary": summary,
        "severity": severity,
        "metric_name": metric_name,
        "metric_type": metric_type,
        "agent_id": agent_id,
        "manager": manager,
        "event_type": event_type,
        "raise_time": raise_time,
        "clear_time": clear_time,
        "clear_type": clear_type,
        "action_class": action_class,
        "top_level": top_level,
        "event_id": event_id,
        "actionable": actionable,
        "additional_data": additional_data,
        "ci_id": ci_id,
    }
