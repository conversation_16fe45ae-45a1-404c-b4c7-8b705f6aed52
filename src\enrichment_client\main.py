"""Entrypoint for the application."""

from enrichment_client import enrichment
from enrichment_client.config import config
from olympus_common import defaults
from olympus_common.core import Application
from olympus_common.datareaders import DatabaseReader
from olympus_common.datawriters import DatabaseWriter
from olympus_common.db import Enrichment, SessionManager, create_session_manager
from olympus_common.monitoring.monitoring_client import JobMonitoringClient
from olympus_common.ucmdb import create_ucmdb_session_manager


def _init_application() -> tuple[Application, JobMonitoringClient, SessionManager, SessionManager]:
    backend_session_manager = create_session_manager(config.service_name)
    ucmdb_session_manager = create_ucmdb_session_manager()
    datareader = DatabaseReader(session_manager=backend_session_manager, job_name="enrichment")
    logger = defaults.get_logger(config.debug, config.logger_config)
    datawriter = DatabaseWriter(session_manager=backend_session_manager, object_class=Enrichment)
    job_monitoring_client = JobMonitoringClient(application_name=config.service_name)
    application = Application(datareader=datareader, datawriter=datawriter, logger=logger)
    return application, job_monitoring_client, backend_session_manager, ucmdb_session_manager


application, job_monitoring_client, backend_session_manager, ucmdb_session_manager = _init_application()


@application.run_forever(sleep_time=config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Execute the main function for when the project is run."""
    job_monitoring_client.send_heartbeat()
    enrichments = enrichment.enrichment(
        alarms_be=data, backend_session_manager=backend_session_manager, ucmdb_session_manager=ucmdb_session_manager
    )
    job_monitoring_client.check_jobs_in_error(alarm_jobs=data)
    return enrichments
