"""Entrypoint for the application."""

import icinga_objects.statics as statics
from icinga_objects import jobs
from icinga_objects.config import config
from olympus_common import defaults
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType
from olympus_common.ucmdb import create_ucmdb_session_manager


@trace_scan(MeasureType.CUSTOM.value)
def run_jobs(run_cis_job: bool, run_metrics_job: bool, run_dependencies_job: bool) -> None:
    """Run all jobs to synchronise UCMDB and Icinga."""
    ucmdb_session_manager = create_ucmdb_session_manager()
    ucmdb_session = ucmdb_session_manager.get_session()
    # 1- start by CI's (hosts) jobs to match with the Icinga logic
    if run_cis_job:
        for ci_type in statics.CI_TYPE:
            jobs.cis_job(ci_type, ucmdb_session)

    # 2- start gmt (services)  jobs
    if run_metrics_job:
        for source in statics.SOURCES:
            jobs.metrics_job(source, ucmdb_session)

    # 3- start dependencies  jobs
    if run_dependencies_job:
        jobs.relation_job(ucmdb_session)


def main() -> None:
    """Read data from UCMDB, update Icinga with the hosts, services and execute their checks."""
    defaults.get_logger(debug=config.debug, logger_config=config.logger_config, setup=True)
    run_jobs(
        run_cis_job=config.run_cis_job,
        run_metrics_job=config.run_metrics_job,
        run_dependencies_job=config.run_dependencies_job,
    )
