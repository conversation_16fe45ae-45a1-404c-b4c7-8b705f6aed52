"""Module to manage the data model for SAP and AIR."""

from typing import Any, Self

from pydantic import BaseModel, Field, model_validator

from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import MeasureType
from olympus_common.utils import is_valid_md5
from sap_air_client.utils import convert_datetime_sap_to_str

MAX_LONG_TEXT_LENGTH = 1990


class Model(BaseModel):
    """Override the BaseModel class to customize the model_dump method."""

    def model_dump(self, by_alias: bool = True, **kwargs: Any) -> dict[str, Any]:
        """Return a dict representation of the model with alias set to True."""
        return super().model_dump(by_alias=by_alias, **kwargs)


class SapDataView(Model):
    """SAP Data View model."""

    activity: str | None = Field(default=None, alias="Activity")
    created_by_name: str | None = Field(default=None, alias="CreatedByName")
    customer_name: str | None = Field(default=None, alias="CustomerName")
    long_text: str | None = Field(default=None, alias="LongText")
    main_order_number: str | None = Field(default=None, alias="MainOrderNumber")
    priokx: str | None = Field(default=None, alias="Priokx")
    qmcod: str | None = Field(default="0100", title="Qmcod", description="", alias="Qmcod")
    qmcod_txt: str | None = Field(default=None, alias="QmcodTxt")
    qmgrp: str | None = Field(default=None, title="Qmgrp", description="", alias="Qmgrp")
    qmnum: str | None = Field(default=None, title="Qmnum", description="", alias="Qmnum")
    qmtxt: str | None = Field(default=None, title="Qmtxt", description="", alias="Qmtxt")
    service_txt: str | None = Field(default=None, alias="ServiceTxt")
    severity: str | None = Field(default=None, alias="Severity")
    stat_ord_sys: str | None = Field(default=None, alias="StatOrdSys")
    stat_ord_sys_txt: str | None = Field(default=None, alias="StatOrdSysTxt")
    stat_ord_usr_ns: str | None = Field(default=None, alias="StatOrdUsrNs")
    stat_ord_usr_ns_txt: str | None = Field(default=None, alias="StatOrdUsrNsTxt")
    stat_ord_usr_seq: str | None = Field(default=None, alias="StatOrdUsrSeq")
    stat_ord_usr_seq_txt: str | None = Field(default=None, alias="StatOrdUsrSeqTxt")
    work_center: str | None = Field(default=None, title="WorkCenter", description="The work center", alias="WorkCenter")

    # release source -> to be displayed on icinga on release details
    id: str | None = Field(default=None, title="Id", alias="Id")
    user_status: str | None = Field(default=None, alias="UserStatus")
    user_status_text: str | None = Field(default=None, alias="UserStatusText")
    floc: str | None = Field(default=None, alias="Floc")
    floc_desc: str | None = Field(default=None, alias="FlocDesc")
    costcenter: str | None = Field(default=None, alias="Costcenter")
    cost_center_txt: str | None = Field(default=None, alias="CostCenterTxt")
    emergency_ltext: str | None = Field(default=None, alias="EmergencyLtext")
    equipment: str | None = Field(default=None, alias="Equipment")
    equipment_desc: str | None = Field(default=None, alias="EquipmentDesc")
    planned_end_date: str | None = Field(default=None, alias="PlannedEndDate")
    planned_start_date: str | None = Field(default=None, alias="PlannedStartDate")
    priority: str | None = Field(default=None, alias="Priority")
    priority_txt: str | None = Field(default=None, alias="PriorityTxt")
    required_start_date: str | None = Field(default=None, alias="RequiredStartDate")
    required_end_date: str | None = Field(default=None, alias="RequiredEndDate")
    service: str | None = Field(default=None, alias="Service")
    short_text: str | None = Field(default=None, alias="ShortText")
    subservice: str | None = Field(default=None, alias="Subservice")
    subservice_text: str | None = Field(default=None, alias="SubserviceText")

    @staticmethod
    def format_long_text(text: str | None) -> str:
        """Format long text from the api call to be more readable."""
        if text is None:
            return ""
        formatted_text = text.replace("\r\n", "<br>")
        return formatted_text[:MAX_LONG_TEXT_LENGTH]

    def model_post_init(self, __context) -> None:
        """Validate the model."""
        self.long_text = self.format_long_text(self.long_text)
        self.planned_start_date = convert_datetime_sap_to_str(self.planned_start_date)
        self.planned_end_date = convert_datetime_sap_to_str(self.planned_end_date)
        self.required_start_date = convert_datetime_sap_to_str(self.required_start_date)
        self.required_end_date = convert_datetime_sap_to_str(self.required_end_date)


class SapDataToUpdate(Model):
    """SAP Data model to update in icinga."""

    # Incident source
    qmnum: str | None = Field(title="Qmnum", default="", alias="Qmnum")
    stat_ord_usr_seq: str | None = Field(title="StatOrdUsrSeq", default="", alias="StatOrdUsrSeq")
    stat_ord_usr_seq_txt: str | None = Field(title="StatOrdUsrSeqTxt", default="", alias="StatOrdUsrSeqTxt")
    tt_main_alarm: str | None = Field(title="TTMainAlarm", default="", alias="TTMainAlarm")
    long_text: str | None = Field(title="LongText", default=None, alias="LongText")
    confirmation_text: str | None = Field(title="ConfirmationText", default=None, alias="ConfirmationText")
    reason: str | None = Field(title="Reason", default=None, alias="Reason")
    floc_id: str | None = Field(title="FlocId", default=None, alias="FlocId")
    metric_type: str | None = Field(default=None)
    metric_name: str | None = Field(default=None)
    alarm_id: str | None = Field(default=None)
    user_id: str | None = Field(default=None)
    user_comment: str | None = Field(default=None)

    # Release source
    user_status: str | None = Field(title="UserStatus", default=None, alias="UserStatus")
    user_status_text: str | None = Field(title="UserStatusText", default=None, alias="UserStatusText")
    floc: str | None = Field(title="Floc", default=None, alias="Floc")  # the equivalent of floc_id for the release

    @CaptureSpan(MeasureType.CUSTOM.value)
    def incident_status(self) -> dict:
        """Return the required fields to update the status."""
        return {"StatOrdUsrSeqTxt": self.stat_ord_usr_seq_txt}

    @CaptureSpan(MeasureType.CUSTOM.value)
    def release_status(self) -> dict:
        """Return the required fields to update the status."""
        return {"StatOrdUsrSeqTxt": self.user_status_text}

    @CaptureSpan(MeasureType.CUSTOM.value)
    def create(self):
        """Return data to send after create command."""
        return {"Qmnum": self.qmnum, "StatOrdUsrSeqTxt": self.stat_ord_usr_seq_txt, "TTMainAlarm": self.tt_main_alarm}

    def model_post_init(self, __context) -> None:
        """Validate the model."""
        if self.long_text:
            self.long_text = self.long_text[:MAX_LONG_TEXT_LENGTH]


class AirEntity(Model):
    """The AI-Reactivity Entity."""

    identification: str | None = Field(default=None)
    floc_id: str | None = Field(default=None, alias="FlocId")
    metric_type: str | None = Field(default=None)
    metric_name: str | None = Field(default=None)
    model: str | None = Field(default=None)
    subgroups: list[str] | str | None = Field(default=None)
    a_codes: list[str] | None = Field(default=None)
    sap_incident_id: str | None = Field(default=None)
    severity: str | None = Field(default=None, alias="Severity")

    @model_validator(mode="after")
    def check_ci_or_floc_id(self) -> Self:
        """Check if identification or floc_id are valid."""
        if self.identification is None and self.floc_id is None:
            raise ValueError("Either ci or floc_id should be provided")
        return self

    @CaptureSpan(MeasureType.CUSTOM.value)
    def to_air(self) -> "AirEntity":
        """Return air data to run procedure."""
        return AirEntity(**self.model_dump())

    def model_post_init(self, __context) -> None:
        """Return the metric name without the md5 hash."""
        if self.metric_name:
            metric_name_splitted = self.metric_name.split("/")
            if "/" in self.metric_name and is_valid_md5(metric_name_splitted[-1]):
                self.metric_name = "/".join(metric_name_splitted[:-1])


class SapEntityCustomVars(Model):
    """The Custom vars for Sap Entity."""

    summary: str | None = Field(default=None)
    metric_name: str | None = Field(default=None)
    identification: str | None = Field(default=None)
    action_class: str | None = Field(default=None)
    description: str | None = Field(default=None)
    raised_at: str | None = Field(default=None)
    last_occurred_at: str | None = Field(default=None)
    tally: str | None = Field(default=None)
    a_codes: list[str] = Field(default=[])
    category: str | None = Field(default=None)
    ip_address: str | None = Field(default=None)
    location_address: str | None = Field(default=None)
    location_attribute: str | None = Field(default=None)
    location_category: str | None = Field(default=None)
    location_type: str | None = Field(default=None)
    metric_type: str | None = Field(default=None)
    model: str | None = Field(default=None)
    run_procedure: bool | None = Field(default=None)
    source: str | None = Field(default=None)
    subgroups: list[str] | str | None = Field(default=None)
    main_service: str | None = Field(default=None)
    main_sub_service: str | None = Field(default=None)
    alarm_id: str | None = Field(default=None)
    user_id: str | None = Field(default=None)
    user_comment: str | None = Field(default=None)
    is_first_ack: bool = Field(default=False)

    def get_qmtxt(self) -> str:
        """Return qmtxt value with a limit of 40 characters."""
        qmtxt = f"MON-{self.identification}-{self.metric_name}"
        return qmtxt[:40]

    def get_work_center(self) -> str:
        """Return work_center value."""
        if self.action_class == "cybersecurity":
            return "T31C"
        elif self.action_class == "IT":
            return "T31A"
        else:
            return "T31B"

    def model_post_init(self, __context) -> None:
        """Transform the field to right format after."""
        if self.subgroups and isinstance(self.subgroups, str):
            self.subgroups = self.subgroups.split(",")


class SapEntityIncident(SapEntityCustomVars):
    """Represent a SAP Incident entity based on SAP fields."""

    work_center: str | None = Field(
        default="T22B", title="WorkCenter", description="The work center", alias="WorkCenter"
    )
    api_origin: str | None = Field(default="0114", title="ApiOrigin", description="The api origin", alias="ApiOrigin")
    user_status_txt: str | None = Field(default=None, title="UserStatusTxt", alias="UserStatusTxt")
    user_status: str | None = Field(default=None, title="UserStatus", alias="UserStatus")
    urgency_txt: str | None = Field(default=None, title="UrgencyTxt", alias="UrgencyTxt")
    stat_ord_usr_seq_txt: str | None = Field(
        default=None, title="Main Order user sequential status", alias="StatOrdUsrSeqTxt"
    )
    stat_ord_usr_seq: str | None = Field(default=None, title="Main Order user sequential status", alias="StatOrdUsrSeq")
    stat_ord_usr_ns_txt: str | None = Field(
        default=None, title="Main Order user non sequential status", alias="StatOrdUsrNsTxt"
    )
    stat_ord_usr_ns: str | None = Field(
        default=None, title="Main Order user non sequential status", alias="StatOrdUsrNs"
    )
    stat_ord_sys_txt: str | None = Field(default=None, title="Main Order system status", alias="StatOrdSysTxt")
    stat_ord_sys: str | None = Field(default=None, title="Main Order system status", alias="StatOrdSys")
    stat_not_usr_ns_txt: str | None = Field(
        default=None, title="notification user not sequential status", alias="StatNotUsrNsTxt"
    )
    stat_not_usr_ns: str | None = Field(
        default=None, title="notification user not sequential status", alias="StatNotUsrNs"
    )
    stat_not_sys_txt: str | None = Field(default=None, title="Notification system status", alias="StatNotSysTxt")
    stat_not_sys: str | None = Field(default=None, title="Notification system status", alias="StatNotSys")
    sod_request: bool | None = Field(default=None, title="SodRequest", alias="SodRequest")
    required_start_time: str | None = Field(default=None, title="RequiredStartTime", alias="RequiredStartTime")
    required_start_date: str | None = Field(default=None, title="RequiredStartDate", alias="RequiredStartDate")
    required_end_time: str | None = Field(default=None, title="RequiredEndTime", alias="RequiredEndTime")
    required_end_date: str | None = Field(default=None, title="RequiredEndDate", alias="RequiredEndDate")
    qmtxt: str | None = Field(default=None, title="notification description", alias="Qmtxt")
    qmnum: str | None = Field(default=None, title="notification number", alias="Qmnum")
    qmgrp: str | None = Field(default=None, title="service", alias="Qmgrp")
    qmcod_txt: str | None = Field(default=None, title="sub service", alias="QmcodTxt")
    qmcod: str | None = Field(default=None, title="sub service", alias="Qmcod")
    priokx: str | None = Field(default=None, title="priority", alias="Priokx")
    priok: str = Field(default="4", alias="Priok")
    open: bool | None = Field(default=None, title="is open", alias="Open")
    objnr: str | None = Field(default=None, title="object number", alias="Objnr")
    notif_time: str | None = Field(default=None, title="notification time", alias="NotifTime")
    notif_date: str | None = Field(default=None, title="NotifDate and time", alias="NotifDate")
    flag_main_order_create: bool = Field(
        default=True, title="FlagMainOrderCreate", description="Flag main order create", alias="FlagMainOrderCreate"
    )
    flag_main_order_release: bool = Field(
        default=True, title="FlagMainOrderRelease", description="Flag main order release", alias="FlagMainOrderRelease"
    )
    mzeit: str | None = Field(default=None, title="NotifTime", alias="Mzeit")
    main_order_number: str | None = Field(default=None, title="MainOrderNumber", alias="MainOrderNumber")
    last_modified_by_name: str | None = Field(default=None, title="LastModifiedByName", alias="LastModifiedByName")
    last_modified_by_id: str | None = Field(default=None, title="LastModifiedById", alias="LastModifiedById")
    floc_id: str | None = Field(default=None, title="Functional location", alias="FlocId")
    floc_description: str | None = Field(default=None, title="functional location", alias="FlocDescription")
    equipment_id: str | None = Field(default=None, title="EquipmentId", alias="EquipmentId")
    equipment_description: str | None = Field(default=None, title="EquipmentDescription", alias="EquipmentDescription")
    urgency: str = Field(default="3", title="Urgency", description="", alias="Urgency")
    urgency_text: str | None = Field(default=None, title="UrgencyText", alias="UrgencyText")
    created_by_name: str | None = Field(default=None, title="CreatedByName", alias="CreatedByName")
    created_by_id: str | None = Field(default=None, title="CreatedById", alias="CreatedById")
    completed: bool | None = Field(default=None, title="is completed", alias="Completed")
    to_conversation: dict | None = Field(default=None, title="ToConversation", description="", alias="ToConversation")
    to_partners: dict | None = Field(default=None, title="ToPartners", description="", alias="toPartners")  # noqa: N815
    to_sub_orders: dict | None = Field(default=None, title="ToSubOrders", description="", alias="toSubOrders")  # noqa: N815
    to_tot_log: dict | None = Field(default=None, title="ToTotLog", description="", alias="toTotLog")  # noqa: N815
    to_originated_by: dict | None = Field(default=None, title="ToOriginateBy", description="", alias="toOriginatedBy")  # noqa: N815
    to_history: dict | None = Field(default=None, title="ToHistory", description="", alias="toHistory")  # noqa: N815
    to_change_indicator: dict | None = Field(
        default=None, title="ToChangeIndicator", description="", alias="toChangeIndicator"
    )  # noqa: N815
    to_attachments: dict | None = Field(default=None, title="ToAttachments", description="", alias="toAttachments")  # noqa: N815
    to_incident_partners: list[dict] | None = Field(
        default=None, title="ToIncidentPartners", description="", alias="toIncidentPartners"
    )  # noqa: N815
    to_long_text: list[dict] | None = Field(default=None, title="ToLongText", description="", alias="toLongText")  # noqa: N815
    severity: str | None = Field(default=None, alias="Severity")

    @staticmethod
    def get_default_incident_partners() -> list[dict[str, str]]:
        """Return incident partners value."""
        return [
            {"Parvw": "ZG", "Parnr": "20530"},
            {"Parvw": "ZX", "Parnr": "F1015"},
            {"Parvw": "ZK", "Parnr": "TZ-101-00"},
        ]

    @CaptureSpan(MeasureType.CUSTOM.value)
    def get_long_text(self) -> list[dict[str, str]]:
        """Return long text value."""
        l_txt = ""
        for key, value in self.model_dump().items():
            if (
                key
                not in [
                    "action_class",
                    "Activity",
                    "ApiOrigin",
                    "EquipmentDescription",
                    "FlagMainOrderCreate",
                    "FlagMainOrderRelease",
                    "FlocId",
                    "main_service",
                    "main_sub_service",
                    "Open",
                    "Priok",
                    "Qmcod",
                    "run_procedure",
                    "Urgency",
                    "WorkCenter",
                ]
                and value
            ):
                l_txt += f"{key}: {value} \n"

        return [{"Tdobject": "QMEL", "Tdname": "", "Tdspras": "", "Ltxt": l_txt[:MAX_LONG_TEXT_LENGTH]}]

    @CaptureSpan(MeasureType.CUSTOM.value)
    def data_to_show(self) -> SapDataView:
        """Return sap data to show in icinga."""
        return SapDataView(**self.model_dump())

    @CaptureSpan(MeasureType.CUSTOM.value)
    def data_to_update(self) -> SapDataToUpdate:
        """Return sap data to update in icinga."""
        return SapDataToUpdate(**self.model_dump())

    @CaptureSpan(MeasureType.CUSTOM.value)
    def to_air(self) -> AirEntity:
        """Return an Air data to run procedure."""
        return AirEntity(**self.model_dump())


class SapEntityRelease(SapEntityCustomVars):
    """Represent a SAP Release entity based on SAP fields."""

    activity: str | None = Field(default=None, title="Activity", alias="Activity")
    activity_txt: str | None = Field(default=None, title="ActivityTxt", alias="ActivityTxt")
    activity_type: str | None = Field(default=None, title="ActivityType", alias="ActivityType")
    activity_type_txt: str | None = Field(default=None, title="ActivityTypeTxt", alias="ActivityTypeTxt")
    cost_center: str | None = Field(default=None, title="CostCenter", alias="CostCenter")
    cost_center_txt: str | None = Field(default=None, title="CostCenterTxt", alias="CostCenterTxt")
    create_date: str | None = Field(default=None, title="CreateDate", alias="CreateDate")
    default_track_end_date: str | None = Field(default=None, title="DefaultTrackEndDate", alias="DefaultTrackEndDate")
    default_track_start_date: str | None = Field(
        default=None, title="DefaultTrackStartDate", alias="DefaultTrackStartDate"
    )
    discipline: str | None = Field(default=None, title="Discipline", alias="Discipline")
    discipline_txt: str | None = Field(default=None, title="DisciplineTxt", alias="DisciplineTxt")
    equipment: str | None = Field(default=None, title="Equipment", alias="Equipment")
    equipment_desc: str | None = Field(default=None, title="EquipmentDesc", alias="EquipmentDesc")
    floc: str | None = Field(default=None, title="Floc", alias="Floc")
    floc_desc: str | None = Field(default=None, title="FlocDesc", alias="FlocDesc")
    id: str | None = Field(default=None, title="Id", alias="Id")
    identification: str | None = Field(default=None, title="Identification", alias="Identification")
    intervention_element: str | None = Field(default=None, title="InterventionElement", alias="InterventionElement")
    intervention_element_txt: str | None = Field(
        default=None, title="InterventionElementTxt", alias="InterventionElementTxt"
    )
    is_template: bool | None = Field(default=None, title="IsTemplate", alias="IsTemplate")
    location: str | None = Field(default=None, title="Location", alias="Location")
    location_txt: str | None = Field(default=None, title="LocationTxt", alias="LocationTxt")
    long_text: str | None = Field(default=None, title="LongText", alias="LongText")
    long_text_new: str | None = Field(default=None, title="LongTextNew", alias="LongTextNew")
    network: str | None = Field(default=None, title="Network", alias="Network")
    network_txt: str | None = Field(default=None, title="NetworkTxt", alias="NetworkTxt")
    planned_end_date: str | None = Field(default=None, title="PlannedEndDate", alias="PlannedEndDate")
    planned_start_date: str | None = Field(default=None, title="PlannedStartDate", alias="PlannedStartDate")
    priority: str | None = Field(default=None, title="Priority", alias="Priority")
    priority_txt: str | None = Field(default=None, title="PriorityTxt", alias="PriorityTxt")
    release_risk: str | None = Field(default=None, title="ReleaseRisk", alias="ReleaseRisk")
    release_risk_txt: str | None = Field(default=None, title="ReleaseRiskTxt", alias="ReleaseRiskTxt")
    required_end_date: str | None = Field(default=None, title="RequiredEndDate", alias="RequiredEndDate")
    required_start_date: str | None = Field(default=None, title="RequiredStartDate", alias="RequiredStartDate")
    search: str | None = Field(default=None, title="Search", alias="Search")
    selection_mode: str | None = Field(default=None, title="SelectionMode", alias="SelectionMode")
    service: str | None = Field(default=None, title="Service", alias="Service")
    service_txt: str | None = Field(default=None, title="ServiceTxt", alias="ServiceTxt")
    short_text: str | None = Field(default=None, title="ShortText", alias="ShortText")
    subservice: str | None = Field(default=None, title="Subservice", alias="Subservice")
    subservice_text: str | None = Field(default=None, title="SubserviceText", alias="SubserviceText")
    template_doc_category: str | None = Field(default=None, title="TemplateDocCategory", alias="TemplateDocCategory")
    template_doc_number: str | None = Field(default=None, title="TemplateDocNumber", alias="TemplateDocNumber")
    template_doc_type: str | None = Field(default=None, title="TemplateDocType", alias="TemplateDocType")
    urgency: str = Field(default="3", title="Urgency", description="", alias="Urgency")
    urgency_text: str | None = Field(default=None, title="UrgencyText", alias="UrgencyText")
    user_status: str | None = Field(default=None, title="UserStatus", alias="UserStatus")
    user_status_text: str | None = Field(default=None, title="UserStatusText", alias="UserStatusText")
    virtual_status: str | None = Field(default=None, title="VirtualStatus", alias="VirtualStatus")
    virtual_status_short_text: str | None = Field(
        default=None, title="VirtualStatusShortText", alias="VirtualStatusShortText"
    )
    virtual_status_text: str | None = Field(default=None, title="VirtualStatusText", alias="VirtualStatusText")
    wbs: str | None = Field(default=None, title="Wbs", alias="Wbs")
    wbs_txt: str | None = Field(default=None, title="WbsTxt", alias="WbsTxt")
    to_partners: dict | None = Field(default=None, title="ToPartners", description="", alias="toPartners")  # noqa: N815
    to_sub_orders: dict | None = Field(default=None, title="ToSubOrders", description="", alias="toSubOrders")  # noqa: N815
    to_tot_log: dict | None = Field(default=None, title="ToTotLog", description="", alias="toTotLog")  # noqa: N815
    to_originated_by: dict | None = Field(default=None, title="ToOriginateBy", description="", alias="toOriginatedBy")  # noqa: N815
    to_history: dict | None = Field(default=None, title="ToHistory", description="", alias="toHistory")  # noqa: N815
    to_change_indicator: dict | None = Field(
        default=None, title="ToChangeIndicator", description="", alias="toChangeIndicator"
    )  # noqa: N815
    to_attachments: dict | None = Field(default=None, title="ToAttachments", description="", alias="toAttachments")  # noqa: N815
    to_incident_partners: list[dict] | None = Field(
        default=None, title="ToIncidentPartners", description="", alias="toIncidentPartners"
    )  # noqa: N815
    to_long_text: list[dict] | None = Field(default=None, title="ToLongText", description="", alias="toLongText")  # noqa: N815

    @CaptureSpan(MeasureType.CUSTOM.value)
    def data_to_show(self) -> SapDataView:
        """Return sap data to show in icinga."""
        return SapDataView(**self.model_dump())

    @CaptureSpan(MeasureType.CUSTOM.value)
    def data_to_update(self) -> SapDataToUpdate:
        """Return sap data to update in icinga."""
        return SapDataToUpdate(**self.model_dump())

    @CaptureSpan(MeasureType.CUSTOM.value)
    def to_air(self) -> AirEntity:
        """Return an Air data to run procedure."""
        return AirEntity(**self.model_dump())

    def model_post_init(self, __context) -> None:
        """Validate the model."""
        if self.long_text:
            self.long_text = self.long_text[:MAX_LONG_TEXT_LENGTH]


class AlarmReleaseLink(Model):
    """Represent a link between an alarm and a release."""

    alarm_id: int = Field(default=0)
    release_number: int = Field(default=0)
    external_release_id: str | None = Field(default=None)
    comment: str | None = Field(default=None)
    user_id: str | None = Field(default=None)
    user_comment: str | None = Field(default=None)
    metric_type: str | None = Field(default=None)
    metric_name: str | None = Field(default=None)


class UserActionAdd(Model):
    """Represent the addition of user action."""

    alarm_id: int
    action_type: str
    user_id: str | None = None
    user_comment: str | None = None
    is_first_ack: bool = False


class AlarmUserEntity(Model):
    """Represent the payload for the "alarm/clear" endpoint."""

    user_id: str = Field(default="")
    alarm_id: int = Field(default=0)


class AlarmsUserEntity(Model):
    """Represent the payload for the "alarm/clear/multiple" endpoint."""

    user_id: str = Field(default="")
    alarm_ids: list[int] = Field(default=[])
