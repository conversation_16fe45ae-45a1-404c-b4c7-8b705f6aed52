"""Detail design implementation for mon-occ."""

import datetime
import json

import pandas as pd
from dateutil.parser import parse
from dateutil.tz import gettz

from mon_occ import patterns
from mon_occ.config import config
from mon_occ.lookup_tables import OccMetrics, occ_lookup_metrics
from olympus_common import enums, utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Run the occ details design script."""
    if config.debug:
        pd.set_option("display.max_columns", None)

    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable(row: pd.Series) -> bool:
    """Indicate if an alarm is actionable or not."""
    if _is_heartbeat(row) | (
        _severity(row) == enums.Severity.INDETERMINATE.value and _event_type(row) != enums.AlarmType.RESOLUTION.value
    ):
        return False
    elif row["ci_type"] in {
        "Automatic Storage Management",
        "Cluster Database",
        "Database Instance",
        "Host",
        "Oracle Reports Server",
        "Oracle Application Server",
        "Oracle HTTP Server",
        "Pluggable Database",
        "Web Cache",
        "MySQL Instance",
    }:
        return True
    elif row["ci_type"] in ["Oracle Weblogic Server", "Application Deployment"]:
        if _ci_id(row) is None:
            # "Alarm wrongfully formatted: the CI_ID cannot be extracted."
            return False
        else:
            return True
    else:
        # "Alarm not part of Phase 1 or scope:"
        return False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_data(row: pd.Series) -> str:
    """Indicate in which platform the server runs. This data is extracted from the event."""
    ci_name: str = row["ci"]
    # Use optimized patterns from patterns module
    instance, version, env = patterns.extract_ci_name_parts(ci_name)
    return json.dumps(
        {
            "platform": f"{row['additional_info']} / {row['event_platform']}",
            "instance": instance,
            "version": version,
            "env": env,
        }
    )


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _is_heartbeat(row: pd.Series) -> bool:
    return row["event_summary"] == "HEARTBEAT"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Indicate the CI ID."""
    if _is_heartbeat(row):
        return "OCC_HB"

    return row["ci"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type() -> str:
    """Indicate the way the clear is made."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_id(row: pd.Series) -> str:
    """Indicate the event ID."""
    return str(row["event_id"]) if row["event_id"] else "N/A"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Name of the module that handle the DD."""
    return "mon-occ"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _key2(row: pd.Series) -> str:
    """Represent the key2 allows to find metrics or element monitored name of the alarm."""
    ci_type = row["ci_type"]
    monitored_object = row["monitored_object"]
    friendly_metric_type = row["friendly_metric_type"]
    additional_info = row["additional_info"]
    metric_type = row["metric_type"]
    if ci_type in ["Database Instance", "Cluster Database", "Pluggable Database", "Agent"]:
        if patterns.USER_DEFINED_METRIC_PATTERN.search(friendly_metric_type):
            return monitored_object.split("=", maxsplit=1)[1]
        elif metric_type == "Availability" or additional_info.startswith("ME$"):
            return additional_info.replace(" ", "").replace("(%)", "")
        else:
            return friendly_metric_type.split("=", maxsplit=1)[1].replace(" ", "").replace("(%)", "")
    else:
        match ci_type:
            case "Host":
                if any(
                    rule in additional_info
                    for rule in ["ME$A1183_DQMonitorRule", "ME$A1183_CacheRule", "ME$A1183_RCSSchedulerRule"]
                ):
                    return patterns.substitute_rule_pattern(additional_info)
            case "MySQL Instance":
                return additional_info.split(":", maxsplit=1)[1]
            case _:
                return additional_info
    return "N/A"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _key(row: pd.Series) -> str:
    """Represent the key allows to find metrics or element monitored name of the alarm."""
    ci_type = row["ci_type"]
    if ci_type in ["Database Instance", "Cluster Database", "Pluggable Database", "Agent", "MySQL Instance"]:
        return ci_type
    else:
        return f"{ci_type} - {_key2(row)}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type(row: pd.Series) -> str:
    """Indicate the alarm metrics."""
    if _is_heartbeat(row):
        return "/ApplicationEvent/"

    occ_metrics = OccMetrics(**occ_lookup_metrics)
    return occ_metrics.get_metrics(_key(row))


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Indicate the monitored element name of the alarm."""
    if _is_heartbeat(row):
        return "Heartbeat"

    occ_metrics = OccMetrics(**occ_lookup_metrics)
    metric_name = occ_metrics.get_element_monitored_name(_key(row))
    return _key2(row) if metric_name == "*" else metric_name


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Indicate the Hostname or the IP value of the Hostname e.g:'bdiraiorlc012'."""
    host: str = row["host"]
    if patterns.is_ipv4_address(host):
        return host
    elif host == "N/A":
        return host
    else:
        return host.split(".")[0]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str:
    """Indicate the Hostname or the IP value of the Hostname."""
    return _node(row)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _platform(row: pd.Series) -> str:
    """Indicate in which platform the server runs."""
    if platform_info := patterns.extract_platform_info(row["event_platform"]):
        operating_system, platform = platform_info
        platform_ = f"{operating_system} {platform}"
        return platform_.split(",", maxsplit=1)[0]
    else:
        return "Platform not found"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime.datetime | None:
    """First date and time when the problem alarm was generated (timestamp sent by EMS)."""
    if row["timestamp"] and row["timestamp"] != "N/A":
        tzinfos = {
            "BRST": -7200,
            "CST": gettz("America/Chicago"),
            "CET": gettz("Europe/Brussels"),
            "CEST": gettz("Europe/Paris"),
            "MEST": gettz("Europe/Paris"),
        }
        parsed_datetime = parse(row["timestamp"], tzinfos=tzinfos)
        # convert `parsed_datetime` to utc and make naive
        return parsed_datetime.astimezone(datetime.timezone.utc).replace(tzinfo=None)
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope_ref() -> str:
    """Represent the MD group that is responsible for handling that alarm."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Represent the alarm summary."""
    if _is_heartbeat(row):
        return "OCC Heartbeat Message"
    elif _actionable(row) is False:
        if (
            row["ci_type"] in ("Oracle WebLogic Server", "Application Deployment")
            and "CI_ID=" not in row["event_platform"]
        ):
            return f"Alarm wrongfully formatted: the CI_ID cannot be extracted. {row['event_summary']}"
        return f"Alarm not part of phase 1 or scope: {row['event_summary']}"
    else:
        return row["event_summary"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Indicate the alarm severity."""
    if _is_heartbeat(row):
        return enums.Severity.INDETERMINATE.value
    else:
        match row["clearlevel_severity"]:
            case "Clear" | "Advisory":
                return enums.Severity.INDETERMINATE.value
            case "Warning":
                return enums.Severity.WARNING.value
            case _:
                return enums.Severity.CRITICAL.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_type(row: pd.Series) -> str:
    """Indicate if the alarm is a clear or not."""
    if _is_heartbeat(row):
        return enums.AlarmType.HEARTBEAT.value

    if row["clearlevel_severity"] in ["Clear", "Advisory"]:
        return enums.AlarmType.RESOLUTION.value

    return enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level(row: pd.Series) -> str | None:
    """Indicate if the alarm is a clear or not."""
    if _is_heartbeat(row):
        return "A1053"

    return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime.datetime | None:
    """Indicate the clear time of the alarm."""
    return _raise_time(row) if _event_type(row) == enums.AlarmType.RESOLUTION.value else None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time() -> datetime.datetime:
    """Determine the handle time, i.e. the time when the alarm is handled by Olympus."""
    return utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime.datetime | None:
    return _raise_time(row)


# ============================
# Main code section
# ============================
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df_records: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    df_records.fillna("N/A", inplace=True)

    # Hardcoded fields
    df_records["agent_id"] = agent_id
    df_records["clear_type"] = _clear_type()
    df_records["manager"] = _manager()
    df_records["action_class"] = _scope_ref()
    df_records["handle_time"] = _handle_time()

    # Computed fields
    df_records["actionable"] = df_records.apply(_actionable, axis=1)
    df_records["ci_id"] = df_records.apply(_ci_id, axis=1)
    df_records["additional_data"] = df_records.apply(_additional_data, axis=1)
    df_records["event_id"] = df_records.apply(_event_id, axis=1)
    df_records["node"] = df_records.apply(_node, axis=1)
    df_records["metric_name"] = df_records.apply(_metric_name, axis=1)
    df_records["metric_type"] = df_records.apply(_metric_type, axis=1)
    df_records["node_alias"] = df_records.apply(_node_alias, axis=1)
    df_records["platform"] = df_records.apply(_platform, axis=1)
    df_records["raise_time"] = df_records.apply(_raise_time, axis=1)
    df_records["summary"] = df_records.apply(_summary, axis=1)
    df_records["severity"] = df_records.apply(_severity, axis=1)
    df_records["event_type"] = df_records.apply(_event_type, axis=1)
    df_records["clear_time"] = df_records.apply(_clear_time, axis=1)
    df_records["wake_up_time"] = df_records.apply(_wake_up_time, axis=1)
    df_records["top_level"] = df_records.apply(_top_level, axis=1)

    return df_records
