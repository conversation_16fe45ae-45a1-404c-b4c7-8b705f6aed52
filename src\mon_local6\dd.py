"""Details Design module for LOCAL6.

Note: this module handles many different agents, listed here sorted by estimated frequency of messages:
- HA
- local6
- Websphere-MQ
- DCMS
- SCADA
- AIRCO
- FTP
"""

import json
from datetime import datetime, timedelta

import pandas as pd

from mon_local6 import utils
from mon_local6.config import config
from olympus_common import enums
from olympus_common import pd as olympus_pd
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


def filter_by_agent(df: pd.DataFrame) -> pd.DataFrame:
    """Filter the dataframe by agent."""
    agent_function_mapping = {
        29: utils.is_ha_message_vectorized,  # HA
        10: utils.is_local6_message_vectorized,  # local6
        27: utils.is_mq_message_vectorized,  # WebSphere-MQ
        30: utils.is_dcms_message_vectorized,  # DCMS
        19: utils.is_scada_message_vectorized,  # SCADA
        33: utils.is_airco_message_vectorized,  # AIRCO
        31: utils.is_ftp_message_vectorized,  # FTP
    }
    fn = agent_function_mapping[config.agent_id]
    mask = fn(df)
    return df.loc[mask].reset_index(drop=True)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm(row: pd.Series) -> bool | None:
    """Return if the event is actionable."""
    if len(row["tags"]) > 0:
        return False
    elif _severity(row) == enums.Severity.INDETERMINATE.value and _clear_level(row) != enums.AlarmType.RESOLUTION.value:
        return False
    else:
        return None  # need enrichment


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Give the ci_id of the event."""
    if row["ci_id"] == "mailgw_hb":
        return "a1617-mail_gw-prod"
    elif utils.is_scada_heartbeat(row):
        return "SCADA_HB"
    elif utils.is_mq_heartbeat(row):
        # Hard-coding is necessary as long as we require the heartbeat occurrence CI to be equal to the alarm CI.
        match row["ci_id"]:
            case "PROXY-DMZINF4-DRP-PRD":
                return "iictbimwlv008"
            case "PROXY-DMZINF4-MUI-PRD":
                return "iictzimwlv008"
            case "PROXY-ITECH-DRP-PRD":
                return "iictbimwlv014"
            case "PROXY-ITECH-MUI-PRD":
                return "iictyimwlv014"
            case _:
                return row["ci_id"]
    elif (
        "OPTIC_" in row["channel"]
        or utils.is_ha_heartbeat(row)
        or utils.is_mq_message(row)
        or utils.is_dcms_heartbeat(row)
        or utils.is_airco_message(row)
        or utils.is_local6_heartbeat(row)
        or utils.is_ftp_message(row)
    ):
        return row["ci_id"]
    elif row["channel"]:
        return row["channel"]
    else:
        return row["ci_id"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type(row: pd.Series) -> str:
    """Give the clear_type for this DD module."""
    return (
        enums.ClearType.MANUALLY.value
        if (utils.is_dcms_heartbeat(row) or utils.is_scada_heartbeat(row))
        else enums.ClearType.AUTOMATICALLY.value
    )


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _extended_attr(row: pd.Series) -> str | None:
    """Return the extended attributes for the event."""
    tag: str = row["tag"]
    if utils.is_dcms_message(row):
        if tag.startswith("A2103-"):
            return json.dumps({"collection": "A2103_IICTXIAPWV013"})
        elif tag.startswith("A2137-"):
            return json.dumps({"collection": "A2137_IICTXIAPWV013"})
        else:
            return None
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _delay(row: pd.Series) -> int:
    """Return the delay in seconds for the event."""
    return 300 if _metric_name(row) in ["F_MSK_ERR", "MSK_ERR", "MSK_NOK", "PMSK_CYC"] else 0


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Give the manager for the DD module."""
    return "mon-local6"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Give the node of the event."""
    hostname: str = row["hostname"]
    return (
        hostname.split(".", maxsplit=1)[0]
        if (
            utils.is_mq_message(row)
            or utils.is_dcms_message(row)
            or utils.is_scada_message(row)
            or utils.is_ftp_message(row)
        )
        else hostname
    )


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str:
    """Give the node_alias of the event.

    Notes
    -----
    For this Details Design this is the same as node.
    """
    return _node(row)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _platform(row: pd.Series) -> str:
    """Give the platform of the event."""
    return row["environment"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime:
    """Give the time of the event in the EMS (not the one in rsyslog) in epoch format."""
    str_timestamp: str = row["event_timestamp"]
    timestamp = datetime.strptime(str_timestamp.split(".")[0], "%Y-%m-%dT%H:%M:%S")
    return timestamp


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime | None:
    """Give the clear time of the event in the EMS (not the one in rsyslog) in epoch format."""
    str_timestamp: str = row["event_timestamp"]
    timestamp: datetime = datetime.strptime(str_timestamp.split(".")[0], "%Y-%m-%dT%H:%M:%S")
    return timestamp if _clear_level(row) == enums.AlarmType.RESOLUTION.value else None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope(row: pd.Series) -> str:
    """Give the scope of the event."""
    if utils.is_dcms_message(row) or utils.is_scada_message(row) or utils.is_ftp_message(row):
        return enums.Scope.IT.value
    elif utils.is_airco_message(row):
        return enums.Scope.TE.value
    else:
        return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Give the summary of the event."""
    if utils.is_ha_heartbeat(row):
        return f"HA {row['tag']} HeartBeat Message"
    elif utils.is_local6_heartbeat(row):
        return "HeartBeat Alarm"
    elif utils.is_mq_heartbeat(row):
        return f"Websphere-MQ probe on {_node(row)} :  {row['metric']} Heartbeat Message"
    elif utils.is_dcms_heartbeat(row):
        return "DCMS Heartbeat Message"
    elif utils.is_ftp_heartbeat(row):
        return "FTP Heartbeat Message"
    elif (
        utils.is_mq_message(row)
        or utils.is_dcms_message(row)
        or utils.is_scada_message(row)
        or utils.is_airco_message(row)
        or utils.is_ftp_message(row)
    ):
        return row["message"]
    elif "OPTIC_" in row["channel"]:
        if row["event_timestamp"]:
            return row["message"]
        else:
            return f"{row['optional_parameter']} {row['message']}"
    else:
        return row["message"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Give the metric_name relative to the event."""
    if (
        utils.is_ha_heartbeat(row)
        or utils.is_local6_heartbeat(row)
        or utils.is_mq_heartbeat(row)
        or utils.is_dcms_heartbeat(row)
        or utils.is_scada_heartbeat(row)
        or utils.is_ftp_heartbeat(row)
    ):
        return "Heartbeat"
    elif (
        "OPTIC_" in row["channel"]
        or utils.is_mq_message(row)
        or utils.is_dcms_message(row)
        or utils.is_scada_message(row)
        or utils.is_airco_message(row)
        or utils.is_ftp_message(row)
    ):
        # In principle, all the messages have a channel starting with OPTIC_, so we always fall in this case for
        # non-heartbeats.
        return row["metric"]
    elif row["metric"] and row["environment"]:
        return f"{row['metric']}_{row['environment']}"
    elif not row["metric"]:
        return row["environment"]
    else:
        return row["metric"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type(row: pd.Series) -> str:
    """Give the metric_type of the events."""
    if (
        utils.is_ha_heartbeat(row)
        or utils.is_local6_heartbeat(row)
        or utils.is_mq_heartbeat(row)
        or utils.is_dcms_heartbeat(row)
        or utils.is_scada_heartbeat(row)
        or utils.is_ftp_heartbeat(row)
    ):
        return "/ApplicationEvent/"
    elif utils.is_mq_message(row):
        syslog_ci_id: str = row["ci_id"]
        if syslog_ci_id.startswith("MQ_"):
            return "/MQ_QueueAvailability/"
        elif syslog_ci_id.startswith("VERNEMQ_"):
            return "/VerneMQAvailibility/"
        elif syslog_ci_id.startswith("PROXY_"):
            return "/NginxAvailability/"
        else:
            return "/MQ_ManagerAvailability/"
    elif utils.is_dcms_message(row):
        return "/ApplicationEvent/"
    elif utils.is_scada_message(row):
        return "/HardwareEvent/"
    else:
        return "/SysLogEvent/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level(row: pd.Series) -> str | None:
    """Give the top level relative to the event."""
    if utils.is_scada_message(row):
        return "A1724"
    elif utils.is_airco_message(row):
        return "A1574"
    elif utils.is_ftp_message(row):
        return "A1315"
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_level(row: pd.Series) -> str:
    """Give the status of the event (RESOLUTION or PROBLEM)."""
    if (
        utils.is_ha_heartbeat(row)
        or utils.is_local6_heartbeat(row)
        or utils.is_mq_heartbeat(row)
        or utils.is_dcms_heartbeat(row)
        or utils.is_scada_heartbeat(row)
        or utils.is_ftp_heartbeat(row)
    ):
        return enums.AlarmType.HEARTBEAT.value
    elif row["syslog_severity"] == "info":
        return enums.AlarmType.RESOLUTION.value
    else:
        return enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Give the severity of the event."""
    if (
        utils.is_ha_heartbeat(row)
        or utils.is_mq_heartbeat(row)
        or utils.is_dcms_heartbeat(row)
        or utils.is_scada_heartbeat(row)
        or utils.is_ftp_heartbeat(row)
        or utils.is_local6_heartbeat(row)
    ):
        return enums.Severity.INDETERMINATE.value

    match row["syslog_severity"]:
        case "info":
            return enums.Severity.CLEARED.value
        case "notice":
            return enums.Severity.WARNING.value
        case "warning":
            return enums.Severity.MINOR.value
        case "err" | "error":
            return enums.Severity.MAJOR.value
        case "crit" | "critical":
            return enums.Severity.CRITICAL.value
        case _:
            return enums.Severity.INDETERMINATE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime:
    """Return the wake up time for all events in the DD."""
    delay = _delay(row)
    return _raise_time(row) + timedelta(0, delay)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time() -> datetime:
    """Return the handle time for the event in the DD."""
    return olympus_utils.now_naive()


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Run the local6 Details Design."""
    df = _transform(df, agent_id)
    return df


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Perform all the transformation needed for the Details Design.

    Parameters
    ----------
    df : pd.DataFrame
        The dataframe created from the "Reader" (the raw data).
    agent_id_dict : dict[str, int]
        The dictionary containing the agent name as key and the agent id as value.

    Returns
    -------
    pd.DataFrame
        The dataframe containing all fields needed for the alarm. This dataframe is not cleaned from unused field since
        this is done in the "Writer".
    """
    # The field "tags" is needed for _actionable_alarm function so we add this column to the mapping dict if it exists.
    # TODO: Check with OCO if tags and optional.parameter are still required. In November 2023 none of the 196674 rows
    #       on kafka have these fields.
    df = filter_by_agent(df)
    if df.empty:
        return df
    if "tags" not in df.columns:
        df = olympus_pd.check_field_in_df(df, "tags")
    df.loc[:, "tags"] = df["tags"].apply(lambda tags: [] if pd.isna(tags) else tags)

    df["optional_parameter"] = df["optional_parameter"].replace({"": "[]"})
    # Hardcoded fields
    df["manager"] = _manager()
    df["handle_time"] = _handle_time()
    df["agent_id"] = agent_id

    # Computed fields
    df["actionable"] = df.apply(_actionable_alarm, axis=1)
    df["ci_id"] = df.apply(_ci_id, axis=1)
    df["event_type"] = df.apply(_clear_level, axis=1)
    df["clear_type"] = df.apply(_clear_type, axis=1)
    df["additional_data"] = df.apply(_extended_attr, axis=1)
    df["metric_name"] = df.apply(_metric_name, axis=1)
    df["metric_type"] = df.apply(_metric_type, axis=1)
    df["node"] = df.apply(_node, axis=1)
    df["node_alias"] = df.apply(_node_alias, axis=1)
    df["platform"] = df.apply(_platform, axis=1)
    df["raise_time"] = df.apply(_raise_time, axis=1)
    df["clear_time"] = df.apply(_clear_time, axis=1)
    df["wake_up_time"] = df.apply(_wake_up_time, axis=1)
    df["action_class"] = df.apply(_scope, axis=1)
    df["severity"] = df.apply(_severity, axis=1)
    df["summary"] = df.apply(_summary, axis=1)
    df["top_level"] = df.apply(_top_level, axis=1)

    return df


def _exclude_pem_messages(df: pd.DataFrame) -> pd.DataFrame:
    """Exclude messages coming from PEM as they are handled in the service mon-pem.

    Notes
    -----
    We filter the df directly with a condition rather than calling apply(utils.is_pem_message) because the latter
    would be slower and the condition can be modified here.
    """
    return df[df["tag"] != "PEM"]
