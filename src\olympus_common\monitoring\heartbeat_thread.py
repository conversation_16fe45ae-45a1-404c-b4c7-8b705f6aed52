"""Thread-based heartbeat implementation for monitoring."""

import logging
import threading
import time
from typing import Callable

logger = logging.getLogger(__name__)


class HeartbeatThread(threading.Thread):
    """Thread that sends heartbeats at regular intervals."""

    def __init__(
        self, heartbeat_function: Callable[[], None], interval_seconds: int = 10, name: str = "heartbeat-thread"
    ):
        """Initialize the heartbeat thread.

        Parameters
        ----------
            heartbeat_function: Function to call to send heartbeat
            interval_seconds: Interval between heartbeats in seconds
            name: Name of the thread
        """
        super().__init__(name=name, daemon=True)
        self._heartbeat_function = heartbeat_function
        self._interval_seconds = interval_seconds
        self._stop_event = threading.Event()
        self._paused = False
        self._pause_lock = threading.Lock()

    def run(self):
        """Run the heartbeat thread.

        Notes
        -----
        This function is called when thread.start() is invoked.
        """
        logger.info(f"Starting heartbeat thread with interval {self._interval_seconds}s")
        while not self._stop_event.is_set():
            try:
                self._call_heartbeat_function()

                for _ in range(self._interval_seconds):
                    if self._stop_event.is_set():
                        break  # Exit if stop event is set
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Error in heartbeat thread: {e}")
                time.sleep(1)

        logger.info("Heartbeat thread stopped")

    def _call_heartbeat_function(self):
        with self._pause_lock:
            if self._paused:
                return
            self._heartbeat_function()
            logger.debug("Heartbeat sent")

    def stop(self):
        """Stop the heartbeat thread."""
        self._stop_event.set()
        if self.is_alive():
            self.join(timeout=5)  # Give thread 5 seconds to shut down

    def pause(self):
        """Pause sending heartbeats."""
        with self._pause_lock:
            self._paused = True
            logger.debug("Heartbeat thread paused")

    def resume(self):
        """Resume sending heartbeats."""
        with self._pause_lock:
            self._paused = False
            logger.debug("Heartbeat thread resumed")
