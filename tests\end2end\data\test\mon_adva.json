{"extra_env": {"OLYMPUS_AGENT_ID": "1"}, "agent_ids": [1], "data": [{"input": {"message": "#<SNMP::SNMPv2_Trap:0x3210418a @varbind_list=[#<SNMP::VarBind:0x3c02301c @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x615446c2 @value=18276441>>, #<SNMP::VarBind:0x51785773 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x22c836ac @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0xf677039 @value=0>>, #<SNMP::VarBind:0x4c465541 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x5f964075 @value=1>>, #<SNMP::VarBind:0x3c08707d @name=[*******.4.1.2544.********.1.3], @value=\"HEART-BEAT\">, #<SNMP::VarBind:0x85a297d @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x7648896a @value=5>>, #<SNMP::VarBind:0x1bb0fa3e @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x6444eaad @value=1>>, #<SNMP::VarBind:0x3d0c3699 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0xf3deb8b @value=2>>, #<SNMP::VarBind:0x13c08158 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x5092000f @value=2>>, #<SNMP::VarBind:0xc765c7a @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x6cf79fcc @value=9999>>, #<SNMP::VarBind:0x7baa194b @name=[*******.4.1.2544.********.1.9], @value=\"HICTAIAPWV014\">, #<SNMP::VarBind:0x3b78ec49 @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x5d3e2d0 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x550a0459 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x7ec12ff2 @value=0>>, #<SNMP::VarBind:0x2a78ec7 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x18783921 @value=0>>, #<SNMP::VarBind:0x1d55cebb @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE9\\x02\\x05\\n\\x16\\x16\\x01\">, #<SNMP::VarBind:0x6a1cdc5c @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE9\\x02\\x05\\n\\x16\\x16\\x01\">, #<SNMP::VarBind:0x5473b66e @name=[*******.4.1.2544.********.1.16], @value=\"FSP-NM Heart Beat\">, #<SNMP::VarBind:0xd9b2cf0 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x3e880bee @value=1>>, #<SNMP::VarBind:0x32fade5f @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x7b73dea8 @value=2>>, #<SNMP::VarBind:0x2fdb819a @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x51f228f4 @value=0>>, #<SNMP::VarBind:0x351e1cb3 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x507d0226 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x23599ee2 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x63aca7f5 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x2692d4b7 @value=2>>, #<SNMP::VarBind:0x1ec69e71 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=769235768, @error_index=0, @error_status=0, @source_ip=\"*************\">", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "@timestamp": "2025-02-05T09:22:38.644324447Z", "host": "*************", "type": "snmp_trap", "SNMPv2-MIB::sysUpTime.0": "2 days, 02:46:04.41", "@version": "1", "snmptrap.neTime": "\u0007é\u0002\u0005\n\u0016\u0016\u0001", "snmptrap.summary": "FSP-NM Heart Beat", "snmptrap.nelpAdress": "*************", "snmptrap.mtosiNeType": "", "snmptrap.location": "0", "snmptrap.name": "HEART-BEAT", "snmptrap.update": "1", "snmptrap.neName": "HICTAIAPWV014", "snmptrap.customerName": "", "snmptrap.direction": "0", "snmptrap.comment": "", "snmptrap.severity": "1", "snmptrap.impairment": "2", "snmptrap.neType": "9999", "snmptrap.acknowledged": "2", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.id": "0", "snmptrap.description": "1", "snmptrap.event_type": "5", "snmptrap.entity": "", "snmptrap.corrRef": "", "snmptrap.security": "2", "snmptrap.nmsTime": "\u0007é\u0002\u0005\n\u0016\u0016\u0001", "event.uuid": "65a44f29-b027-41be-b559-d481db2f0a17", "event.kafka.key": null, "event.kafka.partition": 1, "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.offset": 489662, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.timestamp": "2025-02-05T09:22:38.757Z", "event.logstash.instance_name": "iictniapls015"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "ADVA_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "FSP-NM Heart Beat", "severity": 1, "raise_time": "2025-02-05 09:22:22"}]}}, {"input": {"message": "#<SNMP::SNMPv2_Trap:0x6bdb38fd @varbind_list=[#<SNMP::VarBind:0x450d6b59 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x2fbe982e @value=18391962>>, #<SNMP::VarBind:0x1d9041f4 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x57ddd651 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x79a42850 @value=1883162>>, #<SNMP::VarBind:0x2280b0a2 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x3d00cae7 @value=2>>, #<SNMP::VarBind:0x4d6fc48 @name=[*******.4.1.2544.********.1.3], @value=\"T-OPR-LT\">, #<SNMP::VarBind:0x5c68a22a @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x7b4a1ca5 @value=1>>, #<SNMP::VarBind:0x4026a6d9 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x73213629 @value=4>>, #<SNMP::VarBind:0x6ac1a395 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x41c5355c @value=2>>, #<SNMP::VarBind:0x342053d3 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x4e4abb00 @value=2>>, #<SNMP::VarBind:0x29f07457 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x4177dbe0 @value=100>>, #<SNMP::VarBind:0x77ffa6d6 @name=[*******.4.1.2544.********.1.9], @value=\"adblock11\">, #<SNMP::VarBind:0x6cf68192 @name=[*******.4.1.2544.********.1.10], @value=\"***********\">, #<SNMP::VarBind:0x468e0297 @name=[*******.4.1.2544.********.1.11], @value=\"CH-1-6-C1\">, #<SNMP::VarBind:0x39d9697d @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x339ab186 @value=0>>, #<SNMP::VarBind:0x6b384c76 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x5d14713c @value=4>>, #<SNMP::VarBind:0x2f746962 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE9\\x02\\x05\\n)%\\x03\">, #<SNMP::VarBind:0x4904eb8b @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE9\\x02\\x05\\n)2\\x03\">, #<SNMP::VarBind:0x60765521 @name=[*******.4.1.2544.********.1.16], @value=\"Optical Power Rx Low\">, #<SNMP::VarBind:0x1e154582 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x1226005b @value=2>>, #<SNMP::VarBind:0x7c90e927 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x2e1d74c9 @value=2>>, #<SNMP::VarBind:0x1a900ddd @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x3896f20c @value=0>>, #<SNMP::VarBind:0x4334fde8 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x6ca727b @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x1cae699a @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x1327ee91 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x77188aa0 @value=2>>, #<SNMP::VarBind:0x22146792 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=769235772, @error_index=0, @error_status=0, @source_ip=\"*************\">", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "@timestamp": "2025-02-05T09:41:53.922693961Z", "host": "*************", "type": "snmp_trap", "SNMPv2-MIB::sysUpTime.0": "2 days, 03:05:19.62", "@version": "1", "snmptrap.neTime": "\u0007é\u0002\u0005\n)2\u0003", "snmptrap.summary": "Optical Power Rx Low", "snmptrap.nelpAdress": "***********", "snmptrap.mtosiNeType": "", "snmptrap.location": "0", "snmptrap.name": "T-OPR-LT", "snmptrap.update": "2", "snmptrap.neName": "adblock11", "snmptrap.customerName": "", "snmptrap.direction": "4", "snmptrap.comment": "", "snmptrap.severity": "4", "snmptrap.impairment": "2", "snmptrap.neType": "100", "snmptrap.acknowledged": "2", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.id": "1883162", "snmptrap.description": "2", "snmptrap.event_type": "1", "snmptrap.entity": "CH-1-6-C1", "snmptrap.corrRef": "", "snmptrap.security": "2", "snmptrap.nmsTime": "\u0007é\u0002\u0005\n)%\u0003", "event.uuid": "715c5831-4de1-4052-8dc2-0487a36c4bc4", "event.kafka.key": null, "event.kafka.partition": 2, "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.offset": 487150, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.timestamp": "2025-02-05T09:41:54.023Z", "event.logstash.instance_name": "iictniapls016"}, "output": {"s2110_alarm": [{"ci_id": "adblock11", "metric_name": "adblock11", "metric_type": "T-OPR-LT", "event_id": "1883162", "actionable": true}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "adblock11", "metric_name": "adblock11", "metric_type": "T-OPR-LT", "severity": 3, "summary": "Optical Power Rx Low", "raise_time": "2025-02-05 09:41:50"}]}}, {"input": {"message": "#<SNMP::SNMPv2_Trap:0x2a987da0 @varbind_list=[#<SNMP::VarBind:0x774ab29 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x724f2904 @value=18544343>>, #<SNMP::VarBind:0x1b532f90 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x7a5c9e62 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x58535fe3 @value=1883163>>, #<SNMP::VarBind:0x535f5ae4 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x51f2d0b0 @value=2>>, #<SNMP::VarBind:0x4fc3eb62 @name=[*******.4.1.2544.********.1.3], @value=\"S-NEBAK\">, #<SNMP::VarBind:0xdad093e @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x248cea15 @value=5>>, #<SNMP::VarBind:0x63bcc964 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x3374a68a @value=5>>, #<SNMP::VarBind:0x5675bd23 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x15ea39ba @value=2>>, #<SNMP::VarBind:0x1098358a @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x64e81e5 @value=2>>, #<SNMP::VarBind:0x7da9a8aa @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x394a794c @value=100>>, #<SNMP::VarBind:0x68af2f8a @name=[*******.4.1.2544.********.1.9], @value=\"HICTAIAPWV014\">, #<SNMP::VarBind:0x1a1cdad3 @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x2a40cdc2 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x40aa41df @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x51e8f53f @value=0>>, #<SNMP::VarBind:0x2dde7793 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x15c9137 @value=0>>, #<SNMP::VarBind:0x136a8c7a @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE9\\x02\\x05\\v\\a\\x01\\x01\">, #<SNMP::VarBind:0x53dd225 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE9\\x02\\x05\\v\\a\\x01\\x01\">, #<SNMP::VarBind:0x5b014ffe @name=[*******.4.1.2544.********.1.16], @value=\"Backup of configuration for network element adblock11 has been initiated.\">, #<SNMP::VarBind:0x3e31a880 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x79fffa60 @value=2>>, #<SNMP::VarBind:0x1238b380 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x43976086 @value=2>>, #<SNMP::VarBind:0x7051b5dc @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x767b7d4c @value=0>>, #<SNMP::VarBind:0x218874b0 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x59e3a357 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x246102e2 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x9dd8a3f @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x6b125240 @value=1>>, #<SNMP::VarBind:0x2fafad6e @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=769235778, @error_index=0, @error_status=0, @source_ip=\"*************\">", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "@timestamp": "2025-02-05T10:07:17.481134017Z", "host": "*************", "type": "snmp_trap", "SNMPv2-MIB::sysUpTime.0": "2 days, 03:30:43.43", "@version": "1", "snmptrap.neTime": "\u0007é\u0002\u0005\u000b\u0007\u0001\u0001", "snmptrap.summary": "Backup of configuration for network element adblock11 has been initiated.", "snmptrap.nelpAdress": "*************", "snmptrap.mtosiNeType": "", "snmptrap.location": "0", "snmptrap.name": "S-NEBAK", "snmptrap.update": "2", "snmptrap.neName": "HICTAIAPWV014", "snmptrap.customerName": "", "snmptrap.direction": "0", "snmptrap.comment": "", "snmptrap.severity": "5", "snmptrap.impairment": "2", "snmptrap.neType": "100", "snmptrap.acknowledged": "2", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.id": "1883163", "snmptrap.description": "2", "snmptrap.event_type": "5", "snmptrap.entity": "", "snmptrap.corrRef": "", "snmptrap.security": "1", "snmptrap.nmsTime": "\u0007é\u0002\u0005\u000b\u0007\u0001\u0001", "event.uuid": "96552b18-eb4b-42d1-90a3-f83826e6c656", "event.kafka.key": null, "event.kafka.partition": 1, "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.offset": 489668, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.timestamp": "2025-02-05T10:07:17.583Z", "event.logstash.instance_name": "iictniapls015"}, "output": {"s2110_alarm": [{"ci_id": "HICTAIAPWV014", "metric_name": "HICTAIAPWV014", "metric_type": "S-NEBAK", "event_id": "1883163", "actionable": true}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "HICTAIAPWV014", "metric_name": "HICTAIAPWV014", "metric_type": "S-NEBAK", "severity": 2, "summary": "Backup of configuration for network element adblock11 has been initiated."}]}}, {"input": {"message": "#<SNMP::SNMPv2_Trap:0x669c3258 @varbind_list=[#<SNMP::VarBind:0x4d73f6c6 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x60487d87 @value=18544406>>, #<SNMP::VarBind:0x67c19d24 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x57f0b461 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x567bd9f @value=1883164>>, #<SNMP::VarBind:0x26605cf1 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x4d23dda4 @value=2>>, #<SNMP::VarBind:0x1fa22bdd @name=[*******.4.1.2544.********.1.3], @value=\"CPY-MEM-PRTL\">, #<SNMP::VarBind:0x2711e6f3 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x332b134 @value=3>>, #<SNMP::VarBind:0x502f920b @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x5edda710 @value=1>>, #<SNMP::VarBind:0x7653f446 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x60fe40a9 @value=2>>, #<SNMP::VarBind:0x4c6d2977 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x7f3bf2cd @value=2>>, #<SNMP::VarBind:0x16a3c8a8 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x44a39dd @value=100>>, #<SNMP::VarBind:0x21c98eb2 @name=[*******.4.1.2544.********.1.9], @value=\"adblock11\">, #<SNMP::VarBind:0x1c4addc @name=[*******.4.1.2544.********.1.10], @value=\"***********\">, #<SNMP::VarBind:0x92ac3c4 @name=[*******.4.1.2544.********.1.11], @value=\"SRV-UBR\">, #<SNMP::VarBind:0x5cfb97ed @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x31aa3ede @value=0>>, #<SNMP::VarBind:0x1524cf7a @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x4a4f0af9 @value=0>>, #<SNMP::VarBind:0x4a8ba8a2 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE9\\x02\\x05\\v\\a\\x01\\a\">, #<SNMP::VarBind:0x2a3733e3 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE9\\x02\\x05\\v\\a\\x11\\a\">, #<SNMP::VarBind:0x19db66c0 @name=[*******.4.1.2544.********.1.16], @value=\"Local copy file started\">, #<SNMP::VarBind:0x2b71bfc0 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x3ca8dcb4 @value=2>>, #<SNMP::VarBind:0x2f1348e9 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x6d3cd6ad @value=2>>, #<SNMP::VarBind:0x4b95eb75 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x3122d3cf @value=0>>, #<SNMP::VarBind:0x4439102 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0xf41b699 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x6b6b3a47 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x4c70304e @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x54237343 @value=2>>, #<SNMP::VarBind:0x56d4e220 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=769235779, @error_index=0, @error_status=0, @source_ip=\"*************\">", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "@timestamp": "2025-02-05T10:07:18.100753445Z", "host": "*************", "type": "snmp_trap", "SNMPv2-MIB::sysUpTime.0": "2 days, 03:30:44.06", "@version": "1", "snmptrap.neTime": "\u0007é\u0002\u0005\u000b\u0007\u0011\u0007", "snmptrap.summary": "Local copy file started", "snmptrap.nelpAdress": "***********", "snmptrap.mtosiNeType": "", "snmptrap.location": "0", "snmptrap.name": "CPY-MEM-PRTL", "snmptrap.update": "2", "snmptrap.neName": "adblock11", "snmptrap.customerName": "", "snmptrap.direction": "0", "snmptrap.comment": "", "snmptrap.severity": "1", "snmptrap.impairment": "2", "snmptrap.neType": "100", "snmptrap.acknowledged": "2", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.id": "1883164", "snmptrap.description": "2", "snmptrap.event_type": "3", "snmptrap.entity": "SRV-UBR", "snmptrap.corrRef": "", "snmptrap.security": "2", "snmptrap.nmsTime": "\u0007é\u0002\u0005\u000b\u0007\u0001\u0007", "event.uuid": "9a3f375f-7e70-409e-b6ef-1d199562edd8", "event.kafka.key": null, "event.kafka.partition": 1, "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.offset": 489669, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.timestamp": "2025-02-05T10:07:18.204Z", "event.logstash.instance_name": "iictniapls015"}, "output": {"s2110_alarm": [{"ci_id": "adblock11", "metric_name": "adblock11", "metric_type": "CPY-MEM-PRTL", "event_id": "1883164", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "adblock11", "metric_name": "adblock11", "metric_type": "CPY-MEM-PRTL", "severity": 1, "summary": "Local copy file started"}]}}, {"input": {"message": "#<SNMP::SNMPv2_Trap:0x5c76d713 @varbind_list=[#<SNMP::VarBind:0x3778f487 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x8409f8e @value=19261709>>, #<SNMP::VarBind:0x62e41fda @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x1ab61f3a @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x6a1a0c27 @value=1883171>>, #<SNMP::VarBind:0x66aa5205 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x24f28bc5 @value=2>>, #<SNMP::VarBind:0xe170fae @name=[*******.4.1.2544.********.1.3], @value=\"T-OPR-LT\">, #<SNMP::VarBind:0x34d6517f @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0xd8179a @value=2>>, #<SNMP::VarBind:0x30c418e7 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x6981b2e @value=4>>, #<SNMP::VarBind:0x43ff2b @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x40e75c7e @value=2>>, #<SNMP::VarBind:0x507467a5 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x167347ee @value=2>>, #<SNMP::VarBind:0x9d658ac @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x7328f144 @value=100>>, #<SNMP::VarBind:0x92e9cb @name=[*******.4.1.2544.********.1.9], @value=\"adblock11\">, #<SNMP::VarBind:0xc529200 @name=[*******.4.1.2544.********.1.10], @value=\"***********\">, #<SNMP::VarBind:0x37faa81e @name=[*******.4.1.2544.********.1.11], @value=\"CH-1-6-C1\">, #<SNMP::VarBind:0x3ecdd74 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x14a7a073 @value=0>>, #<SNMP::VarBind:0xc7df275 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x6e2511bc @value=4>>, #<SNMP::VarBind:0xbc77671 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE9\\x02\\x05\\r\\x06\\\"\\b\">, #<SNMP::VarBind:0x224aeb42 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE9\\x02\\x05\\r\\x06)\\b\">, #<SNMP::VarBind:0x41475b24 @name=[*******.4.1.2544.********.1.16], @value=\"CLEARED: Optical Power Rx Low\">, #<SNMP::VarBind:0x7291a41 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x376d902b @value=2>>, #<SNMP::VarBind:0x42087d7f @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x396d9061 @value=2>>, #<SNMP::VarBind:0x76d96e1f @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x2d6e5427 @value=0>>, #<SNMP::VarBind:0x2c8c2770 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x79b74418 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x442374a7 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x1b33df68 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x5649dcd9 @value=2>>, #<SNMP::VarBind:0x151dc58c @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=769235810, @error_index=0, @error_status=0, @source_ip=\"*************\">", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "@timestamp": "2025-02-05T12:06:51.522465252Z", "host": "*************", "type": "snmp_trap", "SNMPv2-MIB::sysUpTime.0": "2 days, 05:30:17.09", "@version": "1", "snmptrap.neTime": "\u0007é\u0002\u0005\r\u0006)\b", "snmptrap.summary": "CLEARED: Optical Power Rx Low", "snmptrap.nelpAdress": "***********", "snmptrap.mtosiNeType": "", "snmptrap.location": "0", "snmptrap.name": "T-OPR-LT", "snmptrap.update": "2", "snmptrap.neName": "adblock11", "snmptrap.customerName": "", "snmptrap.direction": "4", "snmptrap.comment": "", "snmptrap.severity": "4", "snmptrap.impairment": "2", "snmptrap.neType": "100", "snmptrap.acknowledged": "2", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.id": "1883171", "snmptrap.description": "2", "snmptrap.event_type": "2", "snmptrap.entity": "CH-1-6-C1", "snmptrap.corrRef": "", "snmptrap.security": "2", "snmptrap.nmsTime": "\u0007é\u0002\u0005\r\u0006\"\b", "event.uuid": "4ca8da92-ca54-4deb-a6b8-ee50ffc9797f", "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.offset": 489175, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.timestamp": "2025-02-05T12:06:51.626Z", "event.logstash.instance_name": "iictmiapls016"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "adblock11", "metric_name": "adblock11", "metric_type": "T-OPR-LT", "severity": 0, "summary": "CLEARED: Optical Power Rx Low", "raise_time": "2025-02-05 12:06:41", "clear_time": "2025-02-05 12:06:41"}]}}, {"input": {"message": "#<SNMP::SNMPv2_Trap:0x661f93cc @varbind_list=[#<SNMP::VarBind:0xd831a38 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x15e8f953 @value=20118260>>, #<SNMP::VarBind:0xf7389d1 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x163ddf12 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x3fa5537 @value=1883181>>, #<SNMP::VarBind:0x518a067a @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x4224209b @value=2>>, #<SNMP::VarBind:0x3a19a282 @name=[*******.4.1.2544.********.1.3], @value=\"DCN\">, #<SNMP::VarBind:0x6b1b0b1b @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x6a2379d4 @value=1>>, #<SNMP::VarBind:0x960acf3 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x7e1a12ef @value=3>>, #<SNMP::VarBind:0x4d6a8de4 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x3cf6b044 @value=2>>, #<SNMP::VarBind:0x30fd841d @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x1ede6587 @value=2>>, #<SNMP::VarBind:0x7c2de15 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0xe3f2a39 @value=100>>, #<SNMP::VarBind:0x523e9a8f @name=[*******.4.1.2544.********.1.9], @value=\"admechl22\">, #<SNMP::VarBind:0x3fa12f12 @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x2e027c85 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x599bf59e @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x7f57e6c0 @value=0>>, #<SNMP::VarBind:0x49389f87 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x74e32471 @value=0>>, #<SNMP::VarBind:0x72d21876 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE9\\x02\\x05\\x0F\\x1D\\x14\\x03\">, #<SNMP::VarBind:0x32f3910a @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE9\\x02\\x05\\x0F\\x1D\\x14\\x03\">, #<SNMP::VarBind:0x3fbaf474 @name=[*******.4.1.2544.********.1.16], @value=\"Network element does not respond to SNMP requests\">, #<SNMP::VarBind:0x52178f5e @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x30e42cc2 @value=2>>, #<SNMP::VarBind:0x1e319015 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x7640cef3 @value=2>>, #<SNMP::VarBind:0x1843c21a @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x9d06fec @value=0>>, #<SNMP::VarBind:0x1b73f7fb @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x6bb48530 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x6d61c94e @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x67c47401 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x630c8b2d @value=2>>, #<SNMP::VarBind:0x2c5e0f79 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=769235849, @error_index=0, @error_status=0, @source_ip=\"*************\">", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "@timestamp": "2025-02-05T14:29:36.979299531Z", "host": "*************", "type": "snmp_trap", "SNMPv2-MIB::sysUpTime.0": "2 days, 07:53:02.60", "@version": "1", "snmptrap.neTime": "\u0007é\u0002\u0005\u000f\u001d\u0014\u0003", "snmptrap.summary": "Network element does not respond to SNMP requests", "snmptrap.nelpAdress": "*************", "snmptrap.mtosiNeType": "", "snmptrap.location": "0", "snmptrap.name": "DCN", "snmptrap.update": "2", "snmptrap.neName": "admechl22", "snmptrap.customerName": "", "snmptrap.direction": "0", "snmptrap.comment": "", "snmptrap.severity": "3", "snmptrap.impairment": "2", "snmptrap.neType": "100", "snmptrap.acknowledged": "2", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.id": "1883181", "snmptrap.description": "2", "snmptrap.event_type": "1", "snmptrap.entity": "", "snmptrap.corrRef": "", "snmptrap.security": "2", "snmptrap.nmsTime": "\u0007é\u0002\u0005\u000f\u001d\u0014\u0003", "event.uuid": "db615837-18d0-45ae-9978-7a77807bb5bc", "event.kafka.key": null, "event.kafka.partition": 2, "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.offset": 487163, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.timestamp": "2025-02-05T14:29:37.083Z", "event.logstash.instance_name": "iictniapls016"}, "output": {"s2110_alarm": [{"ci_id": "admechl22", "metric_name": "admechl22", "metric_type": "DCN", "event_id": "1883181", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "admechl22", "metric_name": "admechl22", "metric_type": "DCN", "severity": 4, "summary": "Network element does not respond to SNMP requests"}]}}, {"input": {"message": "#<SNMP::SNMPv2_Trap:0x593bf8d9 @varbind_list=[#<SNMP::VarBind:0x2042ecb5 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x1be68c95 @value=48634814>>, #<SNMP::VarBind:0x1d9d5200 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0xc370848 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x3b276a3f @value=1883558>>, #<SNMP::VarBind:0x140acbea @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x63f08639 @value=2>>, #<SNMP::VarBind:0x5731b747 @name=[*******.4.1.2544.********.1.3], @value=\"RCVCLK\">, #<SNMP::VarBind:0x5747490c @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x15d09d95 @value=1>>, #<SNMP::VarBind:0x72f61058 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x28270ace @value=2>>, #<SNMP::VarBind:0x45c539ef @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x546838f7 @value=1>>, #<SNMP::VarBind:0x165e86f2 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x6bf9f8d @value=2>>, #<SNMP::VarBind:0x6ad9d4f4 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x7fe3f6d8 @value=100>>, #<SNMP::VarBind:0x1de896c0 @name=[*******.4.1.2544.********.1.9], @value=\"adbertr11\">, #<SNMP::VarBind:0x67513107 @name=[*******.4.1.2544.********.1.10], @value=\"************\">, #<SNMP::VarBind:0x438b376d @name=[*******.4.1.2544.********.1.11], @value=\"CH-1-9-NW\">, #<SNMP::VarBind:0x233e4cf2 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x7ab980c3 @value=0>>, #<SNMP::VarBind:0x148e4f9a @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0xe242239 @value=4>>, #<SNMP::VarBind:0x5facb6d3 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE9\\x02\\b\\x16*\\x05\\b\">, #<SNMP::VarBind:0x69259a7d @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE9\\x02\\b\\x16*\\x17\\b\">, #<SNMP::VarBind:0xfb1fa5c @name=[*******.4.1.2544.********.1.16], @value=\"Rx Clock-Loss of Sync\">, #<SNMP::VarBind:0x202b2479 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x1a0b0481 @value=2>>, #<SNMP::VarBind:0x5fb893cf @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x5f5bf17e @value=2>>, #<SNMP::VarBind:0x26aa1e00 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x64472958 @value=0>>, #<SNMP::VarBind:0x7f7ae8f4 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x7f2a3664 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x9866d9f @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x54d6881e @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x7c43f738 @value=2>>, #<SNMP::VarBind:0x527467c2 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=769237176, @error_index=0, @error_status=0, @source_ip=\"*************\">", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "@timestamp": "2025-02-08T21:42:27.633157512Z", "host": "*************", "type": "snmp_trap", "SNMPv2-MIB::sysUpTime.0": "5 days, 15:05:48.14", "@version": "1", "snmptrap.neTime": "\u0007é\u0002\b\u0016*\u0017\b", "snmptrap.summary": "Rx Clock-Loss of Sync", "snmptrap.nelpAdress": "************", "snmptrap.mtosiNeType": "", "snmptrap.location": "0", "snmptrap.name": "RCVCLK", "snmptrap.update": "2", "snmptrap.neName": "adbertr11", "snmptrap.customerName": "", "snmptrap.direction": "4", "snmptrap.comment": "", "snmptrap.severity": "2", "snmptrap.impairment": "1", "snmptrap.neType": "100", "snmptrap.acknowledged": "2", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.id": "1883558", "snmptrap.description": "2", "snmptrap.event_type": "1", "snmptrap.entity": "CH-1-9-NW", "snmptrap.corrRef": "", "snmptrap.security": "2", "snmptrap.nmsTime": "\u0007é\u0002\b\u0016*\u0005\b", "event.uuid": "825fbec7-7492-4487-846c-ec4431ff39e7", "event.kafka.key": null, "event.kafka.partition": 2, "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.offset": 487639, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.timestamp": "2025-02-08T21:42:27.793Z", "event.logstash.instance_name": "iictniapls016"}, "output": {"s2110_alarm": [{"ci_id": "adbertr11", "metric_name": "adbertr11", "metric_type": "RCVCLK", "event_id": "1883558", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "adbertr11", "metric_name": "adbertr11", "metric_type": "RCVCLK", "severity": 5, "summary": "Rx Clock-Loss of Sync"}]}}]}