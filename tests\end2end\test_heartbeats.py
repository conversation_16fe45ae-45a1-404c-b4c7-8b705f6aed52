import time

import pytest

from olympus_common.db import Occurrence
from olympus_common.enums import AlarmType
from olympus_common.monitoring.monitoring_client import ScriptMonitoringClient
from olympus_common.monitoring.monitoring_manager import MonitoringManager
from tests.end2end.utils import create_test_session


@pytest.mark.e2e
@pytest.mark.parametrize(
    ("app_name", "interval", "block_time", "expected_heartbeats"), [("a", 3, 7, 3), ("c", 3, 10, 4)]
)
def test_heartbeats(app_name, interval, block_time, expected_heartbeats):
    """Test that heartbeats are sent correctly.

    Notes
    -----
    app_name is required to avoid getting all heartbeats from the database.

    For 3, 7, 3: We expect 3 heartbeats to be sent:
    - First heartbeat at t=0
    - Second heartbeat at t=3
    - Third heartbeat at t=6
    - Fourth heartbeat at t=9 (but we only block for 7 seconds, so it won't be recorded)

    For 3, 10, 4: We expect 4 heartbeats to be sent:
    - First heartbeat at t=0
    - Second heartbeat at t=3
    - Third heartbeat at t=6
    - Fourth heartbeat at t=9
    - Fifth heartbeat at t=12 (but we only block for 10 seconds, so it won't be recorded)
    """

    session = create_test_session()
    monitoring_client = ScriptMonitoringClient(application_name=app_name)
    monitoring_manager = MonitoringManager(monitoring_client=monitoring_client, heartbeat_interval_seconds=interval)

    monitoring_manager.start()

    # Block main thread for a `block_time` seconds to allow heartbeats to be sent
    time.sleep(block_time)

    monitoring_manager.stop()
    # Check if the heartbeat was recorded
    heartbeats = (
        session.query(Occurrence)
        .filter(
            Occurrence.event_type == AlarmType.HEARTBEAT.value,
            Occurrence.metric_name == "mon-heartbeat",
            Occurrence.summary == f"{app_name} Heartbeat Message",
        )
        .all()
    )
    assert len(heartbeats) == expected_heartbeats, (
        f"A wrong number of heartbeats were recorded: {len(heartbeats)=} vs {expected_heartbeats=}"
    )
