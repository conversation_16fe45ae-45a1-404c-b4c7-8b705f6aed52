"""Represent the lookup tables module for the mon-datalines."""

import logging
from dataclasses import dataclass
from typing import List


@dataclass
class Trap:
    """Class to represent traps send by Telindus Datalines."""

    TrapDescription: str
    Severity: int
    Responsible: str
    Description: str


@dataclass
class DatalinesTrap:
    """Represent different traps that will be received from Dataline EMS."""

    TrapList: List[Trap]

    def get_severity(self, trap_description: str) -> int:
        """Get severity."""
        return self._get(trap_description).Severity

    @staticmethod
    def extract_metric_name(trap_description: str) -> str:
        """Get metric name from the trap description."""
        return trap_description.split(".")[-1].split(" ")[0]

    def get_responsible(self, trap_description: str) -> str:
        """Get reponsible."""
        return self._get(trap_description).Responsible

    def get_description(self, trap_description: str) -> str:
        """Get description."""
        return self._get(trap_description).Description

    def is_actionable_alarm(self, trap_description: str) -> int:
        """Get if alarm is actionable."""
        for trap in self.TrapList:
            if trap.TrapDescription == trap_description:
                return 1
        self._log_warning_description(trap_description)
        return 2

    @staticmethod
    def _log_warning_description(trap_description: str) -> None:
        """Log warning message whenever the trap description does not match with the trap list description."""
        logging.warning(f"Can't find {trap_description} in Trap description list!")

    def _get(self, trap_description: str) -> Trap:
        """Get specific trap in the trap list."""
        for trap in self.TrapList:
            if trap.TrapDescription == trap_description:
                return trap
        self._log_warning_description(trap_description)
        return Trap("N/A", 0, "N/A", "N/A")


# Datalines alarm list
alarm_list = [
    Trap("aster5.alarmInfo.currentAlarms.alarmSyncLoss", 5, "Szone", "the internal alarm buffer overflows."),
    Trap("aster5.alarmInfo.currentAlarms.notResponding", 5, "Szone", "Not responding on the polling of the Orchid."),
    Trap(
        "aster5.alarmInfo.currentAlarms.powerSupply1CvFail",
        4,
        "Szone",
        "the upper and/of lower power supply in the CN4 card nest is not present or fails.",
    ),
    Trap(
        "aster5.alarmInfo.currentAlarms.powerSupply2CvFail",
        4,
        "Szone",
        "the upper and/of lower power supply in the CN4 card nest is not present or fails.",
    ),
    Trap(
        "aster5.alarmInfo.currentAlarms.unknownState",
        1,
        "ICT331",
        "each time a new Aster 5 is added to the network and before the Orchid 1035 has completed a first successful "
        "polling session.",
    ),
    Trap(
        "aster5/line.alarmInfo.currentAlarms.linkDown",
        5,
        "Szone",
        "when no data link towards the remote Aster 5 is present.",
    ),
    Trap("aster5/line.alarmInfo.currentAlarms.remotePower", 4, "Szone", "when the remote Aster 5 is not powered."),
    Trap(
        "aster5/line.alarmInfo.currentAlarms.retrain",
        3,
        "Szone",
        "retrain/reinitialization caused by bad line quality.",
    ),
    Trap(
        "aster5/line.alarmInfo.currentAlarms.RoundTripDelay",
        5,
        "ICT331",
        "too much time between sending a signal and receiving its acknowledgment.",
    ),
    Trap("aster5/line.alarmInfo.currentAlarms.signalQuality", 2, "Szone", "Bad quality of the line."),
    Trap("aster5/line.alarmInfo.currentAlarms.test", 3, "Szone", "when a test is active."),
    Trap("aster5/v24.alarmInfo.currentAlarms.linkDown", 4, "Szone", "when no DTE is connected to the DTE interface."),
    Trap(
        "telindus1035.alarmInfo.currentAlarms.alarmSyncLoss",
        5,
        "Szone",
        "when a ping is pending (for example, an indefinite ping).",
    ),
    Trap("telindus1035.alarmInfo.currentAlarms.coldBoot", 2, "ICT331", ""),
    Trap("telindus1035.alarmInfo.currentAlarms.warmBoot", 2, "ICT331", ""),
]

datalines_alarm_list = DatalinesTrap(alarm_list)
