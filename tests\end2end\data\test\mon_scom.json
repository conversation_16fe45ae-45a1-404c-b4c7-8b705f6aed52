{"extra_env": {"OLYMPUS_AGENT_ID": "20"}, "agent_ids": [20], "data": [{"input": {"DisplayString": "A868 HeartBeat connection check Infrabel to Tivoli", "Category": "<PERSON><PERSON>", "ClassId": "300e3747-b192-b4d7-d318-13b6ae89b694", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.PropertyBagData\" time=\"2025-02-18T05:54:12.0803264+01:00\" sourceHealthServiceId=\"65B90021-414C-2011-8F3B-5A9F869EF344\"><Property Name=\"MyScriptPropertyName\" VariantType=\"8\">AlertCondition</Property></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "A868 HeartBeat connection check Infrabel to Tivoli  :: A868_HeartBeat_Infrabel_To_Tivoli :: {D985E24B-98BC-5219-DE5C-CA4FD37C39C8}", "Id": "5381d896-45a8-4f7d-848e-7d58b36e15f1", "IsMonitorAlert": "False", "LastModified": "18/02/2025 04:54:12", "LastModifiedBy": "System", "LastModifiedByNonConnector": "18/02/2025 04:54:12", "MaintenanceModeLastModified": "30/01/2025 10:41:29", "MonitoringClassId": "300e3747-b192-b4d7-d318-13b6ae89b694", "MonitoringObjectDisplayName": "A868_HeartBeat_Infrabel_To_Tivoli", "MonitoringObjectFullName": "A868_HeartBeat_Infrabel_To_Tivoli_Connection:IICTCIAPWV434.msnet.railb.be;A868_HeartBeat_Infrabel_To_Tivoli", "MonitoringObjectHealthState": "Uninitialized", "MonitoringObjectId": "d985e24b-98bc-5219-de5c-ca4fd37c39c8", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "A868_HeartBeat_Infrabel_To_Tivoli", "MonitoringObjectPath": "IICTCIAPWV434.msnet.railb.be", "MonitoringRuleId": "d3252b29-56ca-9771-f773-88b72bf8c30f", "Name": "A868 HeartBeat connection check Infrabel to Tivoli", "NetbiosComputerName": "IICTCIAPWV434", "NetbiosDomainName": "MSNET", "Owner": null, "Parameters": "", "PrincipalName": "IICTCIAPWV434.msnet.railb.be", "Priority": "Normal", "ProblemId": "570a7a80-a2ac-f663-797a-d11d2fdaeacd", "RepeatCount": "21486", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "d3252b29-56ca-9771-f773-88b72bf8c30f", "Severity": "Information", "SiteName": null, "StateLastModified": "30/01/2025 10:42:31", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "05/12/2024 10:04:37", "TimeRaised": "05/12/2024 10:04:37", "TimeResolutionStateLastModified": "05/12/2024 10:04:37", "TimeResolved": "", "UnformattedDescription": "A868 HeartBeat connection check Infrabel to Tivoli  :: A868_HeartBeat_Infrabel_To_Tivoli :: {D985E24B-98BC-5219-DE5C-CA4FD37C39C8}"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "IICTCIAPWV434", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "A868 HeartBeat connection check Infrabel to Tivoli - A868 HeartBeat connection check Infrabel to Tivoli  :: A868_HeartBeat_Infrabel_To_Tivoli :: {D985E24B-98BC-5219-DE5C-CA4FD37C39C8} / ", "clear_time": null, "severity": 1, "raise_time": "2025-02-18T04:54:12", "additional_data": null}]}}, {"input": {"DisplayString": "Application Partition Discovery failed because permission was denied to the LDAP instance", "Category": "EventCollection", "ClassId": "ec628057-7830-b72d-1ca6-2a4b437a5479", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"Microsoft.Windows.EventData\" time=\"2025-02-13T02:47:00.2372046+01:00\" sourceHealthServiceId=\"53848AAB-E4E3-0642-2685-5A75F5CC6F3F\"><EventOriginId>{AFB2B30C-32E0-49BA-98FF-99E72EB86973}</EventOriginId><PublisherId>{02AA29A4-F25D-7425-D22B-5F010DF0C108}</PublisherId><PublisherName>Health Service Script</PublisherName><EventSourceName>Health Service Script</EventSourceName><Channel>Operations Manager</Channel><LoggingComputer>IICTCISMWV088.msnet.railb.be</LoggingComputer><EventNumber>98</EventNumber><EventCategory>0</EventCategory><EventLevel>1</EventLevel><UserName>N/A</UserName><RawDescription><![CDATA[%1 : %2\r\n]]></RawDescription><LCID>2057</LCID><Params><Param>LDS Application Partition Discovery</Param><Param><![CDATA[The script 'ApplicationPartitionDiscovery.vbs' failed to get the FSMO owner from 'LDAP://localhost:389/CN=NTDS Settings,CN=IICTCISMWV088$VMwareVDMDS,CN=Servers,CN=Default-First-Site-Name,CN=Sites,CN=Configuration,CN={7347053C-9B74-48DA-A377-74CA812A2BD7}'. \r\nThe error returned was: 'There is no such object on the server.' (0x80072030). This script was executed as msnet\\sys_a1183-adldsmon S-1-5-21-**********-**********-*********-6784713.]]></Param></Params><EventData><DataItem type=\"System.XmlData\" time=\"2025-02-13T02:47:00.2372046+01:00\" sourceHealthServiceId=\"53848AAB-E4E3-0642-2685-5A75F5CC6F3F\"><EventData xmlns=\"http://schemas.microsoft.com/win/2004/08/events/event\"><Data>LDS Application Partition Discovery</Data><Data>The script 'ApplicationPartitionDiscovery.vbs' failed to get the FSMO owner from 'LDAP://localhost:389/CN=NTDS Settings,CN=IICTCISMWV088$VMwareVDMDS,CN=Servers,CN=Default-First-Site-Name,CN=Sites,CN=Configuration,CN={7347053C-9B74-48DA-A377-74CA812A2BD7}'. \r\nThe error returned was: 'There is no such object on the server.' (0x80072030). This script was executed as msnet\\sys_a1183-adldsmon S-1-5-21-**********-**********-*********-6784713.</Data></EventData></DataItem></EventData><EventDisplayNumber>98</EventDisplayNumber><EventDescription><![CDATA[LDS Application Partition Discovery : The script 'ApplicationPartitionDiscovery.vbs' failed to get the FSMO owner from 'LDAP://localhost:389/CN=NTDS Settings,CN=IICTCISMWV088$VMwareVDMDS,CN=Servers,CN=Default-First-Site-Name,CN=Sites,CN=Configuration,CN={7347053C-9B74-48DA-A377-74CA812A2BD7}'. \r\nThe error returned was: 'There is no such object on the server.' (0x80072030). This script was executed as msnet\\sys_a1183-adldsmon S-1-5-21-**********-**********-*********-6784713.\r\n]]></EventDescription><Keywords>*****************</Keywords></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The Application Partition Discovery script failed because the AD LDS instance could not be accessed over LDAP. The account used to access the AD LDS instance must have appropriate permissions to read the configuration information of the instance. Please refer to the AD LDS Management Pack Guide for information regarding configuring the action accounts.", "Id": "d9cf7c1a-a112-4a9c-9051-b40dd77fcca8", "IsMonitorAlert": "False", "LastModified": "13/02/2025 01:47:00", "LastModifiedBy": "System", "LastModifiedByNonConnector": "13/02/2025 01:47:00", "MaintenanceModeLastModified": "30/01/2025 10:46:29", "MonitoringClassId": "ec628057-7830-b72d-1ca6-2a4b437a5479", "MonitoringObjectDisplayName": "Microsoft.Windows.Server.LDS.InstanceRoleSeed", "MonitoringObjectFullName": "Microsoft.Windows.Server.LDS.InstanceRoleSeed:IICTCISMWV088.msnet.railb.be", "MonitoringObjectHealthState": "Uninitialized", "MonitoringObjectId": "0173ed72-3a05-c0ee-2862-2bc416e74d66", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": null, "MonitoringObjectPath": "IICTCISMWV088.msnet.railb.be", "MonitoringRuleId": "79af5814-7cdd-2375-0ef4-e4de5392b5cd", "Name": "Application Partition Discovery failed because permission was denied to the LDAP instance", "NetbiosComputerName": "IICTCISMWV088", "NetbiosDomainName": "MSNET", "Owner": "Health Service Script", "Parameters": "", "PrincipalName": "IICTCISMWV088.msnet.railb.be", "Priority": "High", "ProblemId": "79af5814-7cdd-2375-0ef4-e4de5392b5cd", "RepeatCount": "437", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "79af5814-7cdd-2375-0ef4-e4de5392b5cd", "Severity": "Error", "SiteName": null, "StateLastModified": "30/01/2025 10:47:14", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "03/12/2024 09:38:13", "TimeRaised": "03/12/2024 09:38:13", "TimeResolutionStateLastModified": "03/12/2024 09:38:13", "TimeResolved": "", "UnformattedDescription": "The Application Partition Discovery script failed because the AD LDS instance could not be accessed over LDAP. The account used to access the AD LDS instance must have appropriate permissions to read the configuration information of the instance. Please refer to the AD LDS Management Pack Guide for information regarding configuring the action accounts."}, "output": {}, "comment": "No records expected since both metric_name and metric_type are NA"}, {"input": {"DisplayString": "Many Corrupt or Unreadable Windows Events", "Category": "AvailabilityHealth", "ClassId": "ab4c891f-3359-3fb6-0704-075fbfe36710", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.ConsolidatorData\" time=\"2025-02-13T02:56:58.5318983+01:00\" sourceHealthServiceId=\"5F227665-461C-EDAA-2931-86B0A69E8963\"><TimeWindowStart>2025-02-13T02:55:55.5148784+01:00</TimeWindowStart><TimeWindowEnd>2025-02-13T03:00:55.5148783+01:00</TimeWindowEnd><TimeFirst>2025-02-13T02:55:55.5148784+01:00</TimeFirst><TimeLast>2025-02-13T02:56:58.5318983+01:00</TimeLast><Count>147</Count><Context><DataItem type=\"Microsoft.Windows.EventData\" time=\"2025-02-13T02:56:58.5318983+01:00\" sourceHealthServiceId=\"5F227665-461C-EDAA-2931-86B0A69E8963\"><EventOriginId>{A3EE5CA3-38DE-4BD1-8A1E-99460CAF9ECA}</EventOriginId><PublisherId>{2DFE7810-DE9F-785F-CD11-80E1C9EE8A67}</PublisherId><PublisherName>Health Service Modules</PublisherName><EventSourceName>Health Service Modules</EventSourceName><Channel>Operations Manager</Channel><LoggingComputer>IICTCIWWWV019.msnet.railb.be</LoggingComputer><EventNumber>26008</EventNumber><EventCategory>0</EventCategory><EventLevel>1</EventLevel><UserName>N/A</UserName><RawDescription><![CDATA[The %5 event log on computer '%8' is still corrupt. The Event Log Provider will attempt to recover by\r\nskipping over a possible bad record. The Provider may skip up to two records.\r\n%n%nOne or more workflows were affected by this. \r\n%n%nWorkflow name: %2\r\n%nInstance name: %3\r\n%nInstance ID: %4\r\n%nManagement group: %1\r\n]]></RawDescription><LCID>2057</LCID><Params><Param>Garda</Param><Param>Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3</Param><Param>Remote Desktop Web Access</Param><Param>{F64A72FD-2C4E-F165-C83B-D25C066D9D9F}</Param><Param>Application</Param><Param>0</Param><Param></Param><Param>IICTCIWWWV019.msnet.railb.be</Param><Param></Param></Params><EventData><DataItem type=\"System.XmlData\" time=\"2025-02-13T02:56:58.5318982+01:00\" sourceHealthServiceId=\"5F227665-461C-EDAA-2931-86B0A69E8963\"><EventData xmlns=\"http://schemas.microsoft.com/win/2004/08/events/event\"><Data>Garda</Data><Data>Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3</Data><Data>Remote Desktop Web Access</Data><Data>{F64A72FD-2C4E-F165-C83B-D25C066D9D9F}</Data><Data>Application</Data><Data>0</Data><Data></Data><Data>IICTCIWWWV019.msnet.railb.be</Data><Data></Data></EventData></DataItem></EventData><EventDisplayNumber>26008</EventDisplayNumber><EventDescription><![CDATA[The Application event log on computer 'IICTCIWWWV019.msnet.railb.be' is still corrupt. The Event Log Provider will attempt to recover by\r\nskipping over a possible bad record. The Provider may skip up to two records.\r\n\r\n\r\nOne or more workflows were affected by this. \r\n\r\n\r\nWorkflow name: Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3\r\n\r\nInstance name: Remote Desktop Web Access\r\n\r\nInstance ID: {F64A72FD-2C4E-F165-C83B-D25C066D9D9F}\r\n\r\nManagement group: Garda\r\n]]></EventDescription><Keywords>*****************</Keywords></DataItem></Context></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The Application event log on computer 'IICTCIWWWV019.msnet.railb.be' is still corrupt. The Event Log Provider will attempt to recover by\nskipping over a possible bad record. The Provider may skip up to two records.\n\n\nOne or more workflows were affected by this. \n\n\nWorkflow name: Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3\n\nInstance name: Remote Desktop Web Access\n\nInstance ID: {F64A72FD-2C4E-F165-C83B-D25C066D9D9F}\n\nManagement group: Garda\n", "Id": "e6e80f5c-9629-453f-be16-326a5663d3ba", "IsMonitorAlert": "True", "LastModified": "13/02/2025 01:58:05", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "13/02/2025 01:58:05", "MaintenanceModeLastModified": "27/01/2025 11:21:28", "MonitoringClassId": "ab4c891f-3359-3fb6-0704-075fbfe36710", "MonitoringObjectDisplayName": "IICTCIWWWV019.msnet.railb.be", "MonitoringObjectFullName": "Microsoft.SystemCenter.HealthService:IICTCIWWWV019.msnet.railb.be", "MonitoringObjectHealthState": "Warning", "MonitoringObjectId": "5f227665-461c-edaa-2931-86b0a69e8963", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": null, "MonitoringObjectPath": "IICTCIWWWV019.msnet.railb.be", "MonitoringRuleId": "e5aad154-6650-6499-ad47-7e32a81423b4", "Name": "Many Corrupt or Unreadable Windows Events", "NetbiosComputerName": "IICTCIWWWV019", "NetbiosDomainName": "MSNET", "Owner": null, "Parameters": "", "PrincipalName": "IICTCIWWWV019.msnet.railb.be", "Priority": "Low", "ProblemId": "e5aad154-6650-6499-ad47-7e32a81423b4", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "e5aad154-6650-6499-ad47-7e32a81423b4", "Severity": "Warning", "SiteName": null, "StateLastModified": "13/02/2025 01:57:01", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "13/02/2025 01:57:01", "TimeRaised": "13/02/2025 01:57:01", "TimeResolutionStateLastModified": "13/02/2025 01:57:01", "TimeResolved": "", "UnformattedDescription": "The Application event log on computer 'IICTCIWWWV019.msnet.railb.be' is still corrupt. The Event Log Provider will attempt to recover by\nskipping over a possible bad record. The Provider may skip up to two records.\n\n\nOne or more workflows were affected by this. \n\n\nWorkflow name: Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3\n\nInstance name: Remote Desktop Web Access\n\nInstance ID: {F64A72FD-2C4E-F165-C83B-D25C066D9D9F}\n\nManagement group: Garda\n"}, "output": {}, "comment": "No records expected since both metric_name and metric_type are NA"}, {"input": {"DisplayString": "Many Corrupt or Unreadable Windows Events", "Category": "AvailabilityHealth", "ClassId": "ab4c891f-3359-3fb6-0704-075fbfe36710", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"Microsoft.Windows.EventData\" time=\"2025-02-13T03:00:53.4655517+01:00\" sourceHealthServiceId=\"5F227665-461C-EDAA-2931-86B0A69E8963\"><EventOriginId>{AB0A751D-EFEE-4459-B53C-B80329BA11F1}</EventOriginId><PublisherId>{2DFE7810-DE9F-785F-CD11-80E1C9EE8A67}</PublisherId><PublisherName>Health Service Modules</PublisherName><EventSourceName>Health Service Modules</EventSourceName><Channel>Operations Manager</Channel><LoggingComputer>IICTCIWWWV019.msnet.railb.be</LoggingComputer><EventNumber>26005</EventNumber><EventCategory>0</EventCategory><EventLevel>0</EventLevel><UserName>N/A</UserName><RawDescription><![CDATA[The Windows Event Log Provider has resumed processing the %5 event log on computer '%8'\r\nafter recovering from errors.\r\n%n%nOne or more workflows were affected by this. \r\n%n%nWorkflow name: %2\r\n%nInstance name: %3\r\n%nInstance ID: %4\r\n%nManagement group: %1\r\n]]></RawDescription><LCID>2057</LCID><Params><Param>Garda</Param><Param>Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3</Param><Param>Remote Desktop Web Access</Param><Param>{F64A72FD-2C4E-F165-C83B-D25C066D9D9F}</Param><Param>Application</Param><Param></Param><Param></Param><Param>IICTCIWWWV019.msnet.railb.be</Param><Param></Param></Params><EventData><DataItem type=\"System.XmlData\" time=\"2025-02-13T03:00:53.4655517+01:00\" sourceHealthServiceId=\"5F227665-461C-EDAA-2931-86B0A69E8963\"><EventData xmlns=\"http://schemas.microsoft.com/win/2004/08/events/event\"><Data>Garda</Data><Data>Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3</Data><Data>Remote Desktop Web Access</Data><Data>{F64A72FD-2C4E-F165-C83B-D25C066D9D9F}</Data><Data>Application</Data><Data></Data><Data></Data><Data>IICTCIWWWV019.msnet.railb.be</Data><Data></Data></EventData></DataItem></EventData><EventDisplayNumber>26005</EventDisplayNumber><EventDescription><![CDATA[The Windows Event Log Provider has resumed processing the Application event log on computer 'IICTCIWWWV019.msnet.railb.be'\r\nafter recovering from errors.\r\n\r\n\r\nOne or more workflows were affected by this. \r\n\r\n\r\nWorkflow name: Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3\r\n\r\nInstance name: Remote Desktop Web Access\r\n\r\nInstance ID: {F64A72FD-2C4E-F165-C83B-D25C066D9D9F}\r\n\r\nManagement group: Garda\r\n]]></EventDescription><Keywords>*****************</Keywords></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The Application event log on computer 'IICTCIWWWV019.msnet.railb.be' is still corrupt. The Event Log Provider will attempt to recover by\nskipping over a possible bad record. The Provider may skip up to two records.\n\n\nOne or more workflows were affected by this. \n\n\nWorkflow name: Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3\n\nInstance name: Remote Desktop Web Access\n\nInstance ID: {F64A72FD-2C4E-F165-C83B-D25C066D9D9F}\n\nManagement group: Garda\n", "Id": "e6e80f5c-9629-453f-be16-326a5663d3ba", "IsMonitorAlert": "True", "LastModified": "13/02/2025 02:00:53", "LastModifiedBy": "System", "LastModifiedByNonConnector": "13/02/2025 02:00:53", "MaintenanceModeLastModified": "27/01/2025 11:21:28", "MonitoringClassId": "ab4c891f-3359-3fb6-0704-075fbfe36710", "MonitoringObjectDisplayName": "IICTCIWWWV019.msnet.railb.be", "MonitoringObjectFullName": "Microsoft.SystemCenter.HealthService:IICTCIWWWV019.msnet.railb.be", "MonitoringObjectHealthState": "Success", "MonitoringObjectId": "5f227665-461c-edaa-2931-86b0a69e8963", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": null, "MonitoringObjectPath": "IICTCIWWWV019.msnet.railb.be", "MonitoringRuleId": "e5aad154-6650-6499-ad47-7e32a81423b4", "Name": "Many Corrupt or Unreadable Windows Events", "NetbiosComputerName": "IICTCIWWWV019", "NetbiosDomainName": "MSNET", "Owner": null, "Parameters": "", "PrincipalName": "IICTCIWWWV019.msnet.railb.be", "Priority": "Low", "ProblemId": "e5aad154-6650-6499-ad47-7e32a81423b4", "RepeatCount": "0", "ResolutionState": "255", "ResolvedBy": "System", "RuleId": "e5aad154-6650-6499-ad47-7e32a81423b4", "Severity": "Warning", "SiteName": null, "StateLastModified": "13/02/2025 02:01:31", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "13/02/2025 01:57:01", "TimeRaised": "13/02/2025 01:57:01", "TimeResolutionStateLastModified": "13/02/2025 02:00:53", "TimeResolved": "13/02/2025 02:00:53", "UnformattedDescription": "The Application event log on computer 'IICTCIWWWV019.msnet.railb.be' is still corrupt. The Event Log Provider will attempt to recover by\nskipping over a possible bad record. The Provider may skip up to two records.\n\n\nOne or more workflows were affected by this. \n\n\nWorkflow name: Microsoft.Windows.Server.10.0.RemoteDesktopServices.NewUnitMonitor_3\n\nInstance name: Remote Desktop Web Access\n\nInstance ID: {F64A72FD-2C4E-F165-C83B-D25C066D9D9F}\n\nManagement group: Garda\n"}, "output": {}, "comment": "No records expected since both metric_name and metric_type are NA"}, {"input": {"DisplayString": "Power Shell Script failed to run", "Category": "<PERSON><PERSON>", "ClassId": "ab4c891f-3359-3fb6-0704-075fbfe36710", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"Microsoft.Windows.EventData\" time=\"2025-02-13T07:18:45.6367338+01:00\" sourceHealthServiceId=\"5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0\"><EventOriginId>{CB1EA5DB-C3CF-4694-9C44-7713AC9E0742}</EventOriginId><PublisherId>{2DFE7810-DE9F-785F-CD11-80E1C9EE8A67}</PublisherId><PublisherName>Health Service Modules</PublisherName><EventSourceName>Health Service Modules</EventSourceName><Channel>Operations Manager</Channel><LoggingComputer>IICTCISMWV205.msnet.railb.be</LoggingComputer><EventNumber>**********</EventNumber><EventCategory>0</EventCategory><EventLevel>1</EventLevel><UserName>N/A</UserName><RawDescription><![CDATA[The PowerShell script failed with below exception%n%n%7%n\r\n%n%nScript Name:%t%5\r\n%n%nOne or more workflows were affected by this. \r\n%n%nWorkflow name: %2\r\n%nInstance name: %3\r\n%nInstance ID: %4\r\n%nManagement group: %1\r\n]]></RawDescription><LCID>2057</LCID><Params><Param>Garda</Param><Param>Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection</Param><Param>IICTCISMWV205.msnet.railb.be</Param><Param>{5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}</Param><Param>SCOMpercentageCPUTimeCounter.ps1</Param><Param>300</Param><Param><![CDATA[System.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\r\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\r\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\r\n]]></Param><Param>System.Management.Automation.ActionPreferenceStopException</Param></Params><EventData><DataItem type=\"System.XmlData\" time=\"2025-02-13T07:18:45.6367338+01:00\" sourceHealthServiceId=\"5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0\"><EventData xmlns=\"http://schemas.microsoft.com/win/2004/08/events/event\"><Data>Garda</Data><Data>Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection</Data><Data>IICTCISMWV205.msnet.railb.be</Data><Data>{5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}</Data><Data>SCOMpercentageCPUTimeCounter.ps1</Data><Data>300</Data><Data>System.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\r\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\r\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\r\n</Data><Data>System.Management.Automation.ActionPreferenceStopException</Data></EventData></DataItem></EventData><EventDisplayNumber>22406</EventDisplayNumber><EventDescription><![CDATA[The PowerShell script failed with below exception\r\n\r\nSystem.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\r\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\r\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\r\n\r\n\r\n\r\n\r\nScript Name:\tSCOMpercentageCPUTimeCounter.ps1\r\n\r\n\r\nOne or more workflows were affected by this. \r\n\r\n\r\nWorkflow name: Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection\r\n\r\nInstance name: IICTCISMWV205.msnet.railb.be\r\n\r\nInstance ID: {5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}\r\n\r\nManagement group: Garda\r\n]]></EventDescription><Keywords>*****************</Keywords></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The PowerShell script failed with below exception\n\nSystem.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\n\n\n\n\nScript Name:\tSCOMpercentageCPUTimeCounter.ps1\n\n\nOne or more workflows were affected by this. \n\n\nWorkflow name: Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection\n\nInstance name: IICTCISMWV205.msnet.railb.be\n\nInstance ID: {5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}\n\nManagement group: Garda\n", "Id": "570d2fbe-27d4-46e8-8e3f-a533fe462d7d", "IsMonitorAlert": "False", "LastModified": "13/02/2025 06:18:45", "LastModifiedBy": "System", "LastModifiedByNonConnector": "13/02/2025 06:18:45", "MaintenanceModeLastModified": "01/01/1900 00:00:00", "MonitoringClassId": "ab4c891f-3359-3fb6-0704-075fbfe36710", "MonitoringObjectDisplayName": "IICTCISMWV205.msnet.railb.be", "MonitoringObjectFullName": "Microsoft.SystemCenter.HealthService:IICTCISMWV205.msnet.railb.be", "MonitoringObjectHealthState": "Warning", "MonitoringObjectId": "5e644f0a-8993-dbd0-969e-aca5f9bbb2a0", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": null, "MonitoringObjectPath": "IICTCISMWV205.msnet.railb.be", "MonitoringRuleId": "c4108e23-e5b9-b0cd-9b75-be85d2039035", "Name": "Power Shell Script failed to run", "NetbiosComputerName": "IICTCISMWV205", "NetbiosDomainName": "msnet.railb.be", "Owner": null, "Parameters": "", "PrincipalName": "IICTCISMWV205.msnet.railb.be", "Priority": "Normal", "ProblemId": "07cf9161-745f-822a-4f51-ebd91b7a7053", "RepeatCount": "72", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "c4108e23-e5b9-b0cd-9b75-be85d2039035", "Severity": "Warning", "SiteName": null, "StateLastModified": "12/02/2025 23:37:27", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "12/02/2025 07:09:55", "TimeRaised": "12/02/2025 07:09:55", "TimeResolutionStateLastModified": "12/02/2025 07:09:55", "TimeResolved": "", "UnformattedDescription": "The PowerShell script failed with below exception\n\nSystem.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\n\n\n\n\nScript Name:\tSCOMpercentageCPUTimeCounter.ps1\n\n\nOne or more workflows were affected by this. \n\n\nWorkflow name: Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection\n\nInstance name: IICTCISMWV205.msnet.railb.be\n\nInstance ID: {5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}\n\nManagement group: Garda\n"}, "output": {}, "comment": "No records expected since both metric_name and metric_type are NA"}, {"input": {"DisplayString": "Power Shell Script failed to run", "Category": "<PERSON><PERSON>", "ClassId": "ab4c891f-3359-3fb6-0704-075fbfe36710", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"Microsoft.Windows.EventData\" time=\"2025-02-13T07:18:45.6367338+01:00\" sourceHealthServiceId=\"5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0\"><EventOriginId>{CB1EA5DB-C3CF-4694-9C44-7713AC9E0742}</EventOriginId><PublisherId>{2DFE7810-DE9F-785F-CD11-80E1C9EE8A67}</PublisherId><PublisherName>Health Service Modules</PublisherName><EventSourceName>Health Service Modules</EventSourceName><Channel>Operations Manager</Channel><LoggingComputer>IICTCISMWV205.msnet.railb.be</LoggingComputer><EventNumber>**********</EventNumber><EventCategory>0</EventCategory><EventLevel>1</EventLevel><UserName>N/A</UserName><RawDescription><![CDATA[The PowerShell script failed with below exception%n%n%7%n\r\n%n%nScript Name:%t%5\r\n%n%nOne or more workflows were affected by this. \r\n%n%nWorkflow name: %2\r\n%nInstance name: %3\r\n%nInstance ID: %4\r\n%nManagement group: %1\r\n]]></RawDescription><LCID>2057</LCID><Params><Param>Garda</Param><Param>Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection</Param><Param>IICTCISMWV205.msnet.railb.be</Param><Param>{5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}</Param><Param>SCOMpercentageCPUTimeCounter.ps1</Param><Param>300</Param><Param><![CDATA[System.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\r\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\r\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\r\n]]></Param><Param>System.Management.Automation.ActionPreferenceStopException</Param></Params><EventData><DataItem type=\"System.XmlData\" time=\"2025-02-13T07:18:45.6367338+01:00\" sourceHealthServiceId=\"5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0\"><EventData xmlns=\"http://schemas.microsoft.com/win/2004/08/events/event\"><Data>Garda</Data><Data>Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection</Data><Data>IICTCISMWV205.msnet.railb.be</Data><Data>{5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}</Data><Data>SCOMpercentageCPUTimeCounter.ps1</Data><Data>300</Data><Data>System.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\r\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\r\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\r\n</Data><Data>System.Management.Automation.ActionPreferenceStopException</Data></EventData></DataItem></EventData><EventDisplayNumber>22406</EventDisplayNumber><EventDescription><![CDATA[The PowerShell script failed with below exception\r\n\r\nSystem.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\r\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\r\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\r\n\r\n\r\n\r\n\r\nScript Name:\tSCOMpercentageCPUTimeCounter.ps1\r\n\r\n\r\nOne or more workflows were affected by this. \r\n\r\n\r\nWorkflow name: Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection\r\n\r\nInstance name: IICTCISMWV205.msnet.railb.be\r\n\r\nInstance ID: {5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}\r\n\r\nManagement group: Garda\r\n]]></EventDescription><Keywords>*****************</Keywords></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "The PowerShell script failed with below exception\n\nSystem.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\n\n\n\n\nScript Name:\tSCOMpercentageCPUTimeCounter.ps1\n\n\nOne or more workflows were affected by this. \n\n\nWorkflow name: Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection\n\nInstance name: IICTCISMWV205.msnet.railb.be\n\nInstance ID: {5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}\n\nManagement group: Garda\n", "Id": "570d2fbe-27d4-46e8-8e3f-a533fe462d7d", "IsMonitorAlert": "False", "LastModified": "13/02/2025 06:30:10", "LastModifiedBy": "MSNET\\SYS_A868-Automation", "LastModifiedByNonConnector": "13/02/2025 06:30:10", "MaintenanceModeLastModified": "01/01/1900 00:00:00", "MonitoringClassId": "ab4c891f-3359-3fb6-0704-075fbfe36710", "MonitoringObjectDisplayName": "IICTCISMWV205.msnet.railb.be", "MonitoringObjectFullName": "Microsoft.SystemCenter.HealthService:IICTCISMWV205.msnet.railb.be", "MonitoringObjectHealthState": "Warning", "MonitoringObjectId": "5e644f0a-8993-dbd0-969e-aca5f9bbb2a0", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": null, "MonitoringObjectPath": "IICTCISMWV205.msnet.railb.be", "MonitoringRuleId": "c4108e23-e5b9-b0cd-9b75-be85d2039035", "Name": "Power Shell Script failed to run", "NetbiosComputerName": "IICTCISMWV205", "NetbiosDomainName": "msnet.railb.be", "Owner": null, "Parameters": "", "PrincipalName": "IICTCISMWV205.msnet.railb.be", "Priority": "Normal", "ProblemId": "07cf9161-745f-822a-4f51-ebd91b7a7053", "RepeatCount": "72", "ResolutionState": "255", "ResolvedBy": "MSNET\\SYS_A868-Automation", "RuleId": "c4108e23-e5b9-b0cd-9b75-be85d2039035", "Severity": "Warning", "SiteName": null, "StateLastModified": "12/02/2025 23:37:27", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "12/02/2025 07:09:55", "TimeRaised": "12/02/2025 07:09:55", "TimeResolutionStateLastModified": "13/02/2025 06:30:10", "TimeResolved": "13/02/2025 06:30:10", "UnformattedDescription": "The PowerShell script failed with below exception\n\nSystem.Management.Automation.ActionPreferenceStopException: The running command stopped because the preference variable \"ErrorActionPreference\" or common parameter is set to Stop: Invalid query \"select * from Win32_PerfFormattedData_PerfProc_Process\"At line:307 char:23\n+ ... iService =  Get-WMIObject -Namespace \"root\\cimv2\" -ComputerName $Comp ...\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n   at System.Management.Automation.Runspaces.PipelineBase.Invoke(IEnumerable input)\n   at Microsoft.EnterpriseManagement.Common.PowerShell.RunspaceController.RunScript[T](String scriptName, String scriptBody, Dictionary`2 parameters, Object[] constructorArgs, IScriptDebug iScriptDebug, Boolean bSerializeOutput)\n\n\n\n\nScript Name:\tSCOMpercentageCPUTimeCounter.ps1\n\n\nOne or more workflows were affected by this. \n\n\nWorkflow name: Microsoft.SystemCenter.HealthService.SCOMpercentageCPUTimeCollection\n\nInstance name: IICTCISMWV205.msnet.railb.be\n\nInstance ID: {5E644F0A-8993-DBD0-969E-ACA5F9BBB2A0}\n\nManagement group: Garda\n"}, "output": {}, "comment": "No records expected since both metric_name and metric_type are NA"}, {"input": {"DisplayString": "Free Space (%) For Cluster Disk Alert", "Category": "AvailabilityHealth", "ClassId": "10f38c20-64e5-06ea-6e54-a18c9226e0a3", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.Performance.Data\" time=\"2025-03-11T10:36:59.1398294+01:00\" sourceHealthServiceId=\"F64AFED8-0975-637B-A7E0-F2D9632AF04E\"><TimeSampled>2025-03-11T10:34:55.9605052+01:00</TimeSampled><ObjectName>Cluster Disk</ObjectName><CounterName>Free space / Percent</CounterName><InstanceName>Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}</InstanceName><IsNull Type=\"Boolean\">false</IsNull><Value>0</Value></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "IICTCISQWC504.msnet.railb.be: The cluster Disk Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6} Named as 15 is running out of disk space. The value that exceeded the warning or error threshold is 0% free space. (Partition: \\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}).", "Id": "202f1f47-24b8-4cdc-8397-a585bfef4d5a", "IsMonitorAlert": "True", "LastModified": "11/03/2025 09:37:12", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "11/03/2025 09:37:12", "MaintenanceModeLastModified": "28/11/2024 10:41:43", "MonitoringClassId": "10f38c20-64e5-06ea-6e54-a18c9226e0a3", "MonitoringObjectDisplayName": "Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}", "MonitoringObjectFullName": "Microsoft.Windows.Server.ClusterDisksMonitoring.ClusterDisk:IICTCISQWC504.msnet.railb.be;Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}", "MonitoringObjectHealthState": "Error", "MonitoringObjectId": "fa0e90d7-a543-9bf4-15b7-93f4ce1cac61", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}", "MonitoringObjectPath": "IICTCISQWC504.msnet.railb.be", "MonitoringRuleId": "efd591b6-741c-93a9-4eef-ea93946bb24e", "Name": "Free Space (%) For Cluster Disk Alert", "NetbiosComputerName": "IICTCISQWC504", "NetbiosDomainName": "msnet", "Owner": null, "Parameters": "", "PrincipalName": "IICTCISQWC504.msnet.railb.be", "Priority": "Normal", "ProblemId": "efd591b6-741c-93a9-4eef-ea93946bb24e", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "efd591b6-741c-93a9-4eef-ea93946bb24e", "Severity": "Error", "SiteName": null, "StateLastModified": "11/03/2025 09:36:59", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "11/03/2025 09:36:59", "TimeRaised": "11/03/2025 09:36:59", "TimeResolutionStateLastModified": "11/03/2025 09:36:59", "TimeResolved": "", "UnformattedDescription": "IICTCISQWC504.msnet.railb.be: The cluster Disk Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6} Named as 15 is running out of disk space. The value that exceeded the warning or error threshold is 0% free space. (Partition: \\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6})."}, "output": {"s2110_alarm": [{"metric_name": "Cluster Disk 23"}], "s2110_occurrence": [{"metric_name": "Cluster Disk 23"}]}}, {"input": {"DisplayString": "Free Space (%) For Cluster Disk Alert", "Category": "AvailabilityHealth", "ClassId": "10f38c20-64e5-06ea-6e54-a18c9226e0a3", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": "<DataItem type=\"System.Performance.Data\" time=\"2025-03-11T15:36:57.0943696+01:00\" sourceHealthServiceId=\"F64AFED8-0975-637B-A7E0-F2D9632AF04E\"><TimeSampled>2025-03-11T15:34:55.4985441+01:00</TimeSampled><ObjectName>Cluster Disk</ObjectName><CounterName>Free space / Percent</CounterName><InstanceName>Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}</InstanceName><IsNull Type=\"Boolean\">false</IsNull><Value>25</Value></DataItem>", "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "IICTCISQWC504.msnet.railb.be: The cluster Disk Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6} Named as 15 is running out of disk space. The value that exceeded the warning or error threshold is 0% free space. (Partition: \\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}).", "Id": "202f1f47-24b8-4cdc-8397-a585bfef4d5a", "IsMonitorAlert": "True", "LastModified": "11/03/2025 14:36:57", "LastModifiedBy": "System", "LastModifiedByNonConnector": "11/03/2025 14:36:57", "MaintenanceModeLastModified": "28/11/2024 10:41:43", "MonitoringClassId": "10f38c20-64e5-06ea-6e54-a18c9226e0a3", "MonitoringObjectDisplayName": "Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}", "MonitoringObjectFullName": "Microsoft.Windows.Server.ClusterDisksMonitoring.ClusterDisk:IICTCISQWC504.msnet.railb.be;Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}", "MonitoringObjectHealthState": "Success", "MonitoringObjectId": "fa0e90d7-a543-9bf4-15b7-93f4ce1cac61", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6}", "MonitoringObjectPath": "IICTCISQWC504.msnet.railb.be", "MonitoringRuleId": "efd591b6-741c-93a9-4eef-ea93946bb24e", "Name": "Free Space (%) For Cluster Disk Alert", "NetbiosComputerName": "IICTCISQWC504", "NetbiosDomainName": "msnet", "Owner": null, "Parameters": "", "PrincipalName": "IICTCISQWC504.msnet.railb.be", "Priority": "Normal", "ProblemId": "efd591b6-741c-93a9-4eef-ea93946bb24e", "RepeatCount": "0", "ResolutionState": "255", "ResolvedBy": "System", "RuleId": "efd591b6-741c-93a9-4eef-ea93946bb24e", "Severity": "Error", "SiteName": null, "StateLastModified": "11/03/2025 14:36:57", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "11/03/2025 09:36:59", "TimeRaised": "11/03/2025 09:36:59", "TimeResolutionStateLastModified": "11/03/2025 14:36:57", "TimeResolved": "11/03/2025 14:36:57", "UnformattedDescription": "IICTCISQWC504.msnet.railb.be: The cluster Disk Cluster Disk 23_\\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6} Named as 15 is running out of disk space. The value that exceeded the warning or error threshold is 0% free space. (Partition: \\\\?\\Volume{809630aa-d440-49cd-ba23-e81411f28df6})."}, "output": {"s2110_occurrence": [{"metric_name": "Cluster Disk 23"}]}}, {"input": {"DisplayString": "AD Site Availability Health Degraded", "Category": "Custom", "ClassId": "adf4f2cc-8380-9b9b-532e-20306371d65a", "ConnectorId": "6a1f8c0e-b8f1-4147-8c9b-5a2f98f10003", "ConnectorStatus": "Pending", "Context": null, "CustomField1": null, "CustomField10": null, "CustomField2": null, "CustomField3": null, "CustomField4": null, "CustomField5": null, "CustomField6": null, "CustomField7": null, "CustomField8": null, "CustomField9": null, "Description": "More than 60% of the DCs contained in this AD Site report an Availability Health problem]", "Id": "04907632-8cdc-492d-88a4-3c1a6f2e1ec3", "IsMonitorAlert": "True", "LastModified": "15/03/2025 07:34:45", "LastModifiedBy": "Connector Framework Alert Write Action", "LastModifiedByNonConnector": "15/03/2025 07:34:45", "MaintenanceModeLastModified": "11/12/2023 17:21:10", "MonitoringClassId": "adf4f2cc-8380-9b9b-532e-20306371d65a", "MonitoringObjectDisplayName": "MONCEAU", "MonitoringObjectFullName": "Microsoft.Windows.Server.AD.Library.Site:msnet.railb.be;MONCEAU", "MonitoringObjectHealthState": "Error", "MonitoringObjectId": "737991ef-e6aa-343c-0bfd-822ba5690c1b", "MonitoringObjectInMaintenanceMode": "False", "MonitoringObjectName": "MONCEAU", "MonitoringObjectPath": "msnet.railb.be", "MonitoringRuleId": "a42c964a-2826-e972-2196-a2c195ed01b0", "Name": "AD Site Availability Health Degraded", "NetbiosComputerName": null, "NetbiosDomainName": null, "Owner": null, "Parameters": "", "PrincipalName": null, "Priority": "High", "ProblemId": "a42c964a-2826-e972-2196-a2c195ed01b0", "RepeatCount": "0", "ResolutionState": "0", "ResolvedBy": null, "RuleId": "a42c964a-2826-e972-2196-a2c195ed01b0", "Severity": "Error", "SiteName": null, "StateLastModified": "15/03/2025 07:33:54", "TfsWorkItemId": null, "TfsWorkItemOwner": null, "TicketId": null, "TimeAdded": "15/03/2025 07:33:54", "TimeRaised": "15/03/2025 07:33:54", "TimeResolutionStateLastModified": "15/03/2025 07:33:54", "TimeResolved": "", "UnformattedDescription": "More than 60% of the DCs contained in this AD Site report an Availability Health problem]"}, "output": {"s2110_alarm": [{"ci_id": "AD", "metric_type": "/ServiceAvailability/", "metric_name": "AD_Site_Availability_Health_Degraded"}], "s2110_occurrence": [{"ci_id": "AD", "metric_type": "/ServiceAvailability/", "metric_name": "AD_Site_Availability_Health_Degraded"}]}}]}