{"extra_env": {"ZABBIX_CC": "ictops", "OLYMPUS_AGENT_ID": "28"}, "clear_agent_for_next_run": true, "agent_ids": [28], "data": [{"input": {"eventid": "0", "name": "Zabbix Server on iictyizblv033 Heartbeat Message", "value": 1, "severity": 1, "clock": 1739448459, "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "floc_id": "N/A", "ci_id": "Zabbix Server on iictyizblv033", "os": "N/A", "object_1": "N/A", "object_2": "N/A", "object_3": "N/A", "object_4": "N/A", "hostname": ["Zabbix Server on iictyizblv033"], "hostid": ["N/A"], "trigger_comments": "N/A", "triggerid": "N/A"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "Zabbix Server on iictyizblv033", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "Zabbix Server on iictyizblv033 Heartbeat Message", "clear_time": null, "severity": 1, "raise_time": "2025-02-13 12:07:39"}]}}, {"input": {"eventid": "3738317", "name": "Host unreachable", "value": "1", "severity": "4", "clock": "1739449774", "metric_name": "PING", "metric_type": "/NetworkConnectivity/", "floc_id": "N/A", "ci_id": "gezee-con-016", "os": ["N/A"], "object_1": "N/A", "object_2": "N/A", "object_3": "N/A", "object_4": "N/A", "hostname": ["gezee-con-016"], "hostid": ["39106"], "trigger_comments": "APP::PING::{$CI_ID}::{$METRIC_NAME}", "triggerid": "106387"}, "output": {"s2110_alarm": [{"ci_id": "gezee-con-016", "metric_name": "PING", "metric_type": "/NetworkConnectivity/", "event_id": "3738317", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "gezee-con-016", "metric_name": "PING", "metric_type": "/NetworkConnectivity/", "summary": "Host unreachable", "clear_time": null, "severity": 4}]}}, {"input": {"eventid": "3738323", "name": "Host unreachable", "value": "0", "severity": "0", "clock": "1739450674", "metric_name": "PING", "metric_type": "/NetworkConnectivity/", "floc_id": "N/A", "ci_id": "gezee-con-016", "os": ["N/A"], "object_1": "N/A", "object_2": "N/A", "object_3": "N/A", "object_4": "N/A", "hostname": ["gezee-con-016"], "hostid": ["39106"], "trigger_comments": "APP::PING::{$CI_ID}::{$METRIC_NAME}", "triggerid": "106387"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "gezee-con-016", "metric_name": "PING", "metric_type": "/NetworkConnectivity/", "summary": "Host unreachable", "clear_time": "2025-02-13T12:44:34", "raise_time": "2025-02-13T12:44:34", "severity": 0}]}}, {"input": {"eventid": "3738331", "name": "https://mopioutbound.infrabel.be/websiteapi/monitoring/countMsgSent Web scenario failed: Contents do not match '<h1 class='OK'>OK</h1>'", "value": "1", "severity": "5", "clock": "1739452258", "metric_name": "countMsgSent", "metric_type": "/URL/", "floc_id": "N/A", "ci_id": "/Infrabel/a1958-mopi.app/a1958-mopi_pool", "os": ["N/A"], "object_1": "N/A", "object_2": "N/A", "object_3": "N/A", "object_4": "N/A", "hostname": ["SWS064398 countMsgSent"], "hostid": ["34757"], "trigger_comments": "APP::URL::{$CI_ID}::{$METRIC_NAME}", "triggerid": "93787"}, "output": {"s2110_alarm": [{"ci_id": "/Infrabel/a1958-mopi.app/a1958-mopi_pool", "metric_name": "countMsgSent", "metric_type": "/URL/", "event_id": "3738331", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "/Infrabel/a1958-mopi.app/a1958-mopi_pool", "metric_name": "countMsgSent", "metric_type": "/URL/", "summary": "https://mopioutbound.infrabel.be/websiteapi/monitoring/countMsgSent Web scenario failed: Contents do not match '<h1 class='OK'>OK</h1>'", "clear_time": null, "severity": 5}]}}, {"input": {"eventid": "3738334", "name": "https://mopioutbound.infrabel.be/websiteapi/monitoring/countMsgSent Web scenario failed: Contents do not match '<h1 class='OK'>OK</h1>'", "value": "0", "severity": "0", "clock": "1739452868", "metric_name": "countMsgSent", "metric_type": "/URL/", "floc_id": "N/A", "ci_id": "/Infrabel/a1958-mopi.app/a1958-mopi_pool", "os": ["N/A"], "object_1": "N/A", "object_2": "N/A", "object_3": "N/A", "object_4": "N/A", "hostname": ["SWS064398 countMsgSent"], "hostid": ["34757"], "trigger_comments": "APP::URL::{$CI_ID}::{$METRIC_NAME}", "triggerid": "93787"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "/Infrabel/a1958-mopi.app/a1958-mopi_pool", "metric_name": "countMsgSent", "metric_type": "/URL/", "clear_time": "2025-02-13T13:21:08", "severity": 0, "raise_time": "2025-02-13T13:21:08"}]}}]}