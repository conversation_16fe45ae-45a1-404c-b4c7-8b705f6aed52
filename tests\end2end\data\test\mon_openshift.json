{"extra_env": {"OLYMPUS_AGENT_ID": "17"}, "agent_ids": [17], "data": [{"input": {"SNMPv2-MIB::sysUpTime.0": "44 days, 23:03:45.57", "SNMPv2-SMI::enterprises.44645.324.1.2.1": "A1647", "SNMPv2-SMI::enterprises.44645.324.1.2.6": "openshift-monitoring", "SNMPv2-SMI::enterprises.44645.324.1.2.14": "sas.nonprod-ocp.infrabel.be", "message": "#<SNMP::SNMPv2_Trap:0x6826e2c7 @varbind_list=[#<SNMP::VarBind:0x5e6c6707 @name=[*******.2.1.1.3.0], @value=#<SNMP::TimeTicks:0x3453cc76 @value=388462557>>, #<SNMP::VarBind:0x6bbc30e1 @name=[*******.6.3.1.1.4.1.0], @value=[*******.4.1.44645.324.1]>, #<SNMP::VarBind:0x76fca4b1 @name=[*******.4.1.44645.324.1.2.1], @value=\"A1647\">, #<SNMP::VarBind:0x4b15b136 @name=[*******.4.1.44645.324.1.2.2], @value=\"sas.nonprod-ocp.infrabel.be\">, #<SNMP::VarBind:0x610ff45e @name=[*******.4.1.44645.324.1.2.3], @value=\"Nonprod-ocp.infrabel\">, #<SNMP::VarBind:0x6bbefba6 @name=[*******.4.1.44645.324.1.2.4], @value=\"openshift-monitoring\">, #<SNMP::VarBind:0x7874134c @name=[*******.4.1.44645.324.1.2.5], @value=\"\">, #<SNMP::VarBind:0x1e0e9175 @name=[*******.4.1.44645.324.1.2.6], @value=\"openshift-monitoring\">, #<SNMP::VarBind:0xbd3bee @name=[*******.4.1.44645.324.1.2.7], @value=\"Watchdog\">, #<SNMP::VarBind:0x1cc29394 @name=[*******.4.1.44645.324.1.2.8], @value=\"sas.nonprod-ocp.infrabel.be 3893d5275809e4c2d206638cea71a4c2\">, #<SNMP::VarBind:0x44740d33 @name=[*******.4.1.44645.324.1.2.9], @value=\"2024-10-29T15:45:02.569Z\">, #<SNMP::VarBind:0x2e9bb9f1 @name=[*******.4.1.44645.324.1.2.10], @value=\"0001-01-01T00:00:00Z\">, #<SNMP::VarBind:0x47109587 @name=[*******.4.1.44645.324.1.2.11], @value=\"FIRING\">, #<SNMP::VarBind:0x4f46e0c2 @name=[*******.4.1.44645.324.1.2.12], @value=\"INFO\">, #<SNMP::VarBind:0x2bbf718c @name=[*******.4.1.44645.324.1.2.13], @value=\"sas.nonprod-ocp.infrabel.be Watchdog openshift-monitoring This is an alert meant to ensure that the entire alerting pipeline is functional. This alert is always firing, therefore it should always be firing in Alertmanager and always fire against a receiver. There are integrations with various notification mechanisms that send a notification when this alert is not firing. For example the \\\"DeadMansSnitch\\\" integration in PagerDuty. \">, #<SNMP::VarBind:0x5818113b @name=[*******.4.1.44645.324.1.2.14], @value=\"sas.nonprod-ocp.infrabel.be\">, #<SNMP::VarBind:0x1be71b12 @name=[*******.4.1.44645.324.1.2.99], @value=\"{\\\"annotations\\\":{\\\"description\\\":\\\"This is an alert meant to ensure that the entire alerting pipeline is functional.\\\\nThis alert is always firing, therefore it should always be firing in Alertmanager\\\\nand always fire against a receiver. There are integrations with various notification\\\\nmechanisms that send a notification when this alert is not firing. For example the\\\\n\\\\\\\"DeadMansSnitch\\\\\\\" integration in PagerDuty.\\\\n\\\",\\\"summary\\\":\\\"An alert that should always be firing to certify that Alertmanager is working properly.\\\"},\\\"endsAt\\\":\\\"0001-01-01T00:00:00Z\\\",\\\"fingerprint\\\":\\\"6934731368443c07\\\",\\\"generatorURL\\\":\\\"https://console.apps.sas.nonprod-ocp.infrabel.be/monitoring/graph?g0.expr=vector%281%29&g0.tab=1\\\",\\\"labels\\\":{\\\"alertname\\\":\\\"Watchdog\\\",\\\"namespace\\\":\\\"openshift-monitoring\\\",\\\"openshift_io_alert_source\\\":\\\"platform\\\",\\\"prometheus\\\":\\\"openshift-monitoring/k8s\\\",\\\"severity\\\":\\\"none\\\"},\\\"startsAt\\\":\\\"2024-10-29T15:45:02.569Z\\\",\\\"status\\\":\\\"firing\\\"}\">], @request_id=970775419, @error_index=0, @error_status=0, @source_ip=\"***********\">", "@timestamp": "2024-12-13T15:11:23.403396303Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.44645.324.1", "SNMPv2-SMI::enterprises.44645.324.1.2.2": "sas.nonprod-ocp.infrabel.be", "SNMPv2-SMI::enterprises.44645.324.1.2.13": "sas.nonprod-ocp.infrabel.be Watchdog openshift-monitoring This is an alert meant to ensure that the entire alerting pipeline is functional. This alert is always firing, therefore it should always be firing in Alertmanager and always fire against a receiver. There are integrations with various notification mechanisms that send a notification when this alert is not firing. For example the \"DeadMansSnitch\" integration in PagerDuty. ", "@version": "1", "SNMPv2-SMI::enterprises.44645.324.1.2.8": "sas.nonprod-ocp.infrabel.be 3893d5275809e4c2d206638cea71a4c2", "SNMPv2-SMI::enterprises.44645.324.1.2.11": "FIRING", "SNMPv2-SMI::enterprises.44645.324.1.2.99": "{\"annotations\":{\"description\":\"This is an alert meant to ensure that the entire alerting pipeline is functional.\\nThis alert is always firing, therefore it should always be firing in Alertmanager\\nand always fire against a receiver. There are integrations with various notification\\nmechanisms that send a notification when this alert is not firing. For example the\\n\\\"DeadMansSnitch\\\" integration in PagerDuty.\\n\",\"summary\":\"An alert that should always be firing to certify that Alertmanager is working properly.\"},\"endsAt\":\"0001-01-01T00:00:00Z\",\"fingerprint\":\"6934731368443c07\",\"generatorURL\":\"https://console.apps.sas.nonprod-ocp.infrabel.be/monitoring/graph?g0.expr=vector%281%29&g0.tab=1\",\"labels\":{\"alertname\":\"Watchdog\",\"namespace\":\"openshift-monitoring\",\"openshift_io_alert_source\":\"platform\",\"prometheus\":\"openshift-monitoring/k8s\",\"severity\":\"none\"},\"startsAt\":\"2024-10-29T15:45:02.569Z\",\"status\":\"firing\"}", "SNMPv2-SMI::enterprises.44645.324.1.2.4": "openshift-monitoring", "host": "***********", "SNMPv2-SMI::enterprises.44645.324.1.2.12": "INFO", "type": "snmp_trap", "SNMPv2-SMI::enterprises.44645.324.1.2.5": "", "SNMPv2-SMI::enterprises.44645.324.1.2.9": "2024-10-29T15:45:02.569Z", "SNMPv2-SMI::enterprises.44645.324.1.2.7": "Watchdog", "SNMPv2-SMI::enterprises.44645.324.1.2.10": "0001-01-01T00:00:00Z", "SNMPv2-SMI::enterprises.44645.324.1.2.3": "Nonprod-ocp.infrabel"}, "output": {"s2110_alarm": [], "s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "sas.nonprod-ocp.infrabel.be", "metric_name": "Watchdog", "metric_type": "/OpenShift/", "alarm_id": null, "summary": "OpenShift Heartbeat Message", "severity": 1, "raise_time": "2024-12-13T15:11:23.403396", "clear_time": null, "additional_data": null, "handle_time": "2025-01-01T12:00:01"}]}}]}