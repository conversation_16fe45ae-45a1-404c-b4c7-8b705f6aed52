"""The common module for mon-zabbix.

This should contains functions that can be used for both the old and new way.
"""

from datetime import datetime

import pandas as pd

from mon_zabbix import statics
from mon_zabbix.enums import ZabbixTypes
from olympus_common import enums as olympus_enums
from olympus_common import utils
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import ZabbixCC
from olympus_common.exceptions import ConfigurationError


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def platform(row: pd.Series) -> str:
    """Return the string corresponding to the platform depending on the type of Zabbix.

    Parameters
    ----------
    row : pd.Series
        The row corresponding of the event.

    Returns
    -------
    str
        The platform name.

    Raises
    ------
    ValueError
        If the type of Zabbix is unknown.
    """
    zabbix_type_: str = row["zabbix_type"]
    alert_group_: str = row["alert_group"]
    if alert_group_ in statics.PLATFORM_MAPPING.keys():
        return statics.PLATFORM_MAPPING[alert_group_]

    if zabbix_type_ in {ZabbixTypes.ICTOPS.value, ZabbixTypes.SCADA.value}:
        return "Application"
    elif zabbix_type_ == ZabbixTypes.LINUX.value:
        return "Linux"
    elif zabbix_type_ == ZabbixTypes.UNIX.value:
        return "Unix"
    elif zabbix_type_ == ZabbixTypes.AIX.value:
        return "AIX"
    else:
        raise ValueError(f"{zabbix_type_} is not a valid value. Good values are {list(ZabbixTypes)}.")


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def agent() -> str:
    """Return the agent as described by the DD."""
    return "Zabbix"


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def manager(zabbix_cc: str) -> str:
    """Return the agent as described by the DD."""
    return f"mon-zabbix-{zabbix_cc}"


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def zabbix_type(row: pd.Series, zabbix_cc: str) -> str:
    """Return the Zabbix event type.

    Notes
    -----
    For AIX/UNIX the split is done following the OS.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.
    zabbix_cc : str
        The Zabbix competence center name, from the environment variable.

    Returns
    -------
    str
        The Zabbix type corresponding to the event.

    Raises
    ------
    ConfigurationError
        If the provided zabbix_cc is not corresponding to one of ZabbixCC.
    """
    os: str = row["os"]
    if zabbix_cc == ZabbixCC.LCC.value:
        return ZabbixTypes.LINUX.value
    elif zabbix_cc == ZabbixCC.UCC.value:
        if "aix" in os.lower():
            return ZabbixTypes.AIX.value
        elif "sparc" in os.lower() or "rhel" in os.lower() or "i386" in os.lower() or "ucc" in os.lower():
            return ZabbixTypes.UNIX.value
        else:
            return ZabbixTypes.UNIX.value
    elif zabbix_cc == ZabbixCC.ICTOPS.value:
        return ZabbixTypes.ICTOPS.value
    elif zabbix_cc == ZabbixCC.SCADA.value:
        return ZabbixTypes.SCADA.value
    else:
        raise ConfigurationError(f"{zabbix_cc} is not in the proposed values: {list(ZabbixCC)}")


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def transform_list_field_to_str_field(row: pd.Series, field_name: str):
    """Transform a field that contains a list into its first element.

    This function would have no impact on string field since the type of the field is checked before taking its first
    element.
    """
    if isinstance(row[field_name], list):
        return row[field_name][0] if len(row[field_name]) > 0 else "N/A"
    else:
        return row[field_name]


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def scope() -> str:
    """Return the scope as described by the DD."""
    return olympus_enums.Scope.IT.value


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def clear_type() -> str:
    """Return the clear type as described by the DD."""
    return olympus_enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def handle_time() -> datetime:
    """Return the handle time of the event."""
    return utils.now_naive()
