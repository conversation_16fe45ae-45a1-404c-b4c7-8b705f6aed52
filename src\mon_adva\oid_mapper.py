"""OID mapping for Adva."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Adva."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "snmptrap.id": "original_event_id",
            "snmptrap.update": "update",
            "snmptrap.name": "name",
            "snmptrap.event_type": "event_type",
            "snmptrap.severity": "severity",
            "snmptrap.impairment": "impairement",
            "snmptrap.disabled": "disabled",
            "snmptrap.neType": "neType",
            "snmptrap.neName": "neName",
            "snmptrap.nelpAdress": "nelpAdress",
            "snmptrap.entity": "entity",
            "snmptrap.location": "location",
            "snmptrap.direction": "direction",
            "snmptrap.nmsTime": "nmsTime",
            "snmptrap.neTime": "neTime",
            "snmptrap.summary": "summary",
            "snmptrap.description": "description",
            "snmptrap.acknowledged": "acknowledged",
            "snmptrap.corr": "corr",
            "snmptrap.corrRef": "corrRef",
            "snmptrap.mtosiNeType": "mtosiNeType",
            "snmptrap.customerName": "customerName",
            "snmptrap.security": "security",
            "snmptrap.comment": "comment",
        }

    @property
    def unmapped_oids(self) -> dict[str, str]:
        """Return unmapped OIDs."""
        return {
            "SNMPv2-SMI::enterprises.2544.********.1.1": "id",
            "SNMPv2-SMI::enterprises.2544.********.1.2": "update",
            "SNMPv2-SMI::enterprises.2544.********.1.3": "name",
            "SNMPv2-SMI::enterprises.2544.********.1.4": "event_type",
            "SNMPv2-SMI::enterprises.2544.********.1.5": "severity",
            "SNMPv2-SMI::enterprises.2544.********.1.6": "impairment",
            "SNMPv2-SMI::enterprises.2544.********.1.7": "disabled",
            "SNMPv2-SMI::enterprises.2544.********.1.8": "neType",
            "SNMPv2-SMI::enterprises.2544.********.1.9": "neName",
            "SNMPv2-SMI::enterprises.2544.********.1.10": "nelpAdress",
            "SNMPv2-SMI::enterprises.2544.********.1.11": "entity",
            "SNMPv2-SMI::enterprises.2544.********.1.12": "location",
            "SNMPv2-SMI::enterprises.2544.********.1.13": "direction",
            "SNMPv2-SMI::enterprises.2544.********.1.14": "nmsTime",
            "SNMPv2-SMI::enterprises.2544.********.1.15": "neTime",
            "SNMPv2-SMI::enterprises.2544.********.1.16": "summary",
            "SNMPv2-SMI::enterprises.2544.********.1.17": "description",
            "SNMPv2-SMI::enterprises.2544.********.1.18": "acknowledged",
            "SNMPv2-SMI::enterprises.2544.********.1.19": "corr",
            "SNMPv2-SMI::enterprises.2544.********.1.20": "corrRef",
            "SNMPv2-SMI::enterprises.2544.********.1.21": "mtosiNeType",
            "SNMPv2-SMI::enterprises.2544.********.1.22": "customerName",
            "SNMPv2-SMI::enterprises.2544.********.1.23": "security",
            "SNMPv2-SMI::enterprises.2544.********.1.24": "comment",
        }

    @property
    def drop_if_all_nan(self) -> list[str]:
        """Return fields to drop if all values are NaN."""
        return ["neName", "name"]
