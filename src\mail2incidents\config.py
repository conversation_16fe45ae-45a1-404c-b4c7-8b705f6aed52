"""Configuration module for mail2incidents."""

from dataclasses import dataclass

from olympus_common.config import JW<PERSON><PERSON>onfig, ServerConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import <PERSON><PERSON>, strtobool


@dataclass(frozen=True)
class GraphConfig(metaclass=Singleton):
    """Represent the config for MS Graph API."""

    client_id: str = env_field("GRAPH_CLIENT_ID")
    client_secret: str = env_field("GRAPH_CLIENT_SECRET")
    tenant_id: str = env_field("GRAPH_TENANT_ID")

    authority: str = env_field("GRAPH_ENDPOINT_FMB")
    scope: str = env_field("GRAPH_SCOPE")
    endpoint_graph: str = env_field("GRAPH_ENDPOINT")

    email: str = env_field("GRAPH_EMAIL")
    include_traceback: bool = env_field("GRAPH_INCLUDE_TRACEBACK", astype=strtobool, default="1")

    inbox_id: str = env_field("ID_INBOX")
    destination_folder_id: str = env_field("ID_DESTINATION_FOLDER")


@dataclass(frozen=True)
class SAPConfig(metaclass=Singleton):
    """Represent the configuration for SAP."""

    host: str = env_field("SAP_HOST")
    service_root: str = env_field("SAP_SERVICE_ROOT")
    incident_resource_path: str = env_field("SAP_INCIDENT_RESOURCE_PATH")
    user: str = env_field("SAP_USER")
    password: str = env_field("SAP_PASSWORD")

    workcenter: str = env_field("WORKCENTER")
    long_text: str = env_field("LONG_TEXT_FEEDBACK")
    confirmation_text: str = env_field("CONFIRMATION_TEXT_FEEDBACK")
    reason: str = "W001"
    status: str = "STUS"

    @property
    def incident_endpoint(self) -> str:
        """Return the sap endpoint."""
        return f"{self.host}{self.service_root}{self.incident_resource_path}"


@dataclass(frozen=True)
class Config(ServerConfig, metaclass=Singleton):
    """Represent the configuration for sap-client."""

    sap: SAPConfig = dataclass_field(SAPConfig)
    jwk_config: JWKConfig = dataclass_field(JWKConfig)
    graph_config: GraphConfig = dataclass_field(GraphConfig)


config = Config()
