import json
import tempfile
from pathlib import Path

import pytest
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from olympus_common.datareaders import <PERSON><PERSON>, ElasticReader, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LocalStorageReader


@pytest.fixture(autouse=True)
def _mock_elastic_env(mocker: Mo<PERSON>Fixture):
    """Mock os.environ for every test in this file."""
    dummy_env = {
        "ELASTIC_USER": "DummyUser",
        "ELASTIC_PASSWORD": "DummyPassword",
        "ELASTIC_INDEXES": '["dummy-index"]',
        "ELASTIC_SERVERS": (
            '["elastic-001.dummy.com:9200", "elastic-002.dummy.com:9200", "elastic-003.dummy.com:9200"]'
        ),
    }
    mocker.patch.dict("os.environ", dummy_env)


def test_elasticreader(mocker: MockerFixture):
    min_timestamp = 12345
    max_timestamp = 23456
    dummy_data = [{"_source": {"@timestamp": min_timestamp}}, {"_source": {"@timestamp": max_timestamp}}]
    mocker.patch("olympus_common.datareaders.elastic")  # Ensure no new additions will effectively call elastic.
    patched_read_elastic_index = mocker.patch("olympus_common.datareaders.elastic.read_elastic_index")
    patched_read_elastic_index.return_value = dummy_data
    with tempfile.TemporaryDirectory() as tmpdirname:
        tmpdirpath = Path(tmpdirname)
        checkpoint = Checkpoint(tmpdirpath)
        datareader = ElasticReader(
            {"bool": {"filter": [{"range": {"@timestamp": {"gte": "now-3d"}}}]}},
            ({"@timestamp": {"order": "asc"}}, {"_shard_doc": "desc"}),
            checkpoint=checkpoint,
        )
        for _ in range(2):  # Run twice to simulate a cold start run and a resuming run.
            data = datareader.read_data()
            assert data == dummy_data

            datareader.success(data)
            checkpointpath = checkpoint.fullpath
            assert checkpointpath.exists()
            assert checkpointpath.read_text() == str(max_timestamp)

            datareader.error(data, Exception("Dummy"))  # No assertions required.


def test_elasticreader_no_checkpoint(mocker: MockerFixture):
    dummy_data = [{"dummy": "data"}]
    mocker.patch("olympus_common.datareaders.elastic")  # Ensure no new additions will effectively call elastic.
    patched_read_elastic_index = mocker.patch("olympus_common.datareaders.elastic.read_elastic_index")
    patched_read_elastic_index.return_value = dummy_data
    with tempfile.TemporaryDirectory() as tmpdirname:
        tmpdirpath = Path(tmpdirname)
        datareader = ElasticReader(
            {"bool": {"filter": [{"range": {"@timestamp": {"gte": "now-3d"}}}]}},
            ({"@timestamp": {"order": "asc"}}, {"_shard_doc": "desc"}),
            checkpoint=None,
        )
        assert datareader.checkpoint is None
        data = datareader.read_data()
        assert data == dummy_data

        datareader.success(data)
        checkpointpath = tmpdirpath / "checkpoint.txt"
        assert not checkpointpath.exists()

        datareader.error(data, Exception("Dummy"))  # No assertions required.


def test_localstoragereader(mocker: MockerFixture):
    dummy_data = [{"a": 1}]
    dummy_data_str = json.dumps(dummy_data)
    mocker.patch("olympus_common.datareaders.elastic")  # Ensure no new additions will effectively call elastic.
    patched_read_elastic_index = mocker.patch("olympus_common.datareaders.elastic.read_elastic_index")
    patched_read_elastic_index.return_value = dummy_data
    with tempfile.TemporaryDirectory() as tmpdirname:
        tmpfile = Path(tmpdirname) / "data.json"
        tmpfile.write_text(dummy_data_str)
        datareader = LocalStorageReader(tmpfile)
        data = datareader.read_data()
        assert data == dummy_data

        datareader.success(data)  # No assertions required.
        datareader.error(data, Exception("Dummy"))  # No assertions required.


def test_kafkareader(mocker: MockerFixture):
    """Test that the KafkaReader behaves as expected."""
    datareader = KafkaReader()
    # Due to local imports, we have to read the
    consumer_patched = mocker.patch.object(datareader, "_consumer")

    consumer_patched.poll.return_value = DummyMessage({"dummy": "data"})
    assert consumer_patched.poll.call_count == 0, "Expected that the consumer's method was never called."
    data = datareader.read_data()
    assert consumer_patched.poll.call_count >= 1, "Expected datareader.read_data to call the consumer.poll."

    assert consumer_patched.commit.call_count == 0, "Excepted consumer.commit callcount to be 0."
    datareader.success(data)
    assert consumer_patched.commit.call_count == 1, "Excepted datareader.success to call consumer.commit."

    assert consumer_patched.close.call_count == 0, "Excepted consumer.close callcount to be 0."
    datareader.error(data, Exception("Dummy"))
    assert consumer_patched.close.call_count == 1, "Excepted datareader.error to call consumer.close."


class DummyMessage:
    """A dummy message class to simulate Kafka messages."""

    def __init__(self, value: str):
        self._value = json.dumps(value).encode("utf-8")

    def value(self):
        return self._value

    def error(self):
        return None

    def partition(self):
        return 0
