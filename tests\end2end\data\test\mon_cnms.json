{"extra_env": {"OLYMPUS_AGENT_ID": "4"}, "agent_ids": [4], "data_old": [{"input": {"type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "0", "message": "#<SNMP::SNMPv2_Trap:0x2fe18e14 @request_id=645282942, @error_index=0, @error_status=0, @source_ip=\"**************\", @varbind_list=[#<SNMP::VarBind:0xb70970 @name=[*******.*******.0], @value=#<SNMP::Integer:0x37e97b16 @value=0>>, #<SNMP::VarBind:0x14dbd2d1 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>]>", "@timestamp": "2023-09-21T23:41:44.897Z", "@version": "1", "snmptrap.description": null, "snmptrap.eventuei": null, "snmptrap.servicename": null, "snmptrap.severityname": null, "snmptrap.hostname": null, "snmptrap.counter": null, "snmptrap.operinstruct": null, "snmptrap.lasteventtime": null, "snmptrap.ipaddr": null, "snmptrap.alarmtype": null, "snmptrap.logmsg": null, "snmptrap.firsteventtime": null, "snmptrap.nodelabel": null, "snmptrap.alarmid": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.kafka.timestamp": "2023-09-21T23:41:44.998Z", "event.kafka.offset": 8232, "event.kafka.key": null, "event.logstash.instance_name": "iictniaplv031", "event.uuid": "545e8c35-08bf-461c-8e16-af8d06ab95f8"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "C-NMS_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "raise_time": "2025-01-01 12:00:01", "summary": "C-NMS Heartbeat Message", "severity": 1, "clear_time": null, "additional_data": null}]}}, {"frozen_time": "2025-01-01 12:00:01", "input": {"snmptrap": {"counter": "3", "operinstruct": "The alarm clears once the call processing application is in service.", "nodelabel": "DE2BEIFBH02", "alarmtype": "1", "eventuei": "uei/vnd/Kapsch/KSC", "firsteventtime": "07.01.2025 02:00:02.573", "description": "DE2BEIFBH02 MRF1307 2025-01-07T02:04:18+01:00 7603946 alarm-notification minor<br/>GU_Message_Routing_Function 0:0:2:0 mrf 2004-1-12-/=/v=1/sg-name=MRF--8-0<br/><br/>MRF Appl OOS. MRF Applicaton Out of Service<br/>Application state change: Running -> Restarting", "severityname": "MINOR", "ipaddr": "*************", "hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "alarmid": "840957", "lasteventtime": "07.01.2025 02:04:20.349", "servicename": "EMA-Ksc", "logmsg": "HLRS MRF1307 MRF Appl OOS (EM:DE2BEIFBH02 se:mrf entity:mrf app-info:2004-12 v:1 sg-name:MRF) processingErrorAlarm/other"}, "@timestamp": "2025-01-07T01:04:54.653668259Z", "SNMPv2-MIB::sysUpTime.0": "0", "event": {"uuid": "5ee3ab1f-7f87-446d-bde9-e2cc4e43c1e2", "kafka": {"partition": 0, "offset": 1024229, "topic": "a1927-gsmr_oss-cnms_events-prd", "consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "key": null, "timestamp": "2025-01-07T01:04:54.754Z"}, "logstash": {"instance_name": "iictmiapls016"}}, "message": "#<SNMP::InformRequest:0x1cfa9e39 @varbind_list=[#<SNMP::VarBind:0x20c869d6 @name=[*******.*******.0], @value=#<SNMP::Integer:0x763c6445 @value=0>>, #<SNMP::VarBind:0x3eff37ed @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x56c547f0 @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x35a00227 @name=[*******.4.1.20306.*******.2], @value=\"840957\">, #<SNMP::VarBind:0x202b569a @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x672c5d2d @name=[*******.4.1.20306.*******.4], @value=\"DE2BEIFBH02\">, #<SNMP::VarBind:0x1b2a7cff @name=[*******.4.1.20306.*******.5], @value=\"*************\">, #<SNMP::VarBind:0x2eaef487 @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0x54cbe84e @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x518b02cf @name=[*******.4.1.20306.*******.8], @value=\"3\">, #<SNMP::VarBind:0x190e4185 @name=[*******.4.1.20306.*******.9], @value=\"MINOR\">, #<SNMP::VarBind:0x405d84ef @name=[*******.4.1.20306.*******.10], @value=\"07.01.2025 02:00:02.573\">, #<SNMP::VarBind:0x4b5f9d32 @name=[*******.4.1.20306.*******.11], @value=\"07.01.2025 02:04:20.349\">, #<SNMP::VarBind:0x42cb63ec @name=[*******.4.1.20306.*******.12], @value=\"HLRS MRF1307 MRF Appl OOS (EM:DE2BEIFBH02 se:mrf entity:mrf app-info:2004-12 v:1 sg-name:MRF) processingErrorAlarm/other\">, #<SNMP::VarBind:0x29a21059 @name=[*******.4.1.20306.*******.13], @value=\"DE2BEIFBH02 MRF1307 2025-01-07T02:04:18+01:00 7603946 alarm-notification minor<br/>GU_Message_Routing_Function 0:0:2:0 mrf 2004-1-12-/=/v=1/sg-name=MRF--8-0<br/><br/>MRF Appl OOS. MRF Applicaton Out of Service<br/>Application state change: Running -> Restarting\">, #<SNMP::VarBind:0x574837e @name=[*******.4.1.20306.*******.14], @value=\"The alarm clears once the call processing application is in service.\">], @request_id=*********, @error_index=0, @error_status=0, @source_ip=\"************\">", "host": "************", "@version": "1", "type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******"}, "output": {"s2110_alarm": [{"ci_id": "DE2BEIFBH02"}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "DE2BEIFBH02"}]}}, {"frozen_time": "2025-01-02 12:00:01", "input": {"snmptrap": {"counter": "1", "operinstruct": "Repair action required.", "nodelabel": "DE2BEIFBH02", "alarmtype": "2", "eventuei": "uei/vnd/Kapsch/KSC", "firsteventtime": "07.01.2025 05:29:26.387", "description": "DE2BEIFBH02 USM1304 2025-01-07T05:29:24+01:00 7604455 alarm-notification cleared<br/>USM 0:0:3:0 backup 1004-0-2-/=/v=1/backupid=all--31-1<br/><br/>USM backup alarm. <br/>Backup process is finished", "severityname": "NORMAL", "ipaddr": "*************", "hostname": "de1cnms1.de1coam.gsmr.infrabel.be", "alarmid": "841029", "lasteventtime": "07.01.2025 05:29:26.387", "servicename": "EMA-Ksc", "logmsg": "HLRS USM1304 USM backup alarm (EM:DE2BEIFBH02 se:usm entity:backup app-info:1004-2 v:1 backupid:all) other/other"}, "@timestamp": "2025-01-07T04:30:04.244137969Z", "SNMPv2-MIB::sysUpTime.0": "0", "event": {"uuid": "929231c0-c981-46b5-a5fc-11811fa557d1", "kafka": {"partition": 0, "offset": 1024395, "topic": "a1927-gsmr_oss-cnms_events-prd", "consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "key": null, "timestamp": "2025-01-07T04:30:04.350Z"}, "logstash": {"instance_name": "iictmiapls016"}}, "message": "#<SNMP::InformRequest:0x7563c52a @varbind_list=[#<SNMP::VarBind:0x326066ba @name=[*******.*******.0], @value=#<SNMP::Integer:0x754c229 @value=0>>, #<SNMP::VarBind:0x6cf50d7c @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x26abb142 @name=[*******.4.1.20306.*******.1], @value=\"de1cnms1.de1coam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x17cf469b @name=[*******.4.1.20306.*******.2], @value=\"841029\">, #<SNMP::VarBind:0x36ab9bc1 @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x72f1fe85 @name=[*******.4.1.20306.*******.4], @value=\"DE2BEIFBH02\">, #<SNMP::VarBind:0x92b7038 @name=[*******.4.1.20306.*******.5], @value=\"*************\">, #<SNMP::VarBind:0x643d0418 @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0xea2eecd @name=[*******.4.1.20306.*******.7], @value=\"2\">, #<SNMP::VarBind:0x735a2c4a @name=[*******.4.1.20306.*******.8], @value=\"1\">, #<SNMP::VarBind:0x212238a9 @name=[*******.4.1.20306.*******.9], @value=\"NORMAL\">, #<SNMP::VarBind:0x2db3f551 @name=[*******.4.1.20306.*******.10], @value=\"07.01.2025 05:29:26.387\">, #<SNMP::VarBind:0x34fea2ac @name=[*******.4.1.20306.*******.11], @value=\"07.01.2025 05:29:26.387\">, #<SNMP::VarBind:0x1664f005 @name=[*******.4.1.20306.*******.12], @value=\"HLRS USM1304 USM backup alarm (EM:DE2BEIFBH02 se:usm entity:backup app-info:1004-2 v:1 backupid:all) other/other\">, #<SNMP::VarBind:0x26336510 @name=[*******.4.1.20306.*******.13], @value=\"DE2BEIFBH02 USM1304 2025-01-07T05:29:24+01:00 7604455 alarm-notification cleared<br/>USM 0:0:3:0 backup 1004-0-2-/=/v=1/backupid=all--31-1<br/><br/>USM backup alarm. <br/>Backup process is finished\">, #<SNMP::VarBind:0x149815fa @name=[*******.4.1.20306.*******.14], @value=\"Repair action required.\">], @request_id=1594761328, @error_index=0, @error_status=0, @source_ip=\"************\">", "host": "************", "@version": "1", "type": "snmp_trap", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "DE2BEIFBH02"}]}}], "data": [{"frozen_time": "2025-02-11T07:29:17", "input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "SNMPv2-MIB::sysUpTime.0": "0", "@timestamp": "2025-02-11T07:29:17.107451326Z", "message": "#<SNMP::SNMPv2_Trap:0x4152bcb8 @varbind_list=[#<SNMP::VarBind:0x43ae159d @name=[*******.*******.0], @value=#<SNMP::Integer:0x5211139f @value=0>>, #<SNMP::VarBind:0x672c219 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>], @request_id=332721917, @error_index=0, @error_status=0, @source_ip=\"************\">", "host": "************", "type": "snmp_trap", "@version": "1", "snmptrap.counter": null, "snmptrap.description": null, "snmptrap.nodelabel": null, "snmptrap.operinstruct": null, "snmptrap.ipaddr": null, "snmptrap.alarmtype": null, "snmptrap.eventuei": null, "snmptrap.severityname": null, "snmptrap.hostname": null, "snmptrap.logmsg": null, "snmptrap.alarmid": null, "snmptrap.lasteventtime": null, "snmptrap.firsteventtime": null, "snmptrap.servicename": null, "event.kafka.timestamp": "2025-02-11T07:29:17.207Z", "event.kafka.offset": 1030022, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.uuid": "8f1c167d-393a-46ac-bbfa-d054fa3a740b", "event.logstash.instance_name": "iictmiapls016"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "C-NMS_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "C-NMS Heartbeat Message", "clear_time": null, "severity": 1, "additional_data": null}]}}, {"frozen_time": "2025-02-11T09:30:53", "input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "SNMPv2-MIB::sysUpTime.0": "0", "@timestamp": "2025-02-11T09:30:53.198691778Z", "message": "#<SNMP::InformRequest:0x710a2459 @varbind_list=[#<SNMP::VarBind:0x131b2e99 @name=[*******.*******.0], @value=#<SNMP::Integer:0x1f816cb0 @value=0>>, #<SNMP::VarBind:0x3f09abf5 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x43507913 @name=[*******.4.1.20306.*******.1], @value=\"muicnms1.muicoam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x975e806 @name=[*******.4.1.20306.*******.2], @value=\"1190273\">, #<SNMP::VarBind:0x1d85f174 @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x6f21b732 @name=[*******.4.1.20306.*******.4], @value=\"MONBEIFBM02\">, #<SNMP::VarBind:0x2ff9c324 @name=[*******.4.1.20306.*******.5], @value=\"************\">, #<SNMP::VarBind:0x9ea2b05 @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0x3b56751d @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x59acdd62 @name=[*******.4.1.20306.*******.8], @value=\"11\">, #<SNMP::VarBind:0x38adc590 @name=[*******.4.1.20306.*******.9], @value=\"MAJOR\">, #<SNMP::VarBind:0x60477383 @name=[*******.4.1.20306.*******.10], @value=\"09.02.2025 19:00:20.801\">, #<SNMP::VarBind:0x68b11c0b @name=[*******.4.1.20306.*******.11], @value=\"09.02.2025 19:00:21.049\">, #<SNMP::VarBind:0x57499bb2 @name=[*******.4.1.20306.*******.12], @value=\"MSCS MtcAe1302 Aggregate Ethernet interface degraded (EM:MONBEIFBM02 se:mtcae entity:ae app-info:1007-1 v:1 frame:0 shelf:0 slot:3 sub-slot:0 if-number:601) equipmentAlarm/equipmentMalfunction\">, #<SNMP::VarBind:0x19e458e @name=[*******.4.1.20306.*******.13], @value=\"MONBEIFBM02 MtcAe1302 2025-01-30T20:46:22+01:00 3533920 alarm-notification major<br/>System Maintenance 0:0:3:0 ae 1007-0-1-/=/v=1/frame=0/shelf=0/slot=3/sub-slot=0/if-number=601-serverEid,0:0:3:0:10:0,entityType,20,instance,601-253-1<br/><br/>Aggregate Ethernet interface degraded. <br/>Aggregate ethernet interface is degraded.\">, #<SNMP::VarBind:0x5a1ee555 @name=[*******.4.1.20306.*******.14], @value=\"Repair member Ethernet interfaces. Contact your next level of support.\">], @request_id=332723398, @error_index=0, @error_status=0, @source_ip=\"************\">", "host": "************", "type": "snmp_trap", "@version": "1", "snmptrap.counter": "11", "snmptrap.description": "MONBEIFBM02 MtcAe1302 2025-01-30T20:46:22+01:00 3533920 alarm-notification major<br/>System Maintenance 0:0:3:0 ae 1007-0-1-/=/v=1/frame=0/shelf=0/slot=3/sub-slot=0/if-number=601-serverEid,0:0:3:0:10:0,entityType,20,instance,601-253-1<br/><br/>Aggregate Ethernet interface degraded. <br/>Aggregate ethernet interface is degraded.", "snmptrap.nodelabel": "MONBEIFBM02", "snmptrap.operinstruct": "Repair member Ethernet interfaces. Contact your next level of support.", "snmptrap.ipaddr": "************", "snmptrap.alarmtype": "1", "snmptrap.eventuei": "uei/vnd/Kapsch/KSC", "snmptrap.severityname": "MAJOR", "snmptrap.hostname": "muicnms1.muicoam.gsmr.infrabel.be", "snmptrap.logmsg": "MSCS MtcAe1302 Aggregate Ethernet interface degraded (EM:MONBEIFBM02 se:mtcae entity:ae app-info:1007-1 v:1 frame:0 shelf:0 slot:3 sub-slot:0 if-number:601) equipmentAlarm/equipmentMalfunction", "snmptrap.alarmid": "1190273", "snmptrap.lasteventtime": "09.02.2025 19:00:21.049", "snmptrap.firsteventtime": "09.02.2025 19:00:20.801", "snmptrap.servicename": "EMA-Ksc", "event.kafka.timestamp": "2025-02-11T09:30:53.300Z", "event.kafka.offset": 1031503, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.uuid": "d9a4ec8c-04a9-40be-a5ab-8181d0ba75dc", "event.logstash.instance_name": "iictmiapls016"}, "output": {"s2110_alarm": [{"ci_id": "MONBEIFBM02", "metric_name": "", "metric_type": "", "event_id": "uei/vnd/Kapsch/KSC", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "MONBEIFBM02", "metric_name": "", "metric_type": "", "summary": "MSCS MtcAe1302 Aggregate Ethernet interface degraded (EM:MONBEIFBM02 se:mtcae entity:ae app-info:1007-1 v:1 frame:0 shelf:0 slot:3 sub-slot:0 if-number:601) equipmentAlarm/equipmentMalfunction", "clear_time": null, "severity": 4, "additional_data": {"alarm_id": "MtcAe1302"}}]}}, {"frozen_time": "2025-02-12T00:37:07", "input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "SNMPv2-MIB::sysUpTime.0": "0", "@timestamp": "2025-02-12T00:37:07.422851552Z", "message": "#<SNMP::InformRequest:0x5712cf77 @varbind_list=[#<SNMP::VarBind:0x19554f40 @name=[*******.*******.0], @value=#<SNMP::Integer:0x312ec182 @value=0>>, #<SNMP::VarBind:0x7086d9a7 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x2b0a2914 @name=[*******.4.1.20306.*******.1], @value=\"muicnms1.muicoam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x18965c7a @name=[*******.4.1.20306.*******.2], @value=\"1190285\">, #<SNMP::VarBind:0x5ff3ac3b @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x197e0d83 @name=[*******.4.1.20306.*******.4], @value=\"MONBEIFBM02\">, #<SNMP::VarBind:0x63c6d531 @name=[*******.4.1.20306.*******.5], @value=\"************\">, #<SNMP::VarBind:0x491920bf @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0xb49dfd3 @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x6232d92c @name=[*******.4.1.20306.*******.8], @value=\"4\">, #<SNMP::VarBind:0x3708e706 @name=[*******.4.1.20306.*******.9], @value=\"MINOR\">, #<SNMP::VarBind:0x37142580 @name=[*******.4.1.20306.*******.10], @value=\"09.02.2025 19:00:22.204\">, #<SNMP::VarBind:0x72e6d3f6 @name=[*******.4.1.20306.*******.11], @value=\"09.02.2025 19:00:22.501\">, #<SNMP::VarBind:0x6e99f3f @name=[*******.4.1.20306.*******.12], @value=\"MSCS NTP1300 Network time protocol (EM:MONBEIFBM02 se:ntp entity:system app-info:1023-1 ref-location:0-0-6-0 ref-info:ntp frame:0 shelf:0 slot:6 subslot:0) qualityOfServiceAlarm/other\">, #<SNMP::VarBind:0x76a96833 @name=[*******.4.1.20306.*******.13], @value=\"MONBEIFBM02 NTP1300 2025-01-30T20:50:01+01:00 3534412 alarm-notification minor<br/>ntp 0:0:6:0 system 1023-0-1--ntp/frame=0/shelf=0/slot=6/subslot=0-300-1<br/><br/>Network time protocol. <br/>Trouble: NTP is not synchronized with some servers/peers; contact next level of support\">, #<SNMP::VarBind:0x60076b43 @name=[*******.4.1.20306.*******.14], @value=\"Please see alarm reason field.\">], @request_id=332734434, @error_index=0, @error_status=0, @source_ip=\"************\">", "host": "************", "type": "snmp_trap", "@version": "1", "snmptrap.counter": "4", "snmptrap.description": "MONBEIFBM02 NTP1300 2025-01-30T20:50:01+01:00 3534412 alarm-notification minor<br/>ntp 0:0:6:0 system 1023-0-1--ntp/frame=0/shelf=0/slot=6/subslot=0-300-1<br/><br/>Network time protocol. <br/>Trouble: NTP is not synchronized with some servers/peers; contact next level of support", "snmptrap.nodelabel": "MONBEIFBM02", "snmptrap.operinstruct": "Please see alarm reason field.", "snmptrap.ipaddr": "************", "snmptrap.alarmtype": "1", "snmptrap.eventuei": "uei/vnd/Kapsch/KSC", "snmptrap.severityname": "MINOR", "snmptrap.hostname": "muicnms1.muicoam.gsmr.infrabel.be", "snmptrap.logmsg": "MSCS NTP1300 Network time protocol (EM:MONBEIFBM02 se:ntp entity:system app-info:1023-1 ref-location:0-0-6-0 ref-info:ntp frame:0 shelf:0 slot:6 subslot:0) qualityOfServiceAlarm/other", "snmptrap.alarmid": "1190285", "snmptrap.lasteventtime": "09.02.2025 19:00:22.501", "snmptrap.firsteventtime": "09.02.2025 19:00:22.204", "snmptrap.servicename": "EMA-Ksc", "event.kafka.timestamp": "2025-02-12T00:37:07.523Z", "event.kafka.offset": 1042539, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.uuid": "1ee7f7fb-f11b-4a26-9fa2-50c985668d15", "event.logstash.instance_name": "iictmiapls016"}, "output": {"s2110_occurrence": [{"event_type": "problem", "ci_id": "MONBEIFBM02", "metric_name": "", "metric_type": "", "summary": "MSCS NTP1300 Network time protocol (EM:MONBEIFBM02 se:ntp entity:system app-info:1023-1 ref-location:0-0-6-0 ref-info:ntp frame:0 shelf:0 slot:6 subslot:0) qualityOfServiceAlarm/other", "clear_time": null, "severity": 3, "additional_data": {"alarm_id": "NTP1300"}}]}}, {"frozen_time": "2025-02-12T01:00:10", "input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "SNMPv2-MIB::sysUpTime.0": "0", "@timestamp": "2025-02-12T01:00:10.142618479Z", "message": "#<SNMP::InformRequest:0x3f64a020 @varbind_list=[#<SNMP::VarBind:0x7f148f13 @name=[*******.*******.0], @value=#<SNMP::Integer:0x55f7a31c @value=0>>, #<SNMP::VarBind:0x4f886c22 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x3a828697 @name=[*******.4.1.20306.*******.1], @value=\"muicnms1.muicoam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x6135fac3 @name=[*******.4.1.20306.*******.2], @value=\"1190253\">, #<SNMP::VarBind:0x9324aba @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x5d7dec2 @name=[*******.4.1.20306.*******.4], @value=\"MONBEIFBM02\">, #<SNMP::VarBind:0x2b4f56d1 @name=[*******.4.1.20306.*******.5], @value=\"************\">, #<SNMP::VarBind:0x65e25299 @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0x7e1ea305 @name=[*******.4.1.20306.*******.7], @value=\"2\">, #<SNMP::VarBind:0x458453ca @name=[*******.4.1.20306.*******.8], @value=\"12\">, #<SNMP::VarBind:0x142c7c75 @name=[*******.4.1.20306.*******.9], @value=\"NORMAL\">, #<SNMP::VarBind:0x632d92f3 @name=[*******.4.1.20306.*******.10], @value=\"09.02.2025 19:00:05.740\">, #<SNMP::VarBind:0x68201318 @name=[*******.4.1.20306.*******.11], @value=\"09.02.2025 19:00:24.255\">, #<SNMP::VarBind:0xf5d07d5 @name=[*******.4.1.20306.*******.12], @value=\"MSCS MSC1302 MSCCA RExTst (EM:MONBEIFBM02 se:msc entity:mscca app-info:2005-12 v:1 sg-name:MSC) other/other\">, #<SNMP::VarBind:0x78e51d9f @name=[*******.4.1.20306.*******.13], @value=\"MONBEIFBM02 MSC1302 2025-02-01T01:39:33+01:00 3536313 alarm-notification cleared<br/>GU_MSC_Call_Agent 0:0:5:0 mscca 2005-1-12-/=/v=1/sg-name=MSC--3-1<br/><br/>MSCCA RExTst. Routine Exercise Test (RExTst) in progress<br/>Alarm cleared\">, #<SNMP::VarBind:0x7750a27a @name=[*******.4.1.20306.*******.14], @value=\"No repair action required.\">], @request_id=332734715, @error_index=0, @error_status=0, @source_ip=\"************\">", "host": "************", "type": "snmp_trap", "@version": "1", "snmptrap.counter": "12", "snmptrap.description": "MONBEIFBM02 MSC1302 2025-02-01T01:39:33+01:00 3536313 alarm-notification cleared<br/>GU_MSC_Call_Agent 0:0:5:0 mscca 2005-1-12-/=/v=1/sg-name=MSC--3-1<br/><br/>MSCCA RExTst. Routine Exercise Test (RExTst) in progress<br/>Alarm cleared", "snmptrap.nodelabel": "MONBEIFBM02", "snmptrap.operinstruct": "No repair action required.", "snmptrap.ipaddr": "************", "snmptrap.alarmtype": "2", "snmptrap.eventuei": "uei/vnd/Kapsch/KSC", "snmptrap.severityname": "NORMAL", "snmptrap.hostname": "muicnms1.muicoam.gsmr.infrabel.be", "snmptrap.logmsg": "MSCS MSC1302 MSCCA RExTst (EM:MONBEIFBM02 se:msc entity:mscca app-info:2005-12 v:1 sg-name:MSC) other/other", "snmptrap.alarmid": "1190253", "snmptrap.lasteventtime": "09.02.2025 19:00:24.255", "snmptrap.firsteventtime": "09.02.2025 19:00:05.740", "snmptrap.servicename": "EMA-Ksc", "event.kafka.timestamp": "2025-02-12T01:00:10.245Z", "event.kafka.offset": 1042820, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.uuid": "68a31d19-9958-4c68-bc58-0eb42e7d57e0", "event.logstash.instance_name": "iictmiapls016"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "MONBEIFBM02", "metric_name": "", "metric_type": "", "summary": "MSCS MSC1302 MSCCA RExTst (EM:MONBEIFBM02 se:msc entity:mscca app-info:2005-12 v:1 sg-name:MSC) other/other", "clear_time": "2025-02-12T01:00:10", "severity": 0, "additional_data": {"alarm_id": "MSC1302"}}]}}, {"frozen_time": "2025-02-12T01:11:11", "input": {"SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.20306.*******", "SNMPv2-MIB::sysUpTime.0": "0", "@timestamp": "2025-02-12T01:11:11.267163149Z", "message": "#<SNMP::InformRequest:0x62ddd569 @varbind_list=[#<SNMP::VarBind:0x2239d2e0 @name=[*******.*******.0], @value=#<SNMP::Integer:0x5fc1f15b @value=0>>, #<SNMP::VarBind:0x53bb0157 @name=[*******.*******.4.1.0], @value=[*******.4.1.20306.*******]>, #<SNMP::VarBind:0x31d6d518 @name=[*******.4.1.20306.*******.1], @value=\"muicnms1.muicoam.gsmr.infrabel.be\">, #<SNMP::VarBind:0x5d14af10 @name=[*******.4.1.20306.*******.2], @value=\"1190263\">, #<SNMP::VarBind:0xd8fc6c0 @name=[*******.4.1.20306.*******.3], @value=\"uei/vnd/Kapsch/KSC\">, #<SNMP::VarBind:0x635df9f @name=[*******.4.1.20306.*******.4], @value=\"MONBEIFBM02\">, #<SNMP::VarBind:0x1cca454 @name=[*******.4.1.20306.*******.5], @value=\"************\">, #<SNMP::VarBind:0x7958e752 @name=[*******.4.1.20306.*******.6], @value=\"EMA-Ksc\">, #<SNMP::VarBind:0xa12877f @name=[*******.4.1.20306.*******.7], @value=\"1\">, #<SNMP::VarBind:0x22f36885 @name=[*******.4.1.20306.*******.8], @value=\"7\">, #<SNMP::VarBind:0x4679868b @name=[*******.4.1.20306.*******.9], @value=\"WARNING\">, #<SNMP::VarBind:0x78259b61 @name=[*******.4.1.20306.*******.10], @value=\"09.02.2025 19:00:11.142\">, #<SNMP::VarBind:0x6692c265 @name=[*******.4.1.20306.*******.11], @value=\"09.02.2025 19:00:26.117\">, #<SNMP::VarBind:0x5f9c2972 @name=[*******.4.1.20306.*******.12], @value=\"MSCS NTP1300 Network time protocol (EM:MONBEIFBM02 se:ntp entity:system app-info:1023-1 ref-location:0-0-2-0 ref-info:ntp frame:0 shelf:0 slot:2 subslot:0) qualityOfServiceAlarm/other\">, #<SNMP::VarBind:0x555bb1d4 @name=[*******.4.1.20306.*******.13], @value=\"MONBEIFBM02 NTP1300 2025-02-03T01:42:47+01:00 3539012 alarm-notification warning<br/>ntp 0:0:2:0 system 1023-0-1--ntp/frame=0/shelf=0/slot=2/subslot=0-300-1<br/><br/>Network time protocol. <br/>Synchronizing: Out-of-service condition for time-sync: re-reading NTP configuration; no corrective action required\">, #<SNMP::VarBind:0x1f1c68d @name=[*******.4.1.20306.*******.14], @value=\"Please see alarm reason field.\">], @request_id=332734849, @error_index=0, @error_status=0, @source_ip=\"************\">", "host": "************", "type": "snmp_trap", "@version": "1", "snmptrap.counter": "7", "snmptrap.description": "MONBEIFBM02 NTP1300 2025-02-03T01:42:47+01:00 3539012 alarm-notification warning<br/>ntp 0:0:2:0 system 1023-0-1--ntp/frame=0/shelf=0/slot=2/subslot=0-300-1<br/><br/>Network time protocol. <br/>Synchronizing: Out-of-service condition for time-sync: re-reading NTP configuration; no corrective action required", "snmptrap.nodelabel": "MONBEIFBM02", "snmptrap.operinstruct": "Please see alarm reason field.", "snmptrap.ipaddr": "************", "snmptrap.alarmtype": "1", "snmptrap.eventuei": "uei/vnd/Kapsch/KSC", "snmptrap.severityname": "WARNING", "snmptrap.hostname": "muicnms1.muicoam.gsmr.infrabel.be", "snmptrap.logmsg": "MSCS NTP1300 Network time protocol (EM:MONBEIFBM02 se:ntp entity:system app-info:1023-1 ref-location:0-0-2-0 ref-info:ntp frame:0 shelf:0 slot:2 subslot:0) qualityOfServiceAlarm/other", "snmptrap.alarmid": "1190263", "snmptrap.lasteventtime": "09.02.2025 19:00:26.117", "snmptrap.firsteventtime": "09.02.2025 19:00:11.142", "snmptrap.servicename": "EMA-Ksc", "event.kafka.timestamp": "2025-02-12T01:11:11.377Z", "event.kafka.offset": 1042954, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1927-gsmr_oss-cnms_events-prd", "event.kafka.topic": "a1927-gsmr_oss-cnms_events-prd", "event.uuid": "b45a3451-f4a2-4bc1-9729-5529af6c595f", "event.logstash.instance_name": "iictmiapls016"}, "output": {"s2110_occurrence": [{"event_type": "problem", "ci_id": "MONBEIFBM02", "metric_name": "", "metric_type": "", "summary": "MSCS NTP1300 Network time protocol (EM:MONBEIFBM02 se:ntp entity:system app-info:1023-1 ref-location:0-0-2-0 ref-info:ntp frame:0 shelf:0 slot:2 subslot:0) qualityOfServiceAlarm/other", "clear_time": null, "severity": 2, "additional_data": {"alarm_id": "NTP1300"}}]}}]}