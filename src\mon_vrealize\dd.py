"""Detail Design module for mon-vrealize."""

import json
from datetime import datetime, timedelta, timezone
from typing import overload

import pandas as pd

from mon_vrealize import statics
from olympus_common import enums, utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Run the dd-script."""
    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type() -> str:
    """Return the clear type relative to the DD."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the manager relative to the DD."""
    return "mon-vrealize"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the scope relative to the DD."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level() -> str:
    """Return the top level of the DD."""
    return "A1232"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm(row: pd.Series) -> bool | None:
    """Return the actionable value corresponding to the event."""
    if _is_heartbeat(row):
        return False
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Return the ci_id relative to the event."""
    entity_name_: str = row["alert_entity_name"]
    if _is_heartbeat(row):
        return row["alert_alive_server_name"]
    elif entity_name_.endswith(".msnet.railb.be"):
        return entity_name_.split(".msnet.railb.be", maxsplit=1)[0]
    else:
        return entity_name_


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_id(row: pd.Series) -> str:
    """Return the event_id of the event."""
    return row["alert_id"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _extended_attributes(row: pd.Series) -> str:
    """Return the extended attributes relatives to the event."""
    server_name: str = row["alert_alive_server_name"]
    if _is_heartbeat(row):
        return json.dumps({"collection": server_name, "alert_message": row["alert_message"]})
    else:
        return json.dumps({"collection": server_name, "delay": _delay(row), "alert_message": row["alert_message"]})


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type(row: pd.Series) -> str:
    """Return the metric type relative to the event."""
    return statics.METRIC_TYPE_DICT[row["alert_definition_name"]]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Return the metric name relative to the event."""
    return statics.METRIC_NAME_DICT[row["alert_definition_name"]]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Return the node relative to the event."""
    return _ci_id(row)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str:
    """Return the node alias relative to the event."""
    return _node(row) if _is_heartbeat(row) else row["alert_entity_name"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime:
    """Return the raise time of the event."""
    return datetime.fromtimestamp(int(row["alert_timestamp"][:10]), tz=timezone.utc).replace(tzinfo=None)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Return the node of the event."""
    if _is_heartbeat(row):
        return enums.Severity.INDETERMINATE.value
    elif _event_type(row) == enums.AlarmType.PROBLEM.value:
        return (
            enums.Severity.CRITICAL.value
            if row["alert_criticality"] in ["critical", "immediate"]
            else enums.Severity.INDETERMINATE.value
        )
    else:
        return enums.Severity.INDETERMINATE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Return the summary relative to the event."""
    if _is_heartbeat(row):
        return "vRealize Heartbeat Message"
    else:
        return row["alert_definition_description"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _url(row: pd.Series) -> str:
    """Return the url relative to the event."""
    return row["alert_url"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_type(row: pd.Series) -> str:
    """Return alarm type of the event (clear or problem)."""
    if row["trap_type"] == statics.TRAP_PROBLEM_CLEAR:
        return enums.AlarmType.RESOLUTION.value
    else:
        return enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime | None:
    """Return the clear time of the event.

    This is equal to the raise time if the event is a clear else this is equal to 0.
    """
    return _raise_time(row) if _event_type(row) == enums.AlarmType.RESOLUTION.value else None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _delay(row: pd.Series) -> int:
    """Return the delay for the events."""
    return statics.GLOBAL_DELAY if not _is_heartbeat(row) else 0


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime:
    """Return the wake-up time."""
    return _raise_time(row) + timedelta(0, _delay(row))


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time() -> datetime:
    """Create the handle time for the event."""
    return utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Add all the DDs fields to the given DataFrame and return it."""
    # Filter the DataFrame in order to keep only monitored alarms
    df_records = df.loc[df["alert_definition_name"].isin(statics.ALARMS_FILTER) | _is_heartbeat(df)].reindex()

    if df_records.empty:
        return df_records

    # Set hardcoded data
    df_records["agent_id"] = agent_id
    df_records["clear_type"] = _clear_type()
    df_records["manager"] = _manager()
    df_records["action_class"] = _scope()
    df_records["handle_time"] = _handle_time()
    df_records["top_level"] = _top_level()

    # Set data from the dd's instructions
    df_records["actionable_alarm"] = df_records.apply(_actionable_alarm, axis=1)
    df_records["ci_id"] = df_records.apply(_ci_id, axis=1)
    df_records["event_id"] = df_records.apply(_event_id, axis=1)
    df_records["additional_data"] = df_records.apply(_extended_attributes, axis=1)
    df_records["metric_type"] = df_records.apply(_metric_type, axis=1)
    df_records["metric_name"] = df_records.apply(_metric_name, axis=1)
    df_records["node"] = df_records.apply(_node, axis=1)
    df_records["node_alias"] = df_records.apply(_node_alias, axis=1)
    df_records["raise_time"] = df_records.apply(_raise_time, axis=1)
    df_records["clear_time"] = df_records.apply(_clear_time, axis=1)
    df_records["severity"] = df_records.apply(_severity, axis=1)
    df_records["summary"] = df_records.apply(_summary, axis=1)
    df_records["url"] = df_records.apply(_url, axis=1)
    df_records["event_type"] = df_records.apply(_event_type, axis=1)
    df_records["wake_up_time"] = df_records.apply(_wake_up_time, axis=1)

    return df_records


@overload
def _is_heartbeat(row: pd.Series) -> bool: ...


@overload
def _is_heartbeat(row: pd.DataFrame) -> pd.Series: ...


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _is_heartbeat(row: pd.Series | pd.DataFrame) -> bool | pd.Series:
    """Return True if the condition for the heartbeat is validated else False."""
    return statics.HEARTBEAT_DEFINITION_NAME in row["alert_definition_name"]
