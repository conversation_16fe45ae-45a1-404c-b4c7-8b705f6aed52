"""Datawriters for ing-zabbix."""

import json
import logging
from dataclasses import dataclass

from confluent_kafka import Producer

from olympus_common.config import KafkaProducerConfig
from olympus_common.dataclass import dataclass_field
from olympus_common.datawriters import DataWriter
from olympus_common.elastic_apm import CaptureSpan, trace_scan
from olympus_common.enums import MeasureType


@dataclass
class KafkaWriter(DataWriter):
    """Represent a KafkaWriter.

    This concrete DataWriter publishes the data to kafka on success and closes the producer on error.
    """

    config: KafkaProducerConfig = dataclass_field(KafkaProducerConfig)

    _producer: Producer | None = None

    @trace_scan(transaction_type=MeasureType.KAFKA.value)
    def success(self, results: list[dict]) -> None:
        """Print data on success."""
        topics = ", ".join(self.config.topics)
        with CaptureSpan(
            name=f"Kafka PRODUCE to {topics}", span_type="messaging", span_subtype="kafka", span_action="poll"
        ) as span:
            if span:
                span.context["destination"] = {
                    "service": {"resource": f"kafka/{topics}", "name": "kafka", "type": "messaging"}
                }
            self._produce_data(results)

    @CaptureSpan(span_type=MeasureType.KAFKA.value)
    def _produce_data(self, results):
        for json_message in results:
            json_message_dumped = json.dumps(json_message).encode(self.config.message_encoding)
            for topic in self.config.topics:  # Support sending to multiple topics
                self.producer.produce(topic, json_message_dumped, on_delivery=delivery_report)
        self.producer.flush()

    @trace_scan(transaction_type=MeasureType.KAFKA.value)
    def error(self, _: list[dict], __: Exception) -> None:
        """Reset the producer to force the creation of a new one for the next run."""
        self.producer.flush(self.config.flush_timeout)
        self._producer = None

    @property
    @CaptureSpan(span_type=MeasureType.KAFKA.value)
    def producer(self):
        """Return the producer, creating it if it doesn't exist."""
        if not self._producer:
            conf = {
                "bootstrap.servers": ",".join(self.config.bootstrap_servers),
                "security.protocol": self.config.security_protocol,
                "sasl.mechanism": self.config.sasl_mechanism,
                "sasl.username": self.config.user.lower(),
                "sasl.password": self.config.password,
                "max.in.flight.requests.per.connection": self.config.max_in_flight_requests_per_connection,
                "retries": self.config.retries,
            }
            self._producer = Producer(conf)
        return self._producer


def delivery_report(err, msg):
    """Indicate delivery result."""
    if err:
        logging.error(f"Message delivery failed: {err} | msg: {msg.value()[:200]}...")
    else:
        logging.info(f"Message delivered to {msg.topic()} [{msg.partition()}]")
