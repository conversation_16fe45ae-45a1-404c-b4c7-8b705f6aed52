"""Module to provide different DataWriters used in the olympus project."""

import json
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import TypeVar

from olympus_common import db
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType


class DataWriter(ABC):
    """Represent the base DataWriter."""

    @abstractmethod
    def success(self, results: list[dict]) -> None:
        """Define what to do with the provided result.

        This function should be called in Application._run when the wrapped function returns without error.
        """
        pass

    @abstractmethod
    def error(self, data: list[dict], exc: Exception) -> None:
        """Define what to do when the retrieved data was NOT parsed successfully.

        This function should be called in Application._run when the wrapped function returns with an error.
        """
        pass


@dataclass
class DatabaseWriter(DataWriter):
    """Represent a DatabaseWriter."""

    session_manager: db.SessionManager
    object_class: type[db.Enrichment | db.Occurrence] = db.Occurrence

    @trace_scan(transaction_type=MeasureType.DB_QUERY.value)
    def success(self, results: list[dict]) -> None:
        """Write the results to the database."""
        with self.session_manager.begin() as session:
            self.object_class.insert_many(data=results, session=session)

    def error(self, _: list[dict], __: Exception) -> None:
        """Do nothing on error."""


@dataclass
class LocalStorageWriter(DataWriter):
    """Represent a LocalStorageWriter.

    This concrete DataWriter writes the data to `self.filepath` on success and prints on error.
    """

    filepath: Path

    def __post_init__(self):
        """Ensure `self.filepath` exists right after creating an instance of `self`.

        References
        ----------
        https://docs.python.org/3/library/dataclasses.html#post-init-processing
        """
        self.filepath.parent.mkdir(exist_ok=True, parents=True)

    def success(self, results: list[dict]) -> None:
        """Print data on success."""
        dumps = json.dumps(results)
        self.filepath.write_text(dumps)
        print(self, "success", results)

    def error(self, data: list[dict], exc: Exception) -> None:
        """Print data on error."""
        print(self, "error", data, exc)


TDataWriter = TypeVar("TDataWriter", bound=DataWriter)
