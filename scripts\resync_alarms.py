"""Script to resync alarms from Olympus to Icinga."""

import psycopg

from olympus_common import db
from olympus_common.dataclass import env_field

# If specific alarm IDs need to be resynced, paste them in alarm_ids.
# Otherwise all the alarms from Icinga will be resynced.
alarm_ids: list[int] = []

if not alarm_ids:
    icinga_db_host: str = env_field("ICINGA_DB_HOST")
    icinga_db_port: int = env_field("ICINGA_DB_PORT", astype=int)
    icinga_db_name: str = env_field("ICINGA_DB_NAME")
    icinga_db_user: str = env_field("ICINGA_DB_USER")
    icinga_db_password: str = env_field("ICINGA_DB_PASSWORD")

    icinga_connection = psycopg.connect(
        dbname=icinga_db_name,
        user=icinga_db_user,
        password=icinga_db_password,
        host=icinga_db_host,
        port=icinga_db_port,
        options="-c DateStyle=ISO",
    )

    icinga_cursor = icinga_connection.cursor()

    icinga_cursor.execute(
        """SELECT DISTINCT replace(c_alarm_id.value,'"','') AS alarm_id
             FROM service sv
             JOIN service_customvar sc
               ON sv.id = sc.service_id
             JOIN customvar c_alarm_id
               ON sc.customvar_id = c_alarm_id.id
              AND c_alarm_id.name = 'alarm_id'
             LEFT JOIN service_state svs
               ON sv.id = svs.service_id
              AND svs.state_type = 'soft'
             LEFT JOIN service_state svh
               ON sv.id = svh.service_id
              AND svh.state_type = 'hard'
             JOIN host h
               ON sv.host_id = h.id
            WHERE 1=1
              AND COALESCE(svs.soft_state, svh.hard_state) in (1,2,3)
            LIMIT 10000"""
    )

    icinga_records = icinga_cursor.fetchall()

    icinga_connection.close()

    alarm_ids = [rec[0] for rec in icinga_records]

session_manager = db.create_session_manager("script-resync-alarms")

with session_manager.get_session() as session:
    try:
        for alarm_id in alarm_ids:
            if alarm := db.Alarm.get_alarm_by_id(session, alarm_id):
                alarm.is_active = True
                alarm.update_object(session)
                alarm.resend_to_ui(session)

        session.commit()
    except Exception as exc:
        print(exc)
        session.rollback()
