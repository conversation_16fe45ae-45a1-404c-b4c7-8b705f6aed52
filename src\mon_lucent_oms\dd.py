"""Details design implementation for mon-lucent-oms."""

import json
from datetime import datetime, timedelta

import pandas as pd

from mon_lucent_oms.statics import ALARM_NAMES
from olympus_common import enums
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _agent() -> str:
    """Return the agent name for the DD."""
    return "LucentOMS"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm(row: pd.Series) -> bool:
    """Return the actionable alarm value."""
    event_type: str = row["event_type"]
    severity: int = row["severity"]
    alert_key: str = _alert_key(row)
    alarm_name: str = row["alarm_name"]

    # Rule (No) for:
    # - resolution alarms
    # - problem alarms with severity 1 (Indeterminate)
    # - problem alarms with AlertKey like "VC.*UNEQ.*"
    if (
        event_type == enums.AlarmType.RESOLUTION.value
        or (event_type == enums.AlarmType.PROBLEM.value and severity == enums.Severity.INDETERMINATE.value)
        or (event_type == enums.AlarmType.PROBLEM.value and "VC.*UNEQ.*" in alert_key)
    ):
        return False

    # Rule (Yes) for Problem Alarms
    # For problem alarms having either @AdditionalInformation in specific list
    # or @AdditionalInformation contains "DEG"
    if event_type == enums.AlarmType.PROBLEM.value and (alarm_name in ALARM_NAMES):
        return True

    # if severity=5 (critical) THEN ActionableAlarm=1 (Yes)
    if severity == enums.Severity.CRITICAL.value:
        return True

    # Rule (No) for all other problem alarms
    return False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_data(row: pd.Series) -> str:
    """Return the additional data."""
    return json.dumps({"alarm_name": row["alarm_name"]})


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type() -> str:
    """Return the clear_type."""
    return enums.ClearType.AUTOMATICALLY.value


def _alert_key(row: pd.Series) -> str:
    """Return the alert key."""
    alarm_name: str = row["alarm_name"]
    physical_port_address: str = row["physical_port_address"]
    # clf is always empty so we don't need it
    return f"{alarm_name} {physical_port_address}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the manager."""
    return "mon-lucent-oms"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Return the node."""
    name_network_resource: str = row["name_network_resource"]
    alarm_name: str = row["alarm_name"]
    if not alarm_name:
        return "Unknown"
    return name_network_resource


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the scope."""
    return enums.Scope.NETWORKS.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Return the severity."""
    severity: int = int(row["alarm_severity"])
    severity_map = {
        1: enums.Severity.CRITICAL.value,
        2: enums.Severity.MAJOR.value,
        3: enums.Severity.MINOR.value,
        4: enums.Severity.WARNING.value,
        5: enums.Severity.INDETERMINATE.value,
    }
    return severity_map.get(severity, enums.Severity.INDETERMINATE.value)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Return the summary of the event."""
    summary = ""
    alarm_name: str = row["alarm_name"]
    alarm_text_string: str = row["alarm_text_string"]
    physical_port: str = row["physical_port"]
    if not alarm_name:
        summary = "No name or description"
    elif alarm_name:
        if not alarm_text_string:
            summary = alarm_name
        else:
            summary = alarm_text_string
    else:
        summary = f"{summary} ({physical_port})"

    return summary


def _physical_port(row: pd.Series) -> str:
    """Return the physical port."""
    # half of logic for this field can't be reproduced because clf is always empty
    return row["physical_port_address"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level() -> str:
    """Return the top level."""
    return "A1029"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_type(row: pd.Series) -> str:
    """Return the alarm type."""
    alarm_state: int = row["alarm_state"]
    if alarm_state == 1:  # 1 represents a raise state
        return enums.AlarmType.PROBLEM.value
    else:
        return enums.AlarmType.RESOLUTION.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time() -> datetime:
    """Return the handle time."""
    return olympus_utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime | None:
    """Return the clear time."""
    event_type: str = row["event_type"]
    raise_time: datetime = row["raise_time"]
    if event_type == enums.AlarmType.RESOLUTION.value:
        return raise_time
    return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime:
    """Return the wake up time."""
    raise_time: datetime = row["raise_time"]
    return raise_time + timedelta(0, _delay())


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _delay() -> int:
    """Return the delay for lucent oms events snooze mechanism.."""
    return 120


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime:
    """Return the raise time."""
    raise_time: str = row["raise_time_raw"]
    return datetime.strptime(raise_time, "%Y-%m-%dT%H:%M:%S.000Z")


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type(row: pd.Series) -> str | None:
    """Return the metric type."""
    alarm_name: str = row["alarm_name"]
    if alarm_name in ALARM_NAMES:
        return "/HardwareEvent/"
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str | None:
    """Return the metric name."""
    alarm_name: str = row["alarm_name"]
    if alarm_name in ALARM_NAMES:
        return alarm_name
    else:
        return None


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Run the LucentOMS detailed design."""
    return transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    # Temporary values for the ems.
    df["physical_port"] = df.apply(_physical_port, axis=1)

    # hardcoded values
    df["agent_id"] = agent_id
    df["manager"] = _manager()
    df["action_class"] = _scope()
    df["handle_time"] = _handle_time()
    df["clear_type"] = _clear_type()
    df["top_level"] = _top_level()
    df["ci_id"] = df["name_network_resource"]

    # computed values
    df["severity"] = df.apply(_severity, axis=1)
    df["event_type"] = df.apply(_event_type, axis=1)
    df["raise_time"] = df.apply(_raise_time, axis=1)
    df["additional_data"] = df.apply(_additional_data, axis=1)
    df["node"] = df.apply(_node, axis=1)
    df["node_alias"] = df["node"]
    df["clear_time"] = df.apply(_clear_time, axis=1)
    df["wake_up_time"] = df.apply(_wake_up_time, axis=1)
    df["summary"] = df.apply(_summary, axis=1)
    df["actionable"] = df.apply(_actionable_alarm, axis=1)
    df["metric_type"] = df.apply(_metric_type, axis=1)
    df["metric_name"] = df.apply(_metric_name, axis=1)

    return df
