"""Jobs module for the optic-matcher."""

import logging

import numpy as np
import oracledb
import pandas as pd
from dateutil.relativedelta import relativedelta
from sqlalchemy import and_, select
from sqlalchemy.orm import Session

from olympus_common import utils
from olympus_common.db import Alarm, AlarmEnrichment, Enrichment, SessionManager
from olympus_common.utils import datetime_from_local_to_utc
from optic_matcher.db import MatchedAlarm, OpticAlarm, ReporterStatusJournal, create_optic_session_manager
from optic_matcher.utils import loose_matching, olympus_pre_processing, optic_pre_processing


def import_from_optic(backend_session_manager: SessionManager) -> None:
    """Import alarms from optic."""
    logging.info("Start import Optic alarms.")
    oracledb.init_oracle_client()
    optic_session_manager = create_optic_session_manager()
    # Date criterion is the first day of the previous month. Example: 01-JUN-24.
    first_day_of_last_month = utils.now_naive() + relativedelta(
        months=-1, day=1, hour=0, minute=0, second=0, microsecond=0
    )

    statement = (
        select(ReporterStatusJournal)
        .where(ReporterStatusJournal.raisetime >= first_day_of_last_month)
        .order_by(ReporterStatusJournal.raisetime.asc())
    )

    with optic_session_manager.get_session() as optic_session:
        try:
            logging.info("Execute Optic statement - import alarms.")
            data = optic_session.execute(statement).scalars().all()
        except Exception as exc:
            logging.error(f"Olympus statement failed: {exc}")

    with backend_session_manager.begin() as backend_session:
        for record in data:
            # Fix agent for AIRCOs
            if record.agent == "local6" and record.ci_id and record.ci_id.startswith("AIRCO"):
                record.agent = "AIRCO"

            OpticAlarm.insert_one(record, backend_session)

    logging.info("Stop import Optic alarms.")


def match_alarms(backend_session: Session) -> None:
    """Match Olympus alarms and Optic alarms."""
    logging.info("Start matching alarms.")
    two_weeks_ago = utils.now_naive() + relativedelta(weeks=-2, day=1, hour=0, minute=0, second=0, microsecond=0)

    # Optic alarms
    optic_statement = (
        select(OpticAlarm)
        .join(MatchedAlarm, OpticAlarm.id == MatchedAlarm.optic_alarm_id, isouter=True)
        .where(MatchedAlarm.id.is_(None))
        .where(OpticAlarm.actionablealarm == 1)
    )

    try:
        logging.info("Execute Optic statement.")
        optic_data = backend_session.execute(optic_statement).scalars().all()
    except Exception as exc:
        logging.error(f"Olympus statement failed: {exc}")

    # Pre-processing for Optic
    transformed_data = []
    for optic_alarm in optic_data:
        optic_alarm = optic_pre_processing(optic_alarm)
        transformed_data.append(optic_alarm.asdict())

    optic_df = pd.DataFrame(transformed_data)

    # Global transforms for Optic
    optic_df["raisetime"] = optic_df.apply(lambda row: datetime_from_local_to_utc(row["raisetime"]), axis=1)
    optic_df = optic_df.fillna("")

    # Olympus alarms (yet unmatched)
    olympus_statement = (
        select(Alarm)
        .join(MatchedAlarm, Alarm.id == MatchedAlarm.alarm_id, isouter=True)
        .join(
            AlarmEnrichment,
            and_(Alarm.id == AlarmEnrichment.alarm_id, AlarmEnrichment.last_enrichment == 1),
            isouter=True,
        )
        .join(
            Enrichment,
            Enrichment.id == AlarmEnrichment.enrichment_id,
            isouter=True,
        )
        .where(
            and_(MatchedAlarm.id.is_(None), Alarm.first_raise_time >= two_weeks_ago),
        )
    )
    try:
        logging.info("Execute Olympus statement.")
        olympus_data = backend_session.execute(olympus_statement).scalars().all()
    except Exception as exc:
        logging.error(f"Olympus statement failed: {exc}")

    transformed_data = []
    try:
        for olympus_alarm in olympus_data:
            olympus_alarm_dict = olympus_pre_processing(olympus_alarm)
            transformed_data.append(
                {
                    "alarm_id": olympus_alarm_dict["id"],
                    "agent_name": olympus_alarm_dict["agent"]["name"] if olympus_alarm_dict["agent"] else "",
                    "agent_id": olympus_alarm_dict["agent_id"],
                    "ci_id": olympus_alarm_dict["ci_id"],
                    "metric_name": olympus_alarm_dict["metric_name"],
                    "metric_type": olympus_alarm_dict["metric_type"],
                    "raise_time": olympus_alarm_dict["first_raise_time"],
                }
            )
    except Exception as exc:
        logging.error(f"Couldn't append Olympus alarm to transformed_data : {exc}")

    olympus_df = pd.DataFrame(transformed_data)
    olympus_df = olympus_df.fillna("")

    # Strict matching
    logging.info("Execute strict matching.")
    strictly_matched_df = pd.merge(
        optic_df,
        olympus_df,
        how="outer",
        left_on=["agent", "ci_id", "monitoredelementname", "metrics", "raisetime"],
        right_on=["agent_name", "ci_id", "metric_name", "metric_type", "raise_time"],
        indicator="exists",
    )

    # Loose matching, by EMS
    logging.info("Execute loose matching.")
    loosely_matched_df = loose_matching(strictly_matched_df)

    matched_alarms = strictly_matched_df[strictly_matched_df["exists"] == "both"]

    logging.info("Execute concat df.")
    matched_alarms = pd.concat([matched_alarms, loosely_matched_df])

    matched_alarms = matched_alarms.rename(columns={"id": "optic_alarm_id"})
    matched_alarms = matched_alarms.replace({pd.NA: None, np.nan: None})
    matched_alarms_dict = matched_alarms.to_dict("records")

    MatchedAlarm.insert_many(matched_alarms_dict, backend_session)
    logging.info("Stop matching alarms.")
