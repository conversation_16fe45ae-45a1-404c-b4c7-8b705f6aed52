kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-ing-zabbix-#{zabbixCC}#-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/a2110_olympus/run.py
  CHECKPOINTS_FOLDER: "/data/checkpoints"
  DEBUG: "0"
  ELASTIC_APM_ENVIRONMENT: "#{elasticApmEnvironment}#"
  ELASTIC_APM_SERVER_URL: "#{elasticApmServerUrl}#"
  ELASTIC_APM_SERVICE_NAME: "#{olympusServiceName}#_#{zabbixCC}#"
  ELASTIC_APM_VERIFY_SERVER_CERT: "#{elasticApmVerifyServerCert}#"
  ENABLE_ELASTIC_APM: "#{enableElasticApm}#"
  ELASTIC_APM_ENVIRONMENT: "#{elasticApmEnvironment}#"
  ELASTIC_APM_LOG_LEVEL: "#{elasticApmLogLevel}#"
  HEARTBEAT_CI: "#{heartbeatCI}#"
  KAFKA_BOOTSTRAP_SERVERS: >-
    #{kafkaBootstrapServers}#
  KAFKA_TOPICS: >-
    #{kafkaTopics}#
  LOGS_FOLDER: "/data/logs"
  MASTER_ZABBIX_DRP_ENDPOINT: "#{masterZabbixDrpEndpoint}#"
  MASTER_ZABBIX_ENDPOINT: "#{masterZabbixEndpoint}#"
  MASTER_ZABBIX_SERVER_ALIAS: "#{masterZabbixServerAlias}#"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  ZABBIX_CC: "#{zabbixCC}#"
  ZABBIX_DRP_ENDPOINT: "#{zabbixDrpEndpoint}#"
  ZABBIX_ENDPOINT: "#{zabbixEndpoint}#"
  ZABBIX_SERVER_ALIAS: "#{zabbixServerAlias}#"
  OLYMPUS_AGENT_ID: "0"
