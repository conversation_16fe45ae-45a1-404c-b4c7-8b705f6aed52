import importlib
import os
import sys
from collections import defaultdict
from pathlib import Path

from dateutil.parser import parse
from dotenv import load_dotenv
from pytest_mock import MockerFixture
from sqlalchemy import bindparam, text
from sqlalchemy.orm import Session

from olympus_common import core, datareaders, datawriters, db
from olympus_common.config import LoggerConfig
from olympus_common.defaults import get_logger
from tests.utils import dummy_environ


def dummy_db_env(add_missing: bool = True) -> dict[str, str]:
    """Return a dictionary with database environment variables.

    The following environment variables are required:
    - TEST_DB_NAME
    - TEST_DB_SCHEMA
    - TEST_DB_HOST
    - TEST_DB_PORT
    - TEST_DB_USER
    - TEST_DB_PASSWORD

    If add_missing is True, the dictionary will also contain dummy_environ.
    """
    load_dotenv()
    db_details = {
        "DB_NAME": os.environ["TEST_DB_NAME"],
        "DB_SCHEMA": os.environ["TEST_DB_SCHEMA"],  # Ensure this is the test_schema!
        "DB_HOST": os.environ["TEST_DB_HOST"],
        "DB_PORT": os.environ["TEST_DB_PORT"],
        "DB_USER": os.environ["TEST_DB_USER"],
        "DB_PASSWORD": os.environ["TEST_DB_PASSWORD"],
    }
    if add_missing:
        env_dict = dummy_environ()
        return {**env_dict, **db_details}
    return db_details


def find_folder(start_path: Path, target_folder: str) -> Path:
    """Find the target folder starting from the start_path and going up in the filesystem."""
    current_path = start_path.resolve()
    if current_path.is_file():
        current_path = current_path.parent

    while current_path != current_path.parent:  # Stop at the root
        potential_target = current_path / target_folder
        if potential_target.exists() and potential_target.is_dir():
            return potential_target
        current_path = current_path.parent

    raise FileNotFoundError(
        f"Folder '{target_folder}' not found starting from '{start_path}'. Last folder checked: '{current_path}'."
    )


def replace_tokens(fps: list[Path], schema: str, user: str):
    """Replace the tokens in the script text and return the result.

    This function does not modify the file.
    """
    for fp in fps:
        script_text = fp.read_text()
        script_text = script_text.replace("#{Schema}#", schema)
        script_text = script_text.replace("#{DbUser}#", user)
        yield script_text


def create_test_session_manager() -> db.SessionManager:
    """Create a test session manager."""
    return db.create_session_manager("test_session_olympus")


def create_test_session() -> Session:
    """Create a test session."""
    return create_test_session_manager().get_session()


def get_test_files():
    """Get all files under tests/end2end/data/test.

    The files can then be used to parametrize the test_services test.
    By parametrizing the test, we will get success/failure reports for each file.

    Notes
    -----
    Uncomment the return statement to filter specific files when testing locally. Do not forget to revert this though :)
    To ignore certain files, you can temporarily change the file extension to something other than .json.
    """
    fp = find_folder(Path(__file__), "data/test")
    # return [f for f in fp.glob("*.json") if f.stem in ["mon_adva"]]
    return fp.glob("*.json")


def assert_module_output(file_content_json: dict):
    """Assert that the output of the module is equal to the expected_output.

    Set the agent_id to 1337 for the next run if 'clear_agent_for_next_run' is found in the file_content.
    This is useful in case of zabbix, which runs multiple times with the same agent_id, but different configuration.
    We choose to set the agent_id to 1337 to ensure that we still have access to the data, but with a different
    agent_id.

    Expanding bindparams are used to expand the agent_ids in the query. This is required for manually created SELECT
    queries with an IN statement.
    """
    session = create_test_session()
    agent_ids = file_content_json["agent_ids"]
    expected_records_dict = get_expected_records(file_content_json["data"])
    for table_name, expected_records in expected_records_dict.items():
        convert_dt(expected_records)
        records = (
            session.execute(
                text(f"SELECT * FROM {table_name} WHERE agent_id IN :agent_ids ORDER BY id").bindparams(  # noqa: S608
                    bindparam("agent_ids", expanding=True)
                ),
                {"agent_ids": agent_ids},
            )
            .mappings()
            .all()
        )
        assert_records_equal(records, expected_records)
        if file_content_json.get("clear_agent_for_next_run"):
            session.execute(
                text(f"UPDATE {table_name} SET agent_id = 1337 WHERE agent_id IN :agent_ids").bindparams(  # noqa: S608
                    bindparam("agent_ids", expanding=True)
                ),
                {"agent_ids": agent_ids},
            )
    session.commit()
    session.close()


def get_expected_records(data: list[dict]) -> dict:
    """Return the expected records from the data."""
    expected_records = defaultdict(list)
    for item in data:
        if item.get("skip"):
            continue
        for table_name, records in item["output"].items():
            expected_records[table_name].extend(records)
    return expected_records


def assert_records_equal(records, expected_records):
    """Assert that the keys and values present in expected_records match those in records.

    Supports:
    - Records in different orders
    - Expected records with no fields (just verifies existence)
    - Partial value validation
    """
    assert len(records) == len(expected_records), f"Record count mismatch: {len(records)} vs {len(expected_records)}"

    remaining_records = list(records)
    remaining_expected = list(expected_records)

    for expected_record in expected_records:
        if not expected_record:  # Handle empty records later
            continue

        match_found = False
        for record in list(remaining_records):
            if all(key in record and record[key] == expected_record[key] for key in expected_record):
                remaining_expected.pop(remaining_expected.index(expected_record))
                remaining_records.pop(remaining_records.index(record))
                match_found = True
                break

        if not match_found:
            raise AssertionError(f"No matching record found for expected: {expected_record}")

    # If we have remaining empty expected records, just verify we have enough records
    if remaining_expected:
        assert all(not exp for exp in remaining_expected), "Non-empty expected records weren't matched"
        assert len(remaining_records) >= len(remaining_expected), (
            f"Not enough records to match {len(remaining_expected)} empty expected records"
        )


def convert_dt(records: list[dict]):
    """Convert datetime objects to strings."""
    for record in records:
        for key, value in record.items():
            if isinstance(value, str) and key.endswith("_time"):
                try:
                    record[key] = parse(value)
                except ValueError:
                    pass


class DataTestReader(datareaders.DataReader):
    def __init__(self, input_data: list[dict]):
        self.input_data = input_data

    def read_data(self) -> list[dict]:
        return self.input_data

    def success(self, data):
        pass

    def error(self, data, exc):
        pass


def init_test_app(
    input_data: list, object_class: type[db.Enrichment | db.Occurrence] = db.Occurrence
) -> core.Application:
    """Return an Application with a DatabaseWriter and DataTestReader."""
    session_manager = create_test_session_manager()
    datareader = DataTestReader(input_data)
    logger = get_logger(True, LoggerConfig())
    datawriter = datawriters.DatabaseWriter(object_class=object_class, session_manager=session_manager)
    return core.Application(datareader=datareader, datawriter=datawriter, logger=logger, monitoring_manager=None)


def load_extra_env(mocker: MockerFixture, file_content_json: dict):
    """Load the extra environment variables specified in the test-file."""
    extra_env = file_content_json.get("extra_env")
    if not extra_env:
        return
    mocker.patch.dict("os.environ", extra_env)


def import_or_reload(modulename: str):
    """Import or reload the modulename.

    mon_zabbix for example is ran twice with a different config. To ensure this config is effectively changed, we need
    to reload config, dd and the main module.
    """
    main_module = f"{modulename}.main"
    config_module = f"{modulename}.config"
    dd_module = f"{modulename}.dd"
    if config_module in sys.modules:
        importlib.reload(sys.modules[config_module])
    if dd_module in sys.modules:
        importlib.reload(sys.modules[dd_module])
    if main_module in sys.modules:
        module = importlib.reload(sys.modules[main_module])
    else:
        module = importlib.import_module(main_module)
    return module
