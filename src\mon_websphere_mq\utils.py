"""Utils for the mon_websphere_mq module."""

from olympus_common import enums
from olympus_common.elastic_apm import CaptureSpan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def convert_to_severity(syslog_severity: str) -> int:
    """Convert the severity to the correct value."""
    if "info" in syslog_severity:
        return enums.Severity.CLEARED.value
    elif "notice" in syslog_severity:
        return enums.Severity.WARNING.value
    elif "warning" in syslog_severity:
        return enums.Severity.MINOR.value
    elif syslog_severity in ["err", "error"]:
        return enums.Severity.MAJOR.value
    elif syslog_severity in ["crit", "critical"]:
        return enums.Severity.CRITICAL.value
    else:
        return enums.Severity.INDETERMINATE.value
