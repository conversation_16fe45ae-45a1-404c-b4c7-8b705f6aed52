*.py[cod]
.DS_Store
**/.env
# C extensions
*.so

# Packages
*.egg
*.egg-info
build
eggs
.eggs
parts
var
sdist
develop-eggs
.installed.cfg
lib
lib64
MANIFEST

# Installer logs
pip-log.txt
npm-debug.log
pip-selfcheck.json

# Unit test / coverage reports
.coverage
.tox
nosetests.xml
htmlcov
.cache
.pytest_cache
.mypy_cache

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# SQLite
test_exp_framework

# npm
node_modules/

# dolphin
.directory
libpeerconnection.log

# setuptools
dist

# IDE Files
atlassian-ide-plugin.xml
.idea/
*.swp
*.kate-swp
.ropeproject/

# Python3 Venv Files
.venv/
bin/
include/
lib/
lib64
pyvenv.cfg
share/
venv/
.python-version

# Cython
*.c

# Emacs backup
*~

# Automatically generated files
docs/preconvert
site/
out

# checkpoints, logs folders and sbom files
checkpoints
logs
exclusion
sbom.json


# Pycharm folders
.run

__pycache__