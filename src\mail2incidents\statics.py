"""Static modules for mail2incidents."""

header_sap = {
    "connection": "keep-alive",
    "x-csrf-token": "fetch",
    "Content-Type": "application/json",
    "Accept-Encoding": "gzip, deflate",
    "Accept": "application/json",
}

FORM_URLENCODED_HEADER = "application/x-www-form-urlencoded"


data_sap = {
    "Qmgrp": "900UNDEF",  # Service
    "Qmcod": "0100",  # Sub Service
    "Urgency": "3",
    "Priok": "4",  # Priority
    "ApiOrigin": "0118",  # ApiOrigin for Mail2Incidents
    "toIncidentPartners": [
        {"Parvw": "ZG", "Parnr": "427973"},  # Request Organisation
        {"Parvw": "ZX", "Parnr": "F1021"},  # Internal Customer
        {"Parvw": "ZK", "Parnr": "TZ-102-00"},  # External Customer
    ],
    "FlagMainOrderCreate": True,
}

data_filter: str = "$filter=isRead eq false&$top=20&$orderby=receivedDateTime asc"
data_needed: str = "$select=sender,toRecipients,sentDateTime,subject,body,hasAttachments,id,isRead"
