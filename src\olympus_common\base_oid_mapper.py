"""Module that handles the OID parsing for a mon-script."""

import pandas as pd

from olympus_common.pd import clean_raw_dataframe, ensure_required_fields


class BaseOIDMapper:
    """Base class for OID mapping."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {}

    @property
    def unmapped_oids(self) -> dict[str, str]:
        """Return unmapped OIDs."""
        return {}

    @property
    def drop_if_all_nan(self) -> list[str]:
        """Return fields to drop if all values are NaN."""
        return []

    def get_oids(self) -> list[dict]:
        """Return oid."""
        return [self.mappings, self.unmapped_oids]

    def transform_df(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform the dataframe based on the oid mappings.

        Parameters
        ----------
        df : pd.DataFrame
            The dataframe to transform.

        Returns
        -------
        pd.DataFrame
            The transformed dataframe.
        """
        for oids in self.get_oids():
            if not oids:
                continue
            try:
                df = ensure_required_fields(df=df, fields=oids.keys(), value="")
                df = clean_raw_dataframe(df=df, columns_renaming=oids, drop_if_all_nan=self.drop_if_all_nan)
                return df
            except Exception:
                """in this case, we should just continue to the next set of oids."""
        raise ValueError("No valid mappings provided.")
