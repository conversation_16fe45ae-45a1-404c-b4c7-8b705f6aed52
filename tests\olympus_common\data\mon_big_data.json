{"data": [{"input": {"annotations": {"summary": "GitLab CI pipeline #testid3 for test_olympus_3 has failed on meru: testurl3"}, "generatorURL": "", "labels": {"metrictype": "/Application/", "ci": "meru", "optic": "enabled", "alertname": "ingestion_job_failure-test_olympus_3", "application": "cloudera", "env": "prod", "acode": "A1556", "metricname": "ingestion_job_failure", "ingest_pipeline": "test_olympus_3", "severity": "error", "node": "meru.example.com"}, "fingerprint": "6b2c1e1fc151523a", "status": "firing", "startsAt": "2025-01-08T08:21:23Z", "endsAt": "0001-01-01T00:00:00Z"}, "output": {"event_type": "problem", "severity": 4, "summary": "GitLab CI pipeline #testid3 for test_olympus_3 has failed on meru: testurl3", "agent_id": 0, "clear_type": "automatic", "manager": "mon-big-data", "action_class": "IT", "top_level": "A1556", "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"a_code\": \"A1556\", \"alert_name\": \"ingestion_job_failure-test_olympus_3\", \"ci\": \"meru\", \"metric_name\": \"ingestion_job_failure\", \"metric_type\": \"/Application/\", \"severity\": \"error\", \"status\": \"firing\", \"summary\": \"GitLab CI pipeline #testid3 for test_olympus_3 has failed on meru: testurl3\"}", "ci_id": "meru", "node": "meru", "metric_type": "/Application/", "metric_name": "ingestion_job_failure", "node_alias": "meru", "raise_time": "2024-01-01 12:00:01", "clear_time": null, "wake_up_time": "2024-01-01 12:00:01"}}, {"input": {"annotations": {"summary": "Prometheus healthcheck"}, "generatorURL": "", "labels": {"metrictype": "/Application/", "ci": "prometheus-server", "optic": "enabled", "alertname": "prometheus_healthcheck", "application": "prometheus", "env": "prod", "acode": "A2066", "metricname": "prometheus_healthcheck", "severity": "info", "node": "iictmismlv013.msnet.railb.be"}, "fingerprint": "abc123def456", "status": "firing", "startsAt": "2025-01-08T08:21:23Z", "endsAt": "0001-01-01T00:00:00Z"}, "output": {"event_type": "heartbeat", "severity": 1, "summary": "BigData HeartBeat Message", "agent_id": 0, "clear_type": "automatic", "manager": "mon-big-data", "action_class": "IT", "top_level": "A2066", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": null, "ci_id": "prometheus-server", "node": "iictmismlv013", "metric_type": "/ApplicationEvent/", "metric_name": "Heartbeat", "node_alias": "iictmismlv013", "raise_time": "2024-01-01 12:00:01", "clear_time": null, "wake_up_time": "2024-01-01 12:00:01"}}]}