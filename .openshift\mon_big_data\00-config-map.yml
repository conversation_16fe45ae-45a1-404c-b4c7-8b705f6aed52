kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-mon-big-data-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/a2110_olympus/run.py
  DEBUG: "0"
  OPENSHIFT: "1"
  LOGS_FOLDER: "/data/logs"
  DB_SCHEMA: "#{databaseSchema}#"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  OLYMPUS_HOST: "#{olympusHost}#"
  OlYMPUS_PORT: "#{olympusPort}#"
  JWK_URI: "#{jwkUri}#"
  JWK_VALID_AUDIENCES: >-
    #{jwkValidAudiences}#
  ELASTIC_APM_SERVER_URL: "#{elasticApmServerUrl}#"
  ELASTIC_APM_SERVICE_NAME: "#{olympusServiceName}#"
  ELASTIC_APM_VERIFY_SERVER_CERT: "#{elasticApmVerifyServerCert}#"
  ENABLE_ELASTIC_APM: "#{enableElasticApm}#"
  ELASTIC_APM_ENVIRONMENT: "#{elasticApmEnvironment}#"
  ELASTIC_APM_LOG_LEVEL: "#{elasticApmLogLevel}#"
  OLYMPUS_AGENT_ID: "2"
