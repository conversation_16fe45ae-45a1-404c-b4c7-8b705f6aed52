"""Entrypoint for the application."""

from fastapi import FastAP<PERSON>
from fastapi.encoders import jsonable_encoder

from mon_big_data import dd
from mon_big_data.config import config
from mon_big_data.models import PrometheusAlert
from mon_big_data.oid_mapper import OIDMapper
from olympus_common import auth, defaults
from olympus_common.datawriters import DatabaseWriter
from olympus_common.db import Occurrence, create_session_manager

app = FastAPI(debug=config.debug)

if not config.debug:
    auth.add_security(app, config.jwk_config)


@app.post("/webhook")
async def receive_webhook_event(prometheus_alert: PrometheusAlert):
    """Webhook of the application who receive the data from big data."""
    session_manager = create_session_manager(config.service_name)
    database_writer = DatabaseWriter(object_class=Occurrence, session_manager=session_manager)
    data = jsonable_encoder(prometheus_alert)

    transformed_data = defaults.transform(data["alerts"], config, dd.run, oid_mapper=OIDMapper)
    database_writer.success(transformed_data)


def main():
    """Execute the main function for when the project is run."""
    defaults.run_fastapi_service(app, config.server_config)
