"""Utils module for commonly used function between Zabbix types."""

import itertools
import logging
from typing import Any

from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import MeasureType


def explode_tags(tags: list[dict[str, str]]) -> dict:
    """Exploit the given tags (from Zabbix event) into a dict with key:value.

    Note
    ----
    This will have to be maintained in order to introduce, change or delete tags
    from the zabbix teams given information.

    Parameters
    ----------
    tags : list[dict[str, str]]
        The list of tags following the Zabbix convention, for example:
        [{'tag': 'METRIC_NAME', 'value': 'PING'}, {'tag': 'APPLICATIONS', 'value': '["A1364"]'}]

    Returns
    -------
    dict
        The dict containing the tags in a more exploitable way (key: value).
    """
    exploded_tags = {
        "metric_name": "N/A",
        "metric_type": "N/A",
        "floc_id": "N/A",
        "ci_id": "N/A",
        "os": "N/A",
        "object_1": "N/A",
        "object_2": "N/A",
        "object_3": "N/A",
        "object_4": "N/A",
    }
    tag_map = {
        "METRIC_NAME": "metric_name",
        "METRIC_TYPE": "metric_type",
        "FLOCID": "floc_id",
        "CI_ID": "ci_id",
        "MON_OS": "os",
        "MON_OBJ1": "object_1",
        "MON_OBJ2": "object_2",
        "MON_OBJ3": "object_3",
        "MON_OBJ4": "object_4",
    }
    for tag in tags:
        tag_name = tag["tag"]
        if tag_name not in tag_map:
            continue
        tag_key = tag_map[tag_name]
        exploded_tags[tag_key] = tag["value"] or "N/A"
    return exploded_tags


@CaptureSpan(span_type=MeasureType.CUSTOM.value)
def _wrong_keys(dict_: dict, keys: list, log_message: str | None = None) -> None:
    """Put N/A into the given keys of the given dict.

    Parameters
    ----------
    dict_ : dict
        The dict on which the changes are performed.
    keys : list
        The list of keys for which we put N/A.
    log_message : str | None, optional
        The message to log as info, by default None
    """
    if log_message:
        logging.info(log_message)
    for key in keys:
        dict_[key] = "N/A"


def merge_host_event(event_list: list[dict], host_list: list[dict]) -> None:
    """Merge the list of events with the list of host from the hostid key.

    Parameters
    ----------
    event_list : list[dict]
        The list of Zabbix events.
    host_list : list[dict]
        The list of Zabbix hosts.
    """
    for host, event in itertools.product(host_list, event_list):
        if "os" in event and event["os"] and event["os"] != "N/A":
            continue

        event["os"] = []
        for id in event["hostid"]:
            if host["os"] and host["hostid"] == id:
                event["os"].append(host["os"])
        if event["os"] == []:
            event["os"].append("N/A")


def explode_host_keys(host: dict[str, Any]) -> None:
    """Explode the keys from the dict contained in the list given by Zabbix host.get method.

    Exploded keys
    -------------
    - os from inventory.os

    Parameters
    ----------
    host : dict[str, Any]
        A dictionary representing a host as returned by the Zabbix API.
    """
    host["os"] = set()
    if "inventory" in host and host["inventory"] and isinstance(host["inventory"], dict):
        host["os"] = host["inventory"].get("os", "N/A") or "N/A"
    elif "inventory" in host and host["inventory"] and isinstance(host["inventory"], list):
        for inventory in host["inventory"]:
            if isinstance(inventory, dict):
                _os = inventory.get("os", "N/A") or "N/A"
                host["os"].add(_os)
            else:
                host["os"].add("N/A")
        host["os"] = ",".join(sorted(host["os"]))
    else:
        host["os"] = "N/A"
    if "inventory" in host:
        host.pop("inventory")


def explode_event_keys(event: dict) -> None:
    """Explode the keys from the dict contained in the list given by Zabbix event.get method.

    Exploded keys
    -------------
    - host from hosts.host
    - hostid from hosts.hostid
    - triggerid from relatedObject.triggerid
    - trigger_comments from relatedObject.comments

    Parameters
    ----------
    event : dict
        The dict containing the event info.
    """
    # Handle the hosts part of the event, corresponding to the list of related hosts.
    _explode_hosts(event)

    # Handle the relatedObject part of the event, corresponding to the trigger.
    explode_related_object(event)

    # Drop the parts of the response that has been threated.
    _splitted_keys = {"tags", "hosts", "relatedObject"}
    for key in _splitted_keys:
        if key in event:
            event.pop(key)


@CaptureSpan(span_type=MeasureType.CUSTOM.value)
def explode_related_object(event: dict) -> None:
    """Add the keys triggerid and trigger_comments from relatedObject key to the given dict."""
    if "relatedObject" in event and event["relatedObject"] and isinstance(event["relatedObject"], dict):
        event["triggerid"] = event["relatedObject"].get("triggerid", "N/A")
        event["trigger_comments"] = event["relatedObject"].get("comments", "N/A")
    else:
        _wrong_keys(event, ["trigger_comments", "triggerid"])


@CaptureSpan(span_type=MeasureType.CUSTOM.value)
def _explode_hosts(event: dict) -> None:
    """Add the keys host and hostid from hosts key to the given dict."""
    if "hosts" in event and event["hosts"]:
        event["hostname"] = []
        event["hostid"] = []
        for host in event["hosts"]:
            if host and isinstance(host, dict):
                event["hostname"].append(host.get("host", "N/A"))
                event["hostid"].append(host.get("hostid", "N/A"))
            else:
                _wrong_keys(event, ["hostname", "hostid"])
    else:
        _wrong_keys(event, ["hostname", "hostid"], "The hosts key is not inside the response.")
