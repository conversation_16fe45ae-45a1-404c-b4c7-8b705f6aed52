{"data": [{"input": {"@version": "1", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "@timestamp": "2024-07-05T07:10:13.465877699Z", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "message": "#<SNMP::SNMPv1_Trap:0x1f45be7c @enterprise=[*******.4.1.5528.100.10.3], @timestamp=#<SNMP::TimeTicks:0x17977dae @value=612996679>, @varbind_list=[#<SNMP::VarBind:0x1f168255 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_EE265E34\">, #<SNMP::VarBind:0x2d927995 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_podunpluggedsensor\">, #<SNMP::VarBind:0x1c41b84 @name=[*******.4.1.5528.100.11.3], @value=\"Communication Lost\">, #<SNMP::VarBind:0x7e7ef7c4 @name=[*******.4.1.5528.100.11.4], @value=\"\">, #<SNMP::VarBind:0x5551d674 @name=[*******.4.1.5528.100.11.5], @value=\"\">, #<SNMP::VarBind:0x6b31539a @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEncB6AA1EC4\">, #<SNMP::VarBind:0x4a887d43 @name=[*******.4.1.5528.100.11.7], @value=\"UPWALCO03RIA(*************)\">, #<SNMP::VarBind:0x43c17301 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x18f8ef7b @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x79b4d27d @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x705fda6d @value=1720163353>>, #<SNMP::VarBind:0x384ee61c @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x67924559 @value=1720163413>>, #<SNMP::VarBind:0x386a44ec @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x36e87d3e @value=0>>, #<SNMP::VarBind:0x795903f9 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0xc942906 @value=3>>, #<SNMP::VarBind:0x66b3515c @name=[*******.4.1.5528.100.11.14], @value=\"\">, #<SNMP::VarBind:0x13c7c139 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x22b05e73 @value=0>>, #<SNMP::VarBind:0xf418f3a @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x7304163f @value=0>>], @specific_trap=7, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x5e67cf6f @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "0", "type": "snmp_trap", "event.uuid": "f9fbf6b4-ed3b-44a8-be98-1a82322ce877", "event.kafka.offset": 29321, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.kafka.timestamp": "2024-07-05T07:10:13.567Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.severity": "3", "snmptrap.sensorvalue1": "", "snmptrap.location": "nbSNMPEncB6AA1EC4", "snmptrap.notifyTime": "1720163413", "snmptrap.startTime": "1720163353", "snmptrap.errorType": "nbErrorType_podunpluggedsensor", "snmptrap.resolveTime": "0", "snmptrap.errorId": "nbErrorCond_EE265E34", "snmptrap.responsible": "", "snmptrap.description": "Communication Lost", "snmptrap.hostname": "UPWALCO03RIA(*************)", "snmptrap.eventName": "", "host.ip": "**************"}, "output": {"severity": 4, "agent_id": 0, "clear_type": "automatic", "manager": "mon-struxureware", "metric_type": "/HardwareEvent/", "action_class": "TE", "top_level": "A1421", "event_id": "nbErrorCond_EE265E34", "handle_time": "2024-01-01 12:00:01", "metric_name": "COMMUNICATION", "ci_id": "UPWALCO03RIA", "event_type": "problem", "raise_time": "2024-07-05 07:10:13", "clear_time": null, "node": "*************", "node_alias": "UPWALCO03RIA", "summary": "Communication Lost.", "additional_data": "{\"delay\": 900, \"event_name\": \"\"}", "wake_up_time": "2024-07-05 07:25:13"}}, {"input": {"@version": "1", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "@timestamp": "2024-07-05T07:25:09.252556525Z", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "message": "#<SNMP::SNMPv1_Trap:0x42ac20f6 @enterprise=[*******.4.1.5528.100.10.3], @timestamp=#<SNMP::TimeTicks:0x72e08f29 @value=613086257>, @varbind_list=[#<SNMP::VarBind:0x5ef25b4e @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_E260F9E7\">, #<SNMP::VarBind:0x7d7a7bf6 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_podunpluggedsensor\">, #<SNMP::VarBind:0xb153ab7 @name=[*******.4.1.5528.100.11.3], @value=\"Communication Lost\">, #<SNMP::VarBind:0x1d79d58f @name=[*******.4.1.5528.100.11.4], @value=\"\">, #<SNMP::VarBind:0x511dd545 @name=[*******.4.1.5528.100.11.5], @value=\"\">, #<SNMP::VarBind:0x7f96b15b @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc9B8D3C3E\">, #<SNMP::VarBind:0x5df4fc9c @name=[*******.4.1.5528.100.11.7], @value=\"UPANTLU01RI (*************)\">, #<SNMP::VarBind:0xef29f00 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x31fad466 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x7b2cb76b @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x146b48e @value=1720164249>>, #<SNMP::VarBind:0x1bd62139 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x7ce88fc1 @value=1720164309>>, #<SNMP::VarBind:0x6261e8d8 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x56b7d2de @value=0>>, #<SNMP::VarBind:0x7c42fed8 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x446cf016 @value=3>>, #<SNMP::VarBind:0x29a6f76b @name=[*******.4.1.5528.100.11.14], @value=\"\">, #<SNMP::VarBind:0x41efe1a3 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x2ab07378 @value=0>>, #<SNMP::VarBind:0x97fda78 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x17fc6993 @value=0>>], @specific_trap=7, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x29a06036 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "0", "type": "snmp_trap", "event.uuid": "985d6dca-3da4-46d8-8a05-c3f7acdd8390", "event.kafka.offset": 29686, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.kafka.timestamp": "2024-07-05T07:25:09.357Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.severity": "3", "snmptrap.sensorvalue1": "", "snmptrap.location": "nbSNMPEnc9B8D3C3E", "snmptrap.notifyTime": "1720164309", "snmptrap.startTime": "1720164249", "snmptrap.errorType": "nbErrorType_podunpluggedsensor", "snmptrap.resolveTime": "0", "snmptrap.errorId": "nbErrorCond_E260F9E7", "snmptrap.responsible": "", "snmptrap.description": "Communication Lost", "snmptrap.hostname": "UPANTLU01RI (*************)", "snmptrap.eventName": "", "host.ip": "**************"}, "output": {"severity": 4, "agent_id": 0, "clear_type": "automatic", "manager": "mon-struxureware", "metric_type": "/HardwareEvent/", "action_class": "TE", "top_level": "A1421", "event_id": "nbErrorCond_E260F9E7", "handle_time": "2024-01-01 12:00:01", "metric_name": "COMMUNICATION", "ci_id": "UPANTLU01RI", "event_type": "problem", "raise_time": "2024-07-05 07:25:09", "clear_time": null, "node": "*************", "node_alias": "UPANTLU01RI", "summary": "Communication Lost.", "additional_data": "{\"delay\": 900, \"event_name\": \"\"}", "wake_up_time": "2024-07-05 07:40:09"}}, {"input": {"@version": "1", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "@timestamp": "2024-07-05T07:26:09.289324798Z", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "message": "#<SNMP::SNMPv1_Trap:0x18f07326 @enterprise=[*******.4.1.5528.100.10.3], @timestamp=#<SNMP::TimeTicks:0x7bb57a15 @value=613092261>, @varbind_list=[#<SNMP::VarBind:0x30c6ec9b @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_E260F9E7\">, #<SNMP::VarBind:0x2b34d0e6 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_podunpluggedsensor\">, #<SNMP::VarBind:0x7b816247 @name=[*******.4.1.5528.100.11.3], @value=\"Communication Lost\">, #<SNMP::VarBind:0x72fe5803 @name=[*******.4.1.5528.100.11.4], @value=\"\">, #<SNMP::VarBind:0x75dbd0f3 @name=[*******.4.1.5528.100.11.5], @value=\"\">, #<SNMP::VarBind:0x78e58d4e @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc9B8D3C3E\">, #<SNMP::VarBind:0x8865b4f @name=[*******.4.1.5528.100.11.7], @value=\"UPANTLU01RI (*************)\">, #<SNMP::VarBind:0x8b8241c @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x234f31b0 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x7714576a @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x4d7226e4 @value=1720164249>>, #<SNMP::VarBind:0x4237392 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x5e3b1017 @value=1720164369>>, #<SNMP::VarBind:0xb41b3bd @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x57d8e137 @value=0>>, #<SNMP::VarBind:0x48f8b7bd @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x4ec5bd1d @value=3>>, #<SNMP::VarBind:0x489f3323 @name=[*******.4.1.5528.100.11.14], @value=\"\">, #<SNMP::VarBind:0x58dea25e @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x5596b3a7 @value=0>>, #<SNMP::VarBind:0x36d8c15d @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x7efea8e @value=0>>], @specific_trap=7, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x2a8e747f @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "0", "type": "snmp_trap", "event.uuid": "a2f1b3aa-0e91-408d-8259-ad695d34d8d6", "event.kafka.offset": 29153, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.kafka.timestamp": "2024-07-05T07:26:09.390Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.severity": "3", "snmptrap.sensorvalue1": "", "snmptrap.location": "nbSNMPEnc9B8D3C3E", "snmptrap.notifyTime": "1720164369", "snmptrap.startTime": "1720164249", "snmptrap.errorType": "nbErrorType_podunpluggedsensor", "snmptrap.resolveTime": "0", "snmptrap.errorId": "nbErrorCond_E260F9E7", "snmptrap.responsible": "", "snmptrap.description": "Communication Lost", "snmptrap.hostname": "UPANTLU01RI (*************)", "snmptrap.eventName": "", "host.ip": "**************"}, "output": {"severity": 4, "agent_id": 0, "clear_type": "automatic", "manager": "mon-struxureware", "metric_type": "/HardwareEvent/", "action_class": "TE", "top_level": "A1421", "event_id": "nbErrorCond_E260F9E7", "handle_time": "2024-01-01 12:00:01", "metric_name": "COMMUNICATION", "ci_id": "UPANTLU01RI", "event_type": "problem", "raise_time": "2024-07-05 07:26:09", "clear_time": null, "node": "*************", "node_alias": "UPANTLU01RI", "summary": "Communication Lost.", "additional_data": "{\"delay\": 900, \"event_name\": \"\"}", "wake_up_time": "2024-07-05 07:41:09"}}, {"input": {"@version": "1", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "@timestamp": "2024-07-05T07:53:03.202615031Z", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "message": "#<SNMP::SNMPv1_Trap:0x22fdc9 @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x77795baf @value=613253652>, @varbind_list=[#<SNMP::VarBind:0x293665b3 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_D582F75D\">, #<SNMP::VarBind:0x3e6d19e9 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toohigh\">, #<SNMP::VarBind:0x37489a8 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too High\">, #<SNMP::VarBind:0x688391e0 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc048EE501_acinfail\">, #<SNMP::VarBind:0x72026abe @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - INVERTER ACin FAIL\">, #<SNMP::VarBind:0x24769746 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc048EE501\">, #<SNMP::VarBind:0x3e873a9 @name=[*******.4.1.5528.100.11.7], @value=\"UPLIEGE13RIB (*************)\">, #<SNMP::VarBind:0x7710eea5 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x16263d63 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x6fa4446 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x46cf1a0b @value=1720165862>>, #<SNMP::VarBind:0x36daa1b1 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x11b05f2c @value=1720165982>>, #<SNMP::VarBind:0x453cf128 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x7b9da4c6 @value=0>>, #<SNMP::VarBind:0x1829452d @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0xbf3203b @value=4>>, #<SNMP::VarBind:0x29e425dc @name=[*******.4.1.5528.100.11.14], @value=\"1.0\">, #<SNMP::VarBind:0x3f491fb2 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x6d9b255b @value=1>>, #<SNMP::VarBind:0x7ffb222b @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x77b932cd @value=0>>], @specific_trap=2, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0xfa0707 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "1", "type": "snmp_trap", "event.uuid": "a0ef1d6b-e030-4e34-bb26-b9c0ed913aef", "event.kafka.offset": 29687, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.kafka.timestamp": "2024-07-05T07:53:03.304Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.severity": "4", "snmptrap.sensorvalue1": "1.0", "snmptrap.location": "nbSNMPEnc048EE501", "snmptrap.notifyTime": "1720165982", "snmptrap.startTime": "1720165862", "snmptrap.errorType": "nbErrorType_toohigh", "snmptrap.resolveTime": "0", "snmptrap.errorId": "nbErrorCond_D582F75D", "snmptrap.responsible": "nbSNMPEnc048EE501_acinfail", "snmptrap.description": "Value Too High", "snmptrap.hostname": "UPLIEGE13RIB (*************)", "snmptrap.eventName": "VOLTAGE - INVERTER ACin FAIL", "host.ip": "**************"}, "output": {"severity": 5, "agent_id": 0, "clear_type": "automatic", "manager": "mon-struxureware", "metric_type": "/HardwareEvent/", "action_class": "TE", "top_level": "A1421", "event_id": "nbErrorCond_D582F75D", "handle_time": "2024-01-01 12:00:01", "metric_name": "VOLTAGE", "ci_id": "UPLIEGE13RIB", "event_type": "problem", "raise_time": "2024-07-05 07:53:02", "clear_time": null, "node": "*************", "node_alias": "UPLIEGE13RIB", "summary": "VOLTAGE - INVERTER ACin FAIL: 1.0.", "additional_data": "{\"delay\": 0, \"event_name\": \"VOLTAGE - INVERTER ACin FAIL\"}", "wake_up_time": "2024-07-05 07:53:02"}}, {"input": {"@version": "1", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "@timestamp": "2024-07-05T07:53:03.393114464Z", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "message": "#<SNMP::SNMPv1_Trap:0x512fe4f0 @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x413501ef @value=613253671>, @varbind_list=[#<SNMP::VarBind:0x641fe001 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_83571A62\">, #<SNMP::VarBind:0x4a399016 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toohigh\">, #<SNMP::VarBind:0x236acac7 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too High\">, #<SNMP::VarBind:0xeb65cc8 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc048EE501_sysmin\">, #<SNMP::VarBind:0x15b2baf7 @name=[*******.4.1.5528.100.11.5], @value=\"SYSTEM - MINOR ALARM\">, #<SNMP::VarBind:0x130d3177 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc048EE501\">, #<SNMP::VarBind:0x66c4ed23 @name=[*******.4.1.5528.100.11.7], @value=\"UPLIEGE13RIB (*************)\">, #<SNMP::VarBind:0x5aa8ad2f @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x5acdc18f @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x20b1c528 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x615d89e8 @value=1720165862>>, #<SNMP::VarBind:0x7e838139 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x4a76e6df @value=1720165982>>, #<SNMP::VarBind:0x64c0e23c @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x69df2585 @value=0>>, #<SNMP::VarBind:0xbb7fbce @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x768aa479 @value=2>>, #<SNMP::VarBind:0x15b87996 @name=[*******.4.1.5528.100.11.14], @value=\"1.0\">, #<SNMP::VarBind:0x55b95a9 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x31866747 @value=1>>, #<SNMP::VarBind:0x120ca462 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x442e1b7c @value=0>>], @specific_trap=2, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x1d81388d @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "1", "type": "snmp_trap", "event.uuid": "85ed3590-220d-4144-91d2-3f2f4279ee7a", "event.kafka.offset": 29326, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.kafka.timestamp": "2024-07-05T07:53:03.493Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.severity": "2", "snmptrap.sensorvalue1": "1.0", "snmptrap.location": "nbSNMPEnc048EE501", "snmptrap.notifyTime": "1720165982", "snmptrap.startTime": "1720165862", "snmptrap.errorType": "nbErrorType_toohigh", "snmptrap.resolveTime": "0", "snmptrap.errorId": "nbErrorCond_83571A62", "snmptrap.responsible": "nbSNMPEnc048EE501_sysmin", "snmptrap.description": "Value Too High", "snmptrap.hostname": "UPLIEGE13RIB (*************)", "snmptrap.eventName": "SYSTEM - MINOR ALARM", "host.ip": "**************"}, "output": {"severity": 3, "agent_id": 0, "clear_type": "automatic", "manager": "mon-struxureware", "metric_type": "/HardwareEvent/", "action_class": "TE", "top_level": "A1421", "event_id": "nbErrorCond_83571A62", "handle_time": "2024-01-01 12:00:01", "metric_name": "SYSTEM - MINOR", "ci_id": "UPLIEGE13RIB", "event_type": "problem", "raise_time": "2024-07-05 07:53:02", "clear_time": null, "node": "*************", "node_alias": "UPLIEGE13RIB", "summary": "SYSTEM - MINOR ALARM: 1.0.", "additional_data": "{\"delay\": 130, \"event_name\": \"SYSTEM - MINOR ALARM\"}", "wake_up_time": "2024-07-05 07:55:12"}}, {"input": {"@version": "1", "SNMPv2-SMI::enterprises.5528.100.11.16": "189999", "@timestamp": "2024-07-05T08:29:41.479349335Z", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "message": "#<SNMP::SNMPv1_Trap:0x2e4774a9 @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x364c799 @value=613473480>, @varbind_list=[#<SNMP::VarBind:0x14cd24df @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_1F7F7F4B\">, #<SNMP::VarBind:0x1bae8d3 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0x2447bf72 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x7989fe6a @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEncE13CA7C0_DC_PWR_SYS_CONTROLLER_INPUT_1\">, #<SNMP::VarBind:0x49fd658 @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - Load Voltage\">, #<SNMP::VarBind:0x52b55a68 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEncE13CA7C0\">, #<SNMP::VarBind:0x4b90d363 @name=[*******.4.1.5528.100.11.7], @value=\"upjette02r (*************)\">, #<SNMP::VarBind:0x7d5c0ed5 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x78b2a30 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x7f9556b3 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x325399e9 @value=1720168058>>, #<SNMP::VarBind:0x2f6403d0 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x71e066a6 @value=1720168178>>, #<SNMP::VarBind:0x241596e7 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0xaa7df5d @value=0>>, #<SNMP::VarBind:0x552275a @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x7775b400 @value=0>>, #<SNMP::VarBind:0x7f5fb6a7 @name=[*******.4.1.5528.100.11.14], @value=\"50.2 V\">, #<SNMP::VarBind:0x4b36c326 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x5f4a45ed @value=50>>, #<SNMP::VarBind:0x5453fb0 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x7ad5b31 @value=189999>>], @specific_trap=3, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x7aff8711 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "50", "type": "snmp_trap", "event.uuid": "590ad877-4fc5-4b6d-a959-f003be63f769", "event.kafka.offset": 29162, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.kafka.timestamp": "2024-07-05T08:29:41.580Z", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.severity": "0", "snmptrap.sensorvalue1": "50.2 V", "snmptrap.location": "nbSNMPEncE13CA7C0", "snmptrap.notifyTime": "1720168178", "snmptrap.startTime": "1720168058", "snmptrap.errorType": "nbErrorType_toolow", "snmptrap.resolveTime": "0", "snmptrap.errorId": "nbErrorCond_1F7F7F4B", "snmptrap.responsible": "nbSNMPEncE13CA7C0_DC_PWR_SYS_CONTROLLER_INPUT_1", "snmptrap.description": "Value Too Low", "snmptrap.hostname": "upjette02r (*************)", "snmptrap.eventName": "VOLTAGE - Load Voltage", "host.ip": "**************"}, "output": {"severity": 5, "agent_id": 0, "clear_type": "automatic", "manager": "mon-struxureware", "metric_type": "/HardwareEvent/", "action_class": "TE", "top_level": "A1421", "event_id": "nbErrorCond_33D07690", "handle_time": "2024-01-01 12:00:01", "metric_name": "VOLTAGE", "ci_id": "UPLIEGE13RIB", "event_type": "problem", "raise_time": "2024-07-05 07:53:02", "clear_time": null, "node": "*************", "node_alias": "UPLIEGE13RIB", "summary": "VOLTAGE - Battery Voltage: 47.9 V.", "additional_data": "{\"delay\": 30, \"event_name\": \"VOLTAGE - Battery Voltage\"}", "wake_up_time": "2024-07-05 07:53:32"}}, {"input": {"@version": "1", "SNMPv2-SMI::enterprises.5528.100.11.16": "939999", "@timestamp": "2024-07-05T07:53:03.517519352Z", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "message": "#<SNMP::SNMPv1_Trap:0x34ff5bbe @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x1233cd70 @value=613253684>, @varbind_list=[#<SNMP::VarBind:0x7c30520d @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_33D07690\">, #<SNMP::VarBind:0x32dcc49e @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0x3e08d7f8 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x7c6052a @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc048EE501_DC_PWR_SYS_CONTROLLER_INPUT_3\">, #<SNMP::VarBind:0x79189fe5 @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - Battery Voltage\">, #<SNMP::VarBind:0x2d345c6c @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc048EE501\">, #<SNMP::VarBind:0x557be925 @name=[*******.4.1.5528.100.11.7], @value=\"UPLIEGE13RIB (*************)\">, #<SNMP::VarBind:0x62a359f5 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x5ff905f3 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x5021f55c @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x5a991cbc @value=1720165862>>, #<SNMP::VarBind:0x228d8aeb @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x354cc5d4 @value=1720165982>>, #<SNMP::VarBind:0x6a185d34 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x78f6c037 @value=0>>, #<SNMP::VarBind:0x3662c710 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x6553c66d @value=4>>, #<SNMP::VarBind:0x4d8ac60e @name=[*******.4.1.5528.100.11.14], @value=\"47.9 V\">, #<SNMP::VarBind:0x3b340f0 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x23c94dd1 @value=47>>, #<SNMP::VarBind:0x5852976b @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x43f390d9 @value=939999>>], @specific_trap=3, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x5a5eec97 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "47", "type": "snmp_trap", "event.uuid": "a42c3fd6-0c09-4161-ad17-60ecad72217f", "event.kafka.offset": 29688, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.kafka.timestamp": "2024-07-05T07:53:03.617Z", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.severity": "4", "snmptrap.sensorvalue1": "47.9 V", "snmptrap.location": "nbSNMPEnc048EE501", "snmptrap.notifyTime": "1720165982", "snmptrap.startTime": "1720165862", "snmptrap.errorType": "nbErrorType_toolow", "snmptrap.resolveTime": "0", "snmptrap.errorId": "nbErrorCond_33D07690", "snmptrap.responsible": "nbSNMPEnc048EE501_DC_PWR_SYS_CONTROLLER_INPUT_3", "snmptrap.description": "Value Too Low", "snmptrap.hostname": "UPLIEGE13RIB (*************)", "snmptrap.eventName": "VOLTAGE - Battery Voltage", "host.ip": "**************"}, "output": {"severity": 5, "agent_id": 0, "clear_type": "automatic", "manager": "mon-struxureware", "metric_type": "/HardwareEvent/", "action_class": "TE", "top_level": "A1421", "event_id": "nbErrorCond_1F7F7F4B", "handle_time": "2024-01-01 12:00:01", "metric_name": "VOLTAGE", "ci_id": "UPJETTE02R", "event_type": "problem", "raise_time": "2024-07-05 08:29:38", "clear_time": null, "node": "*************", "node_alias": "UPJETTE02R", "summary": "VOLTAGE - Load Voltage: 50.2 V.", "additional_data": "{\"delay\": 0, \"event_name\": \"VOLTAGE - Load Voltage\"}", "wake_up_time": "2024-07-05 08:29:38"}}, {"input": {"@version": "1", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "@timestamp": "2024-07-05T08:54:55.219338324Z", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "message": "#<SNMP::SNMPv1_Trap:0x3d228fd @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x32dbb57f @value=613624854>, @varbind_list=[#<SNMP::VarBind:0x560a60b @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_86298D67\">, #<SNMP::VarBind:0x1767e39a @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_errorstate\">, #<SNMP::VarBind:0x7878dc44 @name=[*******.4.1.5528.100.11.3], @value=\"Value Error\">, #<SNMP::VarBind:0x2bfa0b77 @name=[*******.4.1.5528.100.11.4], @value=\"nbModbusEncCB34581E_F6_07\">, #<SNMP::VarBind:0x2b2aa0fd @name=[*******.4.1.5528.100.11.5], @value=\"ZONE6 - F6.07\">, #<SNMP::VarBind:0x379fd31 @name=[*******.4.1.5528.100.11.6], @value=\"nbModbusEncCB34581E\">, #<SNMP::VarBind:0x3d5d8be6 @name=[*******.4.1.5528.100.11.7], @value=\"PDDEMON01(*************)\">, #<SNMP::VarBind:0x46079e89 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x38d4e3fc @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x740801a2 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x25dbb67e @value=1720169575>>, #<SNMP::VarBind:0x5fd2879a @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x4086449f @value=1720169695>>, #<SNMP::VarBind:0x74b7483c @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0xca77892 @value=0>>, #<SNMP::VarBind:0x4782d0cb @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x35d2c618 @value=3>>, #<SNMP::VarBind:0x7b6a8e36 @name=[*******.4.1.5528.100.11.14], @value=\"OFF\">, #<SNMP::VarBind:0x4cbe1a21 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x1deb9ff5 @value=0>>, #<SNMP::VarBind:0x3e2a7856 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x119e5a9b @value=0>>], @specific_trap=10, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x2d4d359b @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "0", "type": "snmp_trap", "event.uuid": "8667fc70-d51b-4881-9369-676894225b51", "event.kafka.offset": 29334, "event.kafka.key": null, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.kafka.timestamp": "2024-07-05T08:54:55.319Z", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.severity": "3", "snmptrap.sensorvalue1": "OFF", "snmptrap.location": "nbModbusEncCB34581E", "snmptrap.notifyTime": "1720169695", "snmptrap.startTime": "1720169575", "snmptrap.errorType": "nbErrorType_errorstate", "snmptrap.resolveTime": "0", "snmptrap.errorId": "nbErrorCond_86298D67", "snmptrap.responsible": "nbModbusEncCB34581E_F6_07", "snmptrap.description": "Value Error", "snmptrap.hostname": "PDDEMON01(*************)", "snmptrap.eventName": "ZONE6 - F6.07", "host.ip": "**************"}, "output": {"severity": 4, "agent_id": 0, "clear_type": "automatic", "manager": "mon-struxureware", "metric_type": "/HardwareEvent/", "action_class": "TE", "top_level": "A1421", "event_id": "nbErrorCond_86298D67", "handle_time": "2024-01-01 12:00:01", "metric_name": "ZONE6 - F", "ci_id": "PDDEMON01", "event_type": "problem", "raise_time": "2024-07-05 08:54:55", "clear_time": null, "node": "*************", "node_alias": "PDDEMON01", "summary": "ZONE6 - F6.07: OFF.", "additional_data": "{\"delay\": 600, \"event_name\": \"ZONE6 - F6.07\"}", "wake_up_time": "2024-07-05 09:04:55"}}]}