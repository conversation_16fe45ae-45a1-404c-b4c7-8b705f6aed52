"""Module to provide DB interactions to the icinga objects."""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Sequence

import pandas as pd
from sqlalchemy import <PERSON><PERSON>ey, create_engine, literal_column, select
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import DeclarativeBase, Mapped, Session, mapped_column

from olympus_common.db import Base, SessionManager
from olympus_common.utils import now_naive
from optic_matcher.config import config


@dataclass
class OpticBase(DeclarativeBase):
    """Represent the base model of UCMDB views."""

    @property
    def session(self) -> Session:
        """Create session."""
        engine = create_engine(url=config.optic_db_config.to_conninfo())
        return Session(engine)

    @classmethod
    def to_dict(cls, objects) -> list:
        """Convert object to dict."""
        return [obj.__dict__ if isinstance(obj, cls) else obj[0].__dict__ for obj in objects]

    def get_all(self) -> Sequence[object]:
        """Get all CI's or Metrics object in UCMDB views."""
        stmt = select(self.__class__)
        return self.session.execute(stmt).all()

    def get_from_field(self, target_field: str, target_value: str) -> Sequence[object]:
        """Select CI's or Metrics based on a specific field and specific value."""
        stmt = select(self.__class__).where(literal_column(target_field) == target_value)
        return self.session.execute(stmt).all()

    def get_all_df(self) -> pd.DataFrame:
        """Return a dataframe from get all."""
        data = self.to_dict(self.get_all())
        df = pd.DataFrame(data).drop_duplicates(ignore_index=True)
        return df.drop(columns=["_sa_instance_state"]) if data else pd.DataFrame()

    def get_from_field_df(self, target_field: str, target_value: str) -> pd.DataFrame:
        """Return a dataframe from get something from a field."""
        data = self.to_dict(self.get_from_field(target_field, target_value))
        df = pd.DataFrame(data).drop_duplicates(ignore_index=True)
        return df.drop(columns=["_sa_instance_state"]) if data else pd.DataFrame()

    def get_one_by_field(self, target_field: str, target_value: str) -> object:
        """Select a one CI's or Metrics  based on a specific field and specific value."""
        stmt = select(self.__class__).where(literal_column(target_field) == target_value)
        return self.session.scalars(stmt).first()


@dataclass
class ReporterStatusJournal(OpticBase):
    """Represent the reporter_status_journal table in Optic."""

    __tablename__ = "reporter_status_journal"

    serial: Mapped[Optional[int]] = mapped_column(primary_key=True)
    agent: Mapped[Optional[str]] = mapped_column()
    ci_id: Mapped[Optional[str]] = mapped_column()
    monitoredelementname: Mapped[Optional[str]] = mapped_column()
    metrics: Mapped[Optional[str]] = mapped_column()
    raisetime: Mapped[Optional[datetime]] = mapped_column()
    actionablealarm: Mapped[Optional[int]] = mapped_column()
    ci_status: Mapped[Optional[int]] = mapped_column()
    enrichmentflag: Mapped[Optional[int]] = mapped_column()


@dataclass
class OpticAlarm(Base):
    """Represent an Optic alarm."""

    __tablename__ = "s2110_optic_alarm"

    id: Mapped[int] = mapped_column(primary_key=True)
    serial: Mapped[Optional[int]] = mapped_column()
    agent: Mapped[Optional[str]] = mapped_column()
    ci_id: Mapped[Optional[str]] = mapped_column()
    monitoredelementname: Mapped[Optional[str]] = mapped_column()
    metrics: Mapped[Optional[str]] = mapped_column()
    raisetime: Mapped[Optional[datetime]] = mapped_column()
    import_time: Mapped[datetime] = mapped_column()
    enrichmentflag: Mapped[Optional[int]] = mapped_column()
    ci_status: Mapped[Optional[int]] = mapped_column()
    actionablealarm: Mapped[Optional[int]] = mapped_column()

    @classmethod
    def insert_one(cls, data: "ReporterStatusJournal", session: Session) -> Optional["OpticAlarm"]:
        """Insert an Optic Alarm in the database.

        Note: there is a unique constraint on the serial field.
        """
        optic_alarm = OpticAlarm(
            serial=data.serial,
            agent=data.agent,
            ci_id=data.ci_id,
            monitoredelementname=data.monitoredelementname,
            metrics=data.metrics,
            raisetime=data.raisetime,
            import_time=now_naive(),
            actionablealarm=data.actionablealarm,
        )
        asdict = optic_alarm.asdict()
        return session.scalar(insert(OpticAlarm).on_conflict_do_nothing().returning(OpticAlarm), asdict)


@dataclass
class MatchedAlarm(Base):
    """Represent a Matched Alarm."""

    __tablename__ = "s2110_matched_alarm"

    id: Mapped[int] = mapped_column(primary_key=True)
    agent_id: Mapped[Optional[int]] = mapped_column(ForeignKey("s2110_agent.id"))
    ci_id: Mapped[Optional[str]] = mapped_column()
    metric_type: Mapped[Optional[str]] = mapped_column()
    metric_name: Mapped[Optional[str]] = mapped_column()
    raise_time: Mapped[Optional[datetime]] = mapped_column()
    optic_alarm_id: Mapped[int] = mapped_column(ForeignKey("s2110_optic_alarm.id"))
    alarm_id: Mapped[int] = mapped_column(ForeignKey("s2110_alarm.id"))
    match_time: Mapped[Optional[datetime]] = mapped_column(default=now_naive())

    @classmethod
    def insert_one(cls, data: dict, session: Session) -> Optional["MatchedAlarm"]:
        """Insert an Optic Alarm in the database.

        Note: there is a unique constraint on the serial field.
        """
        return session.scalar(insert(MatchedAlarm).on_conflict_do_nothing().returning(MatchedAlarm), data)

    @classmethod
    def insert_many(cls, data: list[dict], session: Session) -> None:
        """Insert multiple matched alarms in the database."""
        for record in data:
            MatchedAlarm.insert_one(record, session)

        return


def create_optic_session_manager() -> SessionManager:
    """Create a database session manager for Optic."""
    return SessionManager(config.optic_db_config.to_conninfo(), echo=False)
