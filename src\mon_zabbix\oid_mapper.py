"""OID mapping for Zabbix."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Zabbix."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "trigger_comments": "trigger_comments",
            "name": "summary",
            "severity": "severity",
            "hostname": "host",
            "value": "problem",
            "clock": "clock",
            "eventid": "event_id",
            "os": "os",
            "object_1": "object_1",
            "object_2": "object_2",
            "object_3": "object_3",
            "object_4": "object_4",
            "metric_name": "metric_name",
            "metric_type": "metric_type",
            "ci_id": "event_ci_id",
        }
