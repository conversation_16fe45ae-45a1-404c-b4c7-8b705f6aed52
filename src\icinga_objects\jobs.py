"""Manage jobs for the synchronisation with UCMDB."""

import logging

from sqlalchemy.orm import Session

from icinga_objects.model import ci_relations, cis, metrics
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType


@trace_scan(MeasureType.CUSTOM.value)
def cis_job(ci_type: str, ucmdb_session: Session) -> None:
    """Synchronise CIs."""
    logging.info(f"-- Starting CI's job for {ci_type} --")
    c = cis.CIs(ci_type, ucmdb_session)
    c.sync_with_icinga()
    logging.info(f"-- Finished CI's job for {ci_type} --")


@trace_scan(MeasureType.CUSTOM.value)
def metrics_job(source: str, ucmdb_session: Session) -> None:
    """Add metrics in Icinga."""
    logging.info(f"Starting metrics job for {source}")
    gmt = metrics.GroupMetric(source, ucmdb_session)
    gmt.sync_with_icinga()
    logging.info(f"Finished metrics job for {source}")


@trace_scan(MeasureType.CUSTOM.value)
def relation_job(ucmdb_session: Session) -> None:
    """Synchronise Relations."""
    logging.info("-- Starting relation job --")
    relation = ci_relations.CIRelations(ucmdb_session)
    relation.sync_with_icinga()
    logging.info("-- Finished relation job --")
