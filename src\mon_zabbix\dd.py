"""Detail design implementation for mon-zabbix."""

import pandas as pd

from mon_zabbix import common, ictops, lcc_ucc_sca, statics
from mon_zabbix.config import config
from olympus_common import enums
from olympus_common.elastic_apm import CaptureSpan, trace_scan
from olympus_common.enums import ZabbixCC
from olympus_common.exceptions import ConfigurationError


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Run the zabbix DD."""
    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Add all the DDs fields to the given DataFrame and return it."""
    # Drop rows with all "N/A"s
    df_records = _drop_empty_rows(df)

    # Drop rows with severity 0 and value not 0 (i.e. problems with severity of 0)
    df_records = df_records[(df_records["severity"] != "0") | (df_records["problem"] == "0")]

    if df_records.empty:
        return df_records

    # Transform list fields into str fields for usage
    df_records["os"] = df_records.apply(lambda row: common.transform_list_field_to_str_field(row, "os"), axis=1)
    df_records["host"] = df_records.apply(lambda row: common.transform_list_field_to_str_field(row, "host"), axis=1)

    # Determine ZabbixType
    df_records["zabbix_type"] = df_records.apply(lambda row: common.zabbix_type(row, config.zabbix_cc), axis=1)

    # Hardcoded fields:
    df_records["agent_id"] = agent_id
    df_records["clear_type"] = common.clear_type()
    df_records["manager"] = common.manager(config.zabbix_cc)
    df_records["action_class"] = common.scope()
    df_records["handle_time"] = common.handle_time()

    # ICTOPS fields
    if config.zabbix_cc == ZabbixCC.ICTOPS.value:
        df_records["metric_name"] = df_records.apply(ictops.element_monitored_name, axis=1)
        df_records["metric_type"] = df_records.apply(ictops.metrics, axis=1)
        df_records["ci_id"] = df_records.apply(ictops.ci_id, axis=1)
        df_records["alert_group"] = df_records.apply(ictops.alert_group, axis=1)  # only to compute the platform
        df_records["platform"] = df_records.apply(common.platform, axis=1)
        df_records[["node", "node_alias"]] = df_records.apply(ictops.node_node_alias, axis=1)
        df_records["event_type"] = df_records.apply(ictops.clear_level, axis=1)
        df_records["severity"] = df_records.apply(ictops.severity, axis=1)
        df_records[["raise_time", "clear_time"]] = df_records.apply(ictops.raise_time_clear_time, axis=1)
        df_records["wake_up_time"] = df_records.apply(ictops.wake_up_time, axis=1)
        df_records["actionable"] = df_records.apply(ictops.actionable_alarm, axis=1)

    # AIX, UNIX, LINUX, SCADA fields
    elif config.zabbix_cc in {ZabbixCC.UCC.value, ZabbixCC.LCC.value, ZabbixCC.SCADA.value}:
        # Creation of the fields based on the schema of the DD
        df_records["top_level"] = lcc_ucc_sca.top_level()
        df_records[["metric_type", "metric_subtype"]] = df_records.apply(lcc_ucc_sca.metric_type_metric_subtype, axis=1)
        df_records[["object_3", "object_4"]] = df_records.apply(lcc_ucc_sca.object1_object2, axis=1)
        df_records["key_length"] = df_records.apply(lcc_ucc_sca.key_length, axis=1)
        df_records["metrics_key"] = df_records.apply(lcc_ucc_sca.metrics_key, axis=1)
        df_records[["metric_name", "metric_type", "ci_src"]] = df_records.apply(
            lcc_ucc_sca.lookup_table_elements, axis=1
        )
        df_records[["metric_name", "metric_type", "actionable"]] = df_records.apply(
            lcc_ucc_sca.element_name_metrics_actionable, axis=1
        )
        df_records["ci_id"] = df_records.apply(lcc_ucc_sca.ci_id, axis=1)
        df_records["additional_data"] = df_records.apply(lcc_ucc_sca.extended_attributes, axis=1)
        df_records["alert_group"] = df_records.apply(lcc_ucc_sca.alert_group, axis=1)  # only to compute the platform
        df_records["platform"] = df_records.apply(common.platform, axis=1)
        df_records[["node", "node_alias"]] = df_records.apply(lcc_ucc_sca.node_node_alias, axis=1)
        df_records["event_type"] = df_records.apply(lcc_ucc_sca.clear_level, axis=1)
        df_records["severity"] = df_records.apply(lcc_ucc_sca.severity, axis=1)
        df_records[["raise_time", "clear_time"]] = df_records.apply(lcc_ucc_sca.raise_time_clear_time, axis=1)
        df_records["wake_up_time"] = df_records.apply(lcc_ucc_sca.wake_up_time, axis=1)
        df_records["actionable"] = df_records.apply(lcc_ucc_sca.actionable_alarm, axis=1)
    else:
        raise ConfigurationError(f"{config.zabbix_cc} is not in the proposed values: {list(ZabbixCC)}")

    return df_records


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _drop_empty_rows(df: pd.DataFrame) -> pd.DataFrame:
    """Drop the empty rows of the dataframe."""
    return df[
        (df["trigger_comments"] != "")
        | (df["metric_name"] != statics.NA_STR)
        | (df["metric_type"] != statics.NA_STR)
        | (df["object_1"] != statics.NA_STR)
        | (df["object_2"] != statics.NA_STR)
        | (df["object_3"] != statics.NA_STR)
        | (df["object_4"] != statics.NA_STR)
    ]
