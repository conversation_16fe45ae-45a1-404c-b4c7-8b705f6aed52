"""OID mapping for CNMS."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for CNMS."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "SNMPv2-MIB::snmpTrapOID.0": "sourcetype",
            "@timestamp": "source_timestamp",
            "snmptrap.hostname": "hostname",
            "snmptrap.alarmid": "alarmid",
            "snmptrap.eventuei": "eventuei",
            "snmptrap.nodelabel": "nodelabel",
            "snmptrap.ipaddr": "ipaddr",
            "snmptrap.servicename": "servicename",
            "snmptrap.alarmtype": "alarmtype",
            "snmptrap.severityname": "severityname",
            "snmptrap.firsteventtime": "firsteventtime",
            "snmptrap.lasteventtime": "lasteventtime",
            "snmptrap.logmsg": "logmsg",
            "snmptrap.description": "description",
            "snmptrap.operinstruct": "operinstruct",
        }
