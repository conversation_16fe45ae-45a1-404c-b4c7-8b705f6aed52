{"data": [{"input": {"SNMPv2-SMI::enterprises.44645.324.1.2.14": "nonprod-ocp.infrabel.be", "@timestamp": "2024-12-05T08:45:18.902438775Z", "@version": "1", "SNMPv2-SMI::enterprises.44645.324.1.2.2": "nonprod-ocp.infrabel.be", "SNMPv2-SMI::enterprises.44645.324.1.2.3": "Infrabel", "SNMPv2-SMI::enterprises.44645.324.1.2.5": "", "SNMPv2-SMI::enterprises.44645.324.1.2.99": "{\"annotations\":{\"description\":\"This is an alert meant to ensure that the entire alerting pipeline is functional.\\nThis alert is always firing, therefore it should always be firing in Alertmanager\\nand always fire against a receiver. There are integrations with various notification\\nmechanisms that send a notification when this alert is not firing. For example the\\n\\\"DeadMansSnitch\\\" integration in PagerDuty.\\n\",\"summary\":\"An alert that should always be firing to certify that Alertmanager is working properly.\"},\"endsAt\":\"0001-01-01T00:00:00Z\",\"fingerprint\":\"6934731368443c07\",\"generatorURL\":\"https://console.apps.nonprod-ocp.infrabel.be/monitoring/graph?g0.expr=vector%281%29&g0.tab=1\",\"labels\":{\"alertname\":\"Watchdog\",\"namespace\":\"openshift-monitoring\",\"openshift_io_alert_source\":\"platform\",\"prometheus\":\"openshift-monitoring/k8s\",\"severity\":\"none\"},\"startsAt\":\"2024-09-26T09:02:40.811Z\",\"status\":\"firing\"}", "SNMPv2-SMI::enterprises.44645.324.1.2.9": "2024-09-26T09:02:40.811Z", "message": "#<SNMP::SNMPv2_Trap:0x22b5d9ad @varbind_list=[#<SNMP::VarBind:0x7741e748 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x2167bf5b @value=603973177>>, #<SNMP::VarBind:0x2d93626b @name=[*******.*******.4.1.0], @value=[*******.4.1.44645.324.1]>, #<SNMP::VarBind:0x79e8c098 @name=[*******.4.1.44645.324.1.2.1], @value=\"A1647\">, #<SNMP::VarBind:0x1eb134a @name=[*******.4.1.44645.324.1.2.2], @value=\"nonprod-ocp.infrabel.be\">, #<SNMP::VarBind:0x1deae94b @name=[*******.4.1.44645.324.1.2.3], @value=\"Infrabel\">, #<SNMP::VarBind:0x2bdc40a7 @name=[*******.4.1.44645.324.1.2.4], @value=\"openshift-monitoring\">, #<SNMP::VarBind:0x140fa944 @name=[*******.4.1.44645.324.1.2.5], @value=\"\">, #<SNMP::VarBind:0x4d2799cc @name=[*******.4.1.44645.324.1.2.6], @value=\"openshift-monitoring\">, #<SNMP::VarBind:0x74d8192 @name=[*******.4.1.44645.324.1.2.7], @value=\"Watchdog\">, #<SNMP::VarBind:0x4e5d30a4 @name=[*******.4.1.44645.324.1.2.8], @value=\"nonprod-ocp.infrabel.be 3893d5275809e4c2d206638cea71a4c2\">, #<SNMP::VarBind:0x2771f5a2 @name=[*******.4.1.44645.324.1.2.9], @value=\"2024-09-26T09:02:40.811Z\">, #<SNMP::VarBind:0x56e9e179 @name=[*******.4.1.44645.324.1.2.10], @value=\"0001-01-01T00:00:00Z\">, #<SNMP::VarBind:0x3d43bb27 @name=[*******.4.1.44645.324.1.2.11], @value=\"FIRING\">, #<SNMP::VarBind:0x1a3c643b @name=[*******.4.1.44645.324.1.2.12], @value=\"INFO\">, #<SNMP::VarBind:0x32485e3b @name=[*******.4.1.44645.324.1.2.13], @value=\"nonprod-ocp.infrabel.be Watchdog openshift-monitoring This is an alert meant to ensure that the entire alerting pipeline is functional. This alert is always firing, therefore it should always be firing in Alertmanager and always fire against a receiver. There are integrations with various notification mechanisms that send a notification when this alert is not firing. For example the \\\"DeadMansSnitch\\\" integration in PagerDuty. \">, #<SNMP::VarBind:0x2bb38322 @name=[*******.4.1.44645.324.1.2.14], @value=\"nonprod-ocp.infrabel.be\">, #<SNMP::VarBind:0xb898ff0 @name=[*******.4.1.44645.324.1.2.99], @value=\"{\\\"annotations\\\":{\\\"description\\\":\\\"This is an alert meant to ensure that the entire alerting pipeline is functional.\\\\nThis alert is always firing, therefore it should always be firing in Alertmanager\\\\nand always fire against a receiver. There are integrations with various notification\\\\nmechanisms that send a notification when this alert is not firing. For example the\\\\n\\\\\\\"DeadMansSnitch\\\\\\\" integration in PagerDuty.\\\\n\\\",\\\"summary\\\":\\\"An alert that should always be firing to certify that Alertmanager is working properly.\\\"},\\\"endsAt\\\":\\\"0001-01-01T00:00:00Z\\\",\\\"fingerprint\\\":\\\"6934731368443c07\\\",\\\"generatorURL\\\":\\\"https://console.apps.nonprod-ocp.infrabel.be/monitoring/graph?g0.expr=vector%281%29&g0.tab=1\\\",\\\"labels\\\":{\\\"alertname\\\":\\\"Watchdog\\\",\\\"namespace\\\":\\\"openshift-monitoring\\\",\\\"openshift_io_alert_source\\\":\\\"platform\\\",\\\"prometheus\\\":\\\"openshift-monitoring/k8s\\\",\\\"severity\\\":\\\"none\\\"},\\\"startsAt\\\":\\\"2024-09-26T09:02:40.811Z\\\",\\\"status\\\":\\\"firing\\\"}\">], @request_id=1484308923, @error_index=0, @error_status=0, @source_ip=\"***********\">", "SNMPv2-SMI::enterprises.44645.324.1.2.6": "openshift-monitoring", "type": "snmp_trap", "SNMPv2-SMI::enterprises.44645.324.1.2.13": "nonprod-ocp.infrabel.be Watchdog openshift-monitoring This is an alert meant to ensure that the entire alerting pipeline is functional. This alert is always firing, therefore it should always be firing in Alertmanager and always fire against a receiver. There are integrations with various notification mechanisms that send a notification when this alert is not firing. For example the \"DeadMansSnitch\" integration in PagerDuty. ", "host": "***********", "SNMPv2-SMI::enterprises.44645.324.1.2.4": "openshift-monitoring", "SNMPv2-SMI::enterprises.44645.324.1.2.12": "INFO", "SNMPv2-SMI::enterprises.44645.324.1.2.7": "Watchdog", "SNMPv2-MIB::sysUpTime.0": "69 days, 21:42:11.77", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.44645.324.1", "SNMPv2-SMI::enterprises.44645.324.1.2.1": "A1647", "SNMPv2-SMI::enterprises.44645.324.1.2.11": "FIRING", "SNMPv2-SMI::enterprises.44645.324.1.2.10": "0001-01-01T00:00:00Z", "SNMPv2-SMI::enterprises.44645.324.1.2.8": "nonprod-ocp.infrabel.be 3893d5275809e4c2d206638cea71a4c2"}, "output": {"agent_id": 0, "metric_type": "/OpenShift/", "manager": "mon-openshift", "action_class": "IT", "handle_time": "2024-01-01 12:00:01", "actionable": true, "ci_id": "nonprod-ocp.infrabel.be", "clear_type": "manual", "metric_name": "Watchdog", "node": "heartbeat OpenShift openshift-monitoring nonprod-ocp", "node_alias": "heartbeat OpenShift openshift-monitoring nonprod-ocp", "raise_time": "2024-12-05 08:45:18.902438", "clear_time": null, "severity": 1, "summary": "OpenShift Heartbeat Message", "event_type": "heartbeat", "top_level": "A1647"}}, {"input": {"iso.org.dod.internet.private.enterprises.44645.324.1.2.1": "A1647", "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTrap.snmpTrapOID.0": "*******.4.1.44645.324.1", "iso.org.dod.internet.private.enterprises.44645.324.1.2.99": "{\"annotations\":{\"description\":\"This is an alert meant to ensure that the entire alerting pipeline is functional.\\nThis alert is always firing, therefore it should always be firing in Alertmanager\\nand always fire against a receiver. There are integrations with various notification\\nmechanisms that send a notification when this alert is not firing. For example the\\n\\\"DeadMansSnitch\\\" integration in PagerDuty.\\n\",\"summary\":\"An alert that should always be firing to certify that Alertmanager is working properly.\"},\"endsAt\":\"0001-01-01T00:00:00Z\",\"fingerprint\":\"6934731368443c07\",\"generatorURL\":\"https://console.apps.rts.ocp.infrabel.be/monitoring/graph?g0.expr=vector%281%29&g0.tab=1\",\"labels\":{\"alertname\":\"Watchdog\",\"namespace\":\"openshift-monitoring\",\"openshift_io_alert_source\":\"platform\",\"prometheus\":\"openshift-monitoring/k8s\",\"severity\":\"none\"},\"startsAt\":\"2025-02-19T08:53:27.424Z\",\"status\":\"firing\"}", "iso.org.dod.internet.private.enterprises.44645.324.1.2.14": "rts.ocp.infrabel.be", "@timestamp": "2025-03-27T08:00:30.957003915Z", "iso.org.dod.internet.private.enterprises.44645.324.1.2.2": "rts.ocp.infrabel.be", "host": "***********", "iso.org.dod.internet.private.enterprises.44645.324.1.2.4": "openshift-monitoring", "iso.org.dod.internet.private.enterprises.44645.324.1.2.7": "Watchdog", "iso.org.dod.internet.private.enterprises.44645.324.1.2.9": "2025-02-19T08:53:27.424Z", "iso.org.dod.internet.private.enterprises.44645.324.1.2.3": "Ocp.infrabel", "iso.org.dod.internet.mgmt.mib-2.system.sysUpTime.sysUpTimeInstance": 310796502, "type": "snmp_trap", "message": "{\"error_index\":0,\"variable_bindings\":{\"*******.4.1.44645.324.1.2.5\":\"\",\"*******.4.1.44645.324.1.2.6\":\"openshift-monitoring\",\"*******.4.1.44645.324.1.2.7\":\"Watchdog\",\"*******.4.1.44645.324.1.2.8\":\"rts.ocp.infrabel.be 3893d5275809e4c2d206638cea71a4c2\",\"*******.4.1.44645.324.1.2.9\":\"2025-02-19T08:53:27.424Z\",\"*******.4.1.44645.324.1.2.1\":\"A1647\",\"*******.4.1.44645.324.1.2.2\":\"rts.ocp.infrabel.be\",\"*******.4.1.44645.324.1.2.3\":\"Ocp.infrabel\",\"*******.4.1.44645.324.1.2.14\":\"rts.ocp.infrabel.be\",\"*******.*******.0\":310796502,\"*******.4.1.44645.324.1.2.4\":\"openshift-monitoring\",\"*******.4.1.44645.324.1.2.12\":\"INFO\",\"*******.4.1.44645.324.1.2.13\":\"rts.ocp.infrabel.be Watchdog openshift-monitoring This is an alert meant to ensure that the entire alerting pipeline is functional. This alert is always firing, therefore it should always be firing in Alertmanager and always fire against a receiver. There are integrations with various notification mechanisms that send a notification when this alert is not firing. For example the \\\"DeadMansSnitch\\\" integration in PagerDuty. \",\"*******.4.1.44645.324.1.2.10\":\"0001-01-01T00:00:00Z\",\"*******.4.1.44645.324.1.2.11\":\"FIRING\",\"*******.4.1.44645.324.1.2.99\":\"{\\\"annotations\\\":{\\\"description\\\":\\\"This is an alert meant to ensure that the entire alerting pipeline is functional.\\\\nThis alert is always firing, therefore it should always be firing in Alertmanager\\\\nand always fire against a receiver. There are integrations with various notification\\\\nmechanisms that send a notification when this alert is not firing. For example the\\\\n\\\\\\\"DeadMansSnitch\\\\\\\" integration in PagerDuty.\\\\n\\\",\\\"summary\\\":\\\"An alert that should always be firing to certify that Alertmanager is working properly.\\\"},\\\"endsAt\\\":\\\"0001-01-01T00:00:00Z\\\",\\\"fingerprint\\\":\\\"6934731368443c07\\\",\\\"generatorURL\\\":\\\"https://console.apps.rts.ocp.infrabel.be/monitoring/graph?g0.expr=vector%281%29&g0.tab=1\\\",\\\"labels\\\":{\\\"alertname\\\":\\\"Watchdog\\\",\\\"namespace\\\":\\\"openshift-monitoring\\\",\\\"openshift_io_alert_source\\\":\\\"platform\\\",\\\"prometheus\\\":\\\"openshift-monitoring/k8s\\\",\\\"severity\\\":\\\"none\\\"},\\\"startsAt\\\":\\\"2025-02-19T08:53:27.424Z\\\",\\\"status\\\":\\\"firing\\\"}\",\"*******.*******.4.1.0\":\"*******.4.1.44645.324.1\"},\"error_status\":0,\"type\":\"TRAP\",\"error_status_text\":\"Success\",\"community\":\"openshift\",\"version\":\"2c\",\"request_id\":2124559453}", "iso.org.dod.internet.private.enterprises.44645.324.1.2.11": "FIRING", "iso.org.dod.internet.private.enterprises.44645.324.1.2.12": "INFO", "iso.org.dod.internet.private.enterprises.44645.324.1.2.6": "openshift-monitoring", "@version": "1", "iso.org.dod.internet.private.enterprises.44645.324.1.2.13": "rts.ocp.infrabel.be Watchdog openshift-monitoring This is an alert meant to ensure that the entire alerting pipeline is functional. This alert is always firing, therefore it should always be firing in Alertmanager and always fire against a receiver. There are integrations with various notification mechanisms that send a notification when this alert is not firing. For example the \"DeadMansSnitch\" integration in PagerDuty. ", "iso.org.dod.internet.private.enterprises.44645.324.1.2.8": "rts.ocp.infrabel.be 3893d5275809e4c2d206638cea71a4c2", "iso.org.dod.internet.private.enterprises.44645.324.1.2.10": "0001-01-01T00:00:00Z", "iso.org.dod.internet.private.enterprises.44645.324.1.2.5": ""}, "output": {}}, {"input": {"SNMPv2-SMI::enterprises.44645.324.1.2.7": "Watchdog", "SNMPv2-SMI::enterprises.44645.324.1.2.6": "openshift-monitoring", "SNMPv2-SMI::enterprises.44645.324.1.2.3": "Infrabel", "SNMPv2-SMI::enterprises.44645.324.1.2.12": "INFO", "@timestamp": "2025-03-27T11:03:35.007428388Z", "SNMPv2-SMI::enterprises.44645.324.1.2.4": "openshift-monitoring", "SNMPv2-SMI::enterprises.44645.324.1.2.14": "ocp.infrabel.be", "SNMPv2-SMI::enterprises.44645.324.1.2.10": "0001-01-01T00:00:00Z", "SNMPv2-SMI::enterprises.44645.324.1.2.8": "ocp.infrabel.be 3893d5275809e4c2d206638cea71a4c2", "@version": "1", "SNMPv2-SMI::enterprises.44645.324.1.2.2": "ocp.infrabel.be", "SNMPv2-SMI::enterprises.44645.324.1.2.11": "FIRING", "message": "{\"error_index\":0,\"variable_bindings\":{\"*******.4.1.44645.324.1.2.5\":\"\",\"*******.4.1.44645.324.1.2.6\":\"openshift-monitoring\",\"*******.4.1.44645.324.1.2.7\":\"Watchdog\",\"*******.4.1.44645.324.1.2.8\":\"ocp.infrabel.be 3893d5275809e4c2d206638cea71a4c2\",\"*******.4.1.44645.324.1.2.9\":\"2025-02-20T07:48:30.548Z\",\"*******.4.1.44645.324.1.2.1\":\"A1647\",\"*******.4.1.44645.324.1.2.2\":\"ocp.infrabel.be\",\"*******.4.1.44645.324.1.2.3\":\"Infrabel\",\"*******.4.1.44645.324.1.2.14\":\"ocp.infrabel.be\",\"*******.*******.0\":302483771,\"*******.4.1.44645.324.1.2.4\":\"openshift-monitoring\",\"*******.4.1.44645.324.1.2.12\":\"INFO\",\"*******.4.1.44645.324.1.2.13\":\"ocp.infrabel.be Watchdog openshift-monitoring This is an alert meant to ensure that the entire alerting pipeline is functional. This alert is always firing, therefore it should always be firing in Alertmanager and always fire against a receiver. There are integrations with various notification mechanisms that send a notification when this alert is not firing. For example the \\\"DeadMansSnitch\\\" integration in PagerDuty. \",\"*******.4.1.44645.324.1.2.10\":\"0001-01-01T00:00:00Z\",\"*******.4.1.44645.324.1.2.11\":\"FIRING\",\"*******.4.1.44645.324.1.2.99\":\"{\\\"annotations\\\":{\\\"description\\\":\\\"This is an alert meant to ensure that the entire alerting pipeline is functional.\\\\nThis alert is always firing, therefore it should always be firing in Alertmanager\\\\nand always fire against a receiver. There are integrations with various notification\\\\nmechanisms that send a notification when this alert is not firing. For example the\\\\n\\\\\\\"DeadMansSnitch\\\\\\\" integration in PagerDuty.\\\\n\\\",\\\"summary\\\":\\\"An alert that should always be firing to certify that Alertmanager is working properly.\\\"},\\\"endsAt\\\":\\\"0001-01-01T00:00:00Z\\\",\\\"fingerprint\\\":\\\"6934731368443c07\\\",\\\"generatorURL\\\":\\\"https://console.apps.ocp.infrabel.be/monitoring/graph?g0.expr=vector%281%29&g0.tab=1\\\",\\\"labels\\\":{\\\"alertname\\\":\\\"Watchdog\\\",\\\"namespace\\\":\\\"openshift-monitoring\\\",\\\"openshift_io_alert_source\\\":\\\"platform\\\",\\\"prometheus\\\":\\\"openshift-monitoring/k8s\\\",\\\"severity\\\":\\\"none\\\"},\\\"startsAt\\\":\\\"2025-02-20T07:48:30.548Z\\\",\\\"status\\\":\\\"firing\\\"}\",\"*******.*******.4.1.0\":\"SNMPv2-SMI::enterprises.44645.324.1\"},\"error_status\":0,\"type\":\"TRAP\",\"error_status_text\":\"Success\",\"community\":\"openshift\",\"version\":\"2c\",\"request_id\":483836368}", "type": "snmp_trap", "SNMPv2-SMI::enterprises.44645.324.1.2.9": "2025-02-20T07:48:30.548Z", "host": "***********", "SNMPv2-SMI::enterprises.44645.324.1.2.13": "ocp.infrabel.be Watchdog openshift-monitoring This is an alert meant to ensure that the entire alerting pipeline is functional. This alert is always firing, therefore it should always be firing in Alertmanager and always fire against a receiver. There are integrations with various notification mechanisms that send a notification when this alert is not firing. For example the \"DeadMansSnitch\" integration in PagerDuty. ", "SNMPv2-SMI::enterprises.44645.324.1.2.99": "{\"annotations\":{\"description\":\"This is an alert meant to ensure that the entire alerting pipeline is functional.\\nThis alert is always firing, therefore it should always be firing in Alertmanager\\nand always fire against a receiver. There are integrations with various notification\\nmechanisms that send a notification when this alert is not firing. For example the\\n\\\"DeadMansSnitch\\\" integration in PagerDuty.\\n\",\"summary\":\"An alert that should always be firing to certify that Alertmanager is working properly.\"},\"endsAt\":\"0001-01-01T00:00:00Z\",\"fingerprint\":\"6934731368443c07\",\"generatorURL\":\"https://console.apps.ocp.infrabel.be/monitoring/graph?g0.expr=vector%281%29&g0.tab=1\",\"labels\":{\"alertname\":\"Watchdog\",\"namespace\":\"openshift-monitoring\",\"openshift_io_alert_source\":\"platform\",\"prometheus\":\"openshift-monitoring/k8s\",\"severity\":\"none\"},\"startsAt\":\"2025-02-20T07:48:30.548Z\",\"status\":\"firing\"}", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.44645.324.1", "SNMPv2-MIB::sysUpTime.0": 302483771, "SNMPv2-SMI::enterprises.44645.324.1.2.5": "", "SNMPv2-SMI::enterprises.44645.324.1.2.1": "A1647"}, "output": {"ci_id": "ocp.infrabel.be", "clear_type": "manual", "metric_name": "Watchdog", "node": "heartbeat OpenShift openshift-monitoring ocp", "summary": "OpenShift Heartbeat Message", "event_type": "heartbeat"}}]}