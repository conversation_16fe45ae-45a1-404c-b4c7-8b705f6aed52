"""OID mapping for SAP Solman."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for SAP Solman."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "snmptrap.r3maiAlertMOName": "r3maiAlertMOName",
            "snmptrap.r3maiAlertMOType": "r3maiAlertMOType",
            "snmptrap.r3maiAlertId": "r3maiAlertId",
            "snmptrap.r3maiAlertDate": "r3maiAlertDate",
            "snmptrap.r3maiAlertTime": "r3maiAlertTime",
            "snmptrap.r3maiAlertName": "r3maiAlertName",
            "snmptrap.r3maiAlertTechnicalName": "r3maiAlertTechnicalName",
            "snmptrap.r3maiAlertDescription": "r3maiAlertDescription",
            "snmptrap.r3maiAlertCategory": "r3maiAlertCategory",
            "snmptrap.r3maiAlertRating": "r3maiAlertRating",
            "snmptrap.r3maiAlertSeverity": "r3maiAlertSeverity",
            "snmptrap.r3maiAlertMetricName": "r3maiAlertMetricName",
            "snmptrap.r3maiAlertMetricValue": "r3maiAlertMetricValue",
            "snmptrap.r3maiAlertMOId": "r3maiAlertMOId",
            "snmptrap.r3maiAlertStatus": "r3maiAlertStatus",
            "snmptrap.r3maiAlertReasonClosure": "r3maiAlertReasonClosure",
            "snmptrap.r3maiAlertPriority": "r3maiAlertPriority",
            "host": "host",
            "@timestamp": "messageTimestamp",
        }

    @property
    def unmapped_oids(self) -> dict[str, str]:
        """Return unmapped OIDs."""
        return {
            "SNMPv2-SMI::enterprises.694.*******.1.1": "r3maiAlertMOName",
            "SNMPv2-SMI::enterprises.694.*******.1.2": "r3maiAlertMOType",
            "SNMPv2-SMI::enterprises.694.*******.1.3": "r3maiAlertId",
            "SNMPv2-SMI::enterprises.694.*******.1.4": "r3maiAlertDate",
            "SNMPv2-SMI::enterprises.694.*******.1.5": "r3maiAlertTime",
            "SNMPv2-SMI::enterprises.694.*******.1.6": "r3maiAlertName",
            "SNMPv2-SMI::enterprises.694.*******.1.7": "r3maiAlertTechnicalName",
            "SNMPv2-SMI::enterprises.694.*******.1.8": "r3maiAlertDescription",
            "SNMPv2-SMI::enterprises.694.*******.1.9": "r3maiAlertCategory",
            "SNMPv2-SMI::enterprises.694.*******.1.10": "r3maiAlertRating",
            "SNMPv2-SMI::enterprises.694.*******.1.11": "r3maiAlertSeverity",
            "SNMPv2-SMI::enterprises.694.*******.1.12": "r3maiAlertMetricName",
            "SNMPv2-SMI::enterprises.694.*******.1.13": "r3maiAlertMetricValue",
            "SNMPv2-SMI::enterprises.694.*******.1.14": "r3maiAlertMOId",
            "SNMPv2-SMI::enterprises.694.*******.1.15": "r3maiAlertStatus",
            "SNMPv2-SMI::enterprises.694.*******.1.16": "r3maiAlertReasonClosure",
            "SNMPv2-SMI::enterprises.694.*******.1.17": "r3maiAlertPriority",
            "host": "host",
            "@timestamp": "messageTimestamp",
        }

    @property
    def drop_if_all_nan(self) -> list[str]:
        """Return fields to drop if all values are NaN."""
        return ["r3maiAlertMOName", "r3maiAlertTechnicalName", "r3maiAlertMOType"]
