"""OID mapping for Struxureware."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Struxureware."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "snmptrap.description": "description",
            "snmptrap.notifyTime": "notify_time",
            "snmptrap.responsible": "responsible",
            "snmptrap.severity": "severity",
            "snmptrap.resolveTime": "resolve_time",
            "snmptrap.startTime": "start_time",
            "snmptrap.eventName": "event_name",
            "snmptrap.errorId": "error_id",
            "snmptrap.hostname": "hostname",
            "snmptrap.errorType": "error_type",
            "snmptrap.location": "location",
            "snmptrap.sensorvalue1": "sensor_value_1",
            "@timestamp": "@timestamp",
        }

    @property
    def drop_if_all_nan(self) -> list[str]:
        """Return fields to drop if all values are NaN."""
        return ["description", "event_name", "hostname"]
