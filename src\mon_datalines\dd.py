"""Detail design implementation for mon-datalines."""

import numpy as np
import pandas as pd

from mon_datalines import utils
from mon_datalines.config import config
from olympus_common import enums
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Run the datalines details design script."""
    if config.debug:
        pd.set_option("display.max_columns", None)

    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Perform all the transformation needed for the Details Design."""
    df_records = df.replace({pd.NA: None, np.nan: None})

    # Hardcoded fields
    df_records["agent_id"] = agent_id
    df_records["clear_type"] = utils.clear_type()
    df_records["manager"] = utils.manager()
    df_records["action_class"] = utils.scope()
    df_records["metric_type"] = utils.metric_type()
    df_records["top_level"] = utils.top_level()
    df_records["event_id"] = utils.event_id()
    df_records["handle_time"] = utils.handle_time()

    # Computed fields
    df_records["ci_id"] = df_records.apply(lambda row: utils.ci_id(row["host"]), axis=1)
    df_records["node"] = df_records.apply(lambda row: utils.node(row["host"]), axis=1)
    df_records["node_alias"] = df_records.apply(lambda row: utils.node_alias(row["host"]), axis=1)
    df_records["raise_time"] = df_records.apply(lambda row: utils.raise_time(row["timestamp"]), axis=1)
    df_records["metric_name"] = df_records.apply(lambda row: utils.metric_name(row["tlsTrapDescription"]), axis=1)
    df_records["summary"] = df_records.apply(lambda row: utils.summary(row["tlsTrapDescription"]), axis=1)
    df_records["additional_data"] = df_records.apply(
        lambda row: utils.additional_data(row["tlsTrapDescription"]), axis=1
    )
    df_records["event_type"] = df_records.apply(lambda row: utils.event_type(row["tlsTrapDescription"]), axis=1)
    df_records["clear_time"] = df_records.apply(
        lambda row: utils.clear_time(row["event_type"], row["raise_time"]), axis=1
    )
    df_records["severity"] = df_records.apply(
        lambda row: utils.severity(str(row["tlsTrapSeverityLevel"]), row["event_type"]), axis=1
    )
    df_records["actionable"] = df_records.apply(lambda row: utils.actionable(row), axis=1)
    df_records["identifier"] = df_records.apply(
        lambda row: utils.identifier(row["tlsTrapDescription"], row["ifIndex1"] or row["ifIndex0"], row["host"]), axis=1
    )
    df_records["wake_up_time"] = df_records.apply(lambda row: utils.wake_up_time(row["raise_time"]), axis=1)

    return df_records
