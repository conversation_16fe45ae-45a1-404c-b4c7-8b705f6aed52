"""Configuration module for sap-client."""

from dataclasses import dataclass

from olympus_common.config import DatabaseConfig, JWKConfig, KafkaConsumerConfig, ServerConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import <PERSON>ton, strtobool


@dataclass(frozen=True)
class SAPConfig(metaclass=Singleton):
    """Represent the configuration for SAP."""

    host: str = env_field("SAP_HOST")
    service_root: str = env_field("SAP_SERVICE_ROOT")
    incident_resource_path: str = env_field("SAP_INCIDENT_RESOURCE_PATH")
    release_resource_path: str = env_field("SAP_RELEASE_RESOURCE_PATH")
    user: str = env_field("SAP_USER")
    password: str = env_field("SAP_PASSWORD")

    @property
    def incident_endpoint(self) -> str:
        """Return the sap endpoint."""
        return f"{self.host}{self.service_root}{self.incident_resource_path}"

    @property
    def release_endpoint(self) -> str:
        """Return the sap endpoint."""
        return f"{self.host}{self.service_root}{self.release_resource_path}"


@dataclass(frozen=True)
class AIRConfig(metaclass=Singleton):
    """Represent the configuration for AIR."""

    client_id: str = env_field("AIR_CLIENT_ID")
    client_secret: str = env_field("AIR_CLIENT_SECRET")
    token_url: str = env_field("AIR_TOKEN_URL")
    scope: str = env_field("AIR_SCOPE")
    grant_type: str = env_field("AIR_GRANT_TYPE")
    url_con: str = env_field("AIR_URL_CON")

    @property
    def api_credentials(self) -> dict:
        """Return the air credentials as a dict."""
        return {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "token_url": self.token_url,
            "scope": self.scope,
            "grant_type": self.grant_type,
        }


@dataclass(frozen=True)
class IcingaConfig(metaclass=Singleton):
    """Represent the configuration for Icinga."""

    api_endpoint: str = env_field("ICINGA_API_ENDPOINT")
    user: str = env_field("ICINGA_USER")
    password: str = env_field("ICINGA_PASSWORD")


@dataclass(frozen=True)
class Config(ServerConfig, metaclass=Singleton):
    """Represent the configuration for sap-client."""

    kafka_config: KafkaConsumerConfig = dataclass_field(KafkaConsumerConfig)
    database_config: DatabaseConfig = dataclass_field(DatabaseConfig)
    service_name: str = env_field("OLYMPUS_SERVICE_NAME", default="sap_air_client")
    app_env: str = env_field("APP_ENV", default="dev")

    # Service specific configs
    sap: SAPConfig = dataclass_field(SAPConfig)
    air: AIRConfig = dataclass_field(AIRConfig)
    icinga: IcingaConfig = dataclass_field(IcingaConfig)
    jwk_config: JWKConfig = dataclass_field(JWKConfig)
    lifespan_enabled: bool = env_field("LIFESPAN_ENABLED", astype=strtobool, default="1")
    thread_number: int = env_field("THREAD_NUMBER", astype=int, default="10")


config = Config()
