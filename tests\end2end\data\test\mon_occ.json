{"extra_env": {"OLYMPUS_AGENT_ID": "16"}, "agent_ids": [16], "data": [{"input": {"SNMPv2-SMI::enterprises.**********.1.51.1": "", "SNMPv2-SMI::enterprises.**********.1.40.1": "Heartbeat_OMS,rule 1048", "SNMPv2-SMI::enterprises.**********.1.83.1": "", "SNMPv2-SMI::enterprises.**********.1.61.1": "Metric GUID=3AD66194B63DF31D77F27C961CA8AFF9", "SNMPv2-SMI::enterprises.**********.1.19.1": "", "SNMPv2-SMI::enterprises.**********.1.43.1": "", "SNMPv2-SMI::enterprises.**********.1.44.1": "", "SNMPv2-SMI::enterprises.**********.1.2.1": "NOTIF_NORMAL", "SNMPv2-SMI::enterprises.**********.1.49.1": "", "SNMPv2-SMI::enterprises.**********.1.71.1": "", "SNMPv2-SMI::enterprises.**********.1.18.1": "", "SNMPv2-SMI::enterprises.**********.1.38.1": "", "SNMPv2-SMI::enterprises.**********.1.31.1": "", "SNMPv2-SMI::enterprises.**********.1.8.1": "", "SNMPv2-SMI::enterprises.**********.1.74.1": "", "SNMPv2-SMI::enterprises.**********.1.65.1": "Metric Group=ME$Heartbeat_OMS", "SNMPv2-SMI::enterprises.**********.1.84.1": "Number of keys=0", "SNMPv2-SMI::enterprises.**********.1.42.1": "2DEBE07D982CC5CCE063062AF90A93CF", "SNMPv2-SMI::enterprises.**********.1.27.1": "********.0", "host": "************", "SNMPv2-SMI::enterprises.**********.1.41.1": "RKC8900", "SNMPv2-SMI::enterprises.**********.1.52.1": "", "type": "snmp_trap", "SNMPv2-SMI::enterprises.**********.1.85.1": "", "SNMPv2-SMI::enterprises.**********.1.79.1": "", "SNMPv2-SMI::enterprises.**********.1.17.1": "No", "@version": "1", "SNMPv2-SMI::enterprises.**********.1.77.1": "", "SNMPv2-SMI::enterprises.**********.1.36.1": "", "SNMPv2-SMI::enterprises.**********.1.46.1": "HEARTBEAT", "SNMPv2-SMI::enterprises.**********.1.64.1": "Collection Name=ME$Heartbeat_OMS", "SNMPv2-SMI::enterprises.**********.1.30.1": "", "SNMPv2-SMI::enterprises.**********.1.72.1": "", "SNMPv2-SMI::enterprises.**********.1.67.1": "Metric Description=", "SNMPv2-SMI::enterprises.**********.1.75.1": "", "SNMPv2-SMI::enterprises.**********.1.29.1": "", "SNMPv2-SMI::enterprises.**********.1.78.1": "", "SNMPv2-SMI::enterprises.**********.1.34.1": "", "SNMPv2-SMI::enterprises.**********.1.7.1": "0", "SNMPv2-SMI::enterprises.**********.1.48.1": "", "SNMPv2-SMI::enterprises.**********.1.62.1": "Severity GUID=2DEBE07D9828C5CCE063062AF90A93CF", "SNMPv2-SMI::enterprises.**********.1.12.1": "", "SNMPv2-SMI::enterprises.**********.1.82.1": "", "@timestamp": "2025-02-12T05:22:08.922110124Z", "SNMPv2-SMI::enterprises.**********.1.26.1": "", "SNMPv2-SMI::enterprises.**********.1.39.1": "Heartbeat_OMS", "SNMPv2-SMI::enterprises.**********.1.70.1": "", "message": "#<SNMP::SNMPv1_Trap:0x1c9241f5 @enterprise=[*******.**********.2], @timestamp=#<SNMP::TimeTicks:0x35ae16af @value=63591>, @varbind_list=[#<SNMP::VarBind:0x3375a4b6 @name=[*******.4.1.**********.1.2.1], @value=\"NOTIF_NORMAL\">, #<SNMP::VarBind:0x3d66d6c4 @name=[*******.4.1.**********.1.3.1], @value=\"HEARTBEAT\">, #<SNMP::VarBind:0x7838b4a9 @name=[*******.4.1.**********.1.4.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2DEBE07D982CC5CCE063062AF90A93CF\">, #<SNMP::VarBind:0x7abe3a76 @name=[*******.4.1.**********.1.5.1], @value=\"Warning\">, #<SNMP::VarBind:0x64707183 @name=[*******.4.1.**********.1.6.1], @value=\"WARNING\">, #<SNMP::VarBind:0x6f2b4b3f @name=[*******.4.1.**********.1.7.1], @value=\"0\">, #<SNMP::VarBind:0x528b278f @name=[*******.4.1.**********.1.8.1], @value=\"\">, #<SNMP::VarBind:0x596dca22 @name=[*******.4.1.**********.1.9.1], @value=\"\">, #<SNMP::VarBind:0x3ef644e5 @name=[*******.4.1.**********.1.10.1], @value=\"Feb 12, 2025 6:21:33 AM CET\">, #<SNMP::VarBind:0xbb192b7 @name=[*******.4.1.**********.1.11.1], @value=\"\">, #<SNMP::VarBind:0xd6b11 @name=[*******.4.1.**********.1.12.1], @value=\"\">, #<SNMP::VarBind:0x37869de8 @name=[*******.4.1.**********.1.13.1], @value=\"Metric Alert\">, #<SNMP::VarBind:0x3b5b2691 @name=[*******.4.1.**********.1.14.1], @value=\"ME$Heartbeat_OMS:heartbeat_oms\">, #<SNMP::VarBind:0x3c782f67 @name=[*******.4.1.**********.1.15.1], @value=\"\">, #<SNMP::VarBind:0x57c69e1a @name=[*******.4.1.**********.1.16.1], @value=\"\">, #<SNMP::VarBind:0x54ac30d7 @name=[*******.4.1.**********.1.17.1], @value=\"No\">, #<SNMP::VarBind:0x3621eb7d @name=[*******.4.1.**********.1.18.1], @value=\"\">, #<SNMP::VarBind:0x4bdff9c9 @name=[*******.4.1.**********.1.19.1], @value=\"\">, #<SNMP::VarBind:0x280a98e6 @name=[*******.4.1.**********.1.20.1], @value=\"\">, #<SNMP::VarBind:0x1d953fb8 @name=[*******.4.1.**********.1.21.1], @value=\"iictyiaplv217.msnet.railb.be\">, #<SNMP::VarBind:0x24d65574 @name=[*******.4.1.**********.1.22.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=iictyiaplv217.msnet.railb.be&targetType=host\">, #<SNMP::VarBind:0x10f3e196 @name=[*******.4.1.**********.1.23.1], @value=\"Host\">, #<SNMP::VarBind:0x6c4296c6 @name=[*******.4.1.**********.1.24.1], @value=\"iictyiaplv217.msnet.railb.be\">, #<SNMP::VarBind:0x581e1c09 @name=[*******.4.1.**********.1.25.1], @value=\"SYSMAN\">, #<SNMP::VarBind:0x4e758f8b @name=[*******.4.1.**********.1.26.1], @value=\"\">, #<SNMP::VarBind:0x41853882 @name=[*******.4.1.**********.1.27.1], @value=\"********.0\">, #<SNMP::VarBind:0x398972d2 @name=[*******.4.1.**********.1.28.1], @value=\"Operating System=Linux, Platform=x86_64,\">, #<SNMP::VarBind:0x115fdfbe @name=[*******.4.1.**********.1.29.1], @value=\"\">, #<SNMP::VarBind:0x733403c9 @name=[*******.4.1.**********.1.30.1], @value=\"\">, #<SNMP::VarBind:0x1b9d6405 @name=[*******.4.1.**********.1.31.1], @value=\"\">, #<SNMP::VarBind:0x69f08a46 @name=[*******.4.1.**********.1.32.1], @value=\"\">, #<SNMP::VarBind:0x6bd1ae9 @name=[*******.4.1.**********.1.33.1], @value=\"\">, #<SNMP::VarBind:0x55447bbc @name=[*******.4.1.**********.1.34.1], @value=\"\">, #<SNMP::VarBind:0x2589905d @name=[*******.4.1.**********.1.35.1], @value=\"\">, #<SNMP::VarBind:0x48469c10 @name=[*******.4.1.**********.1.36.1], @value=\"\">, #<SNMP::VarBind:0x26c2bda3 @name=[*******.4.1.**********.1.37.1], @value=\"\">, #<SNMP::VarBind:0x50adbb5c @name=[*******.4.1.**********.1.38.1], @value=\"\">, #<SNMP::VarBind:0x394fd26c @name=[*******.4.1.**********.1.39.1], @value=\"Heartbeat_OMS\">, #<SNMP::VarBind:0x45156f49 @name=[*******.4.1.**********.1.40.1], @value=\"Heartbeat_OMS,rule 1048\">, #<SNMP::VarBind:0x7609fded @name=[*******.4.1.**********.1.41.1], @value=\"RKC8900\">, #<SNMP::VarBind:0x4bc3cf2b @name=[*******.4.1.**********.1.42.1], @value=\"2DEBE07D982CC5CCE063062AF90A93CF\">, #<SNMP::VarBind:0xc58a88d @name=[*******.4.1.**********.1.43.1], @value=\"\">, #<SNMP::VarBind:0x6259e168 @name=[*******.4.1.**********.1.44.1], @value=\"\">, #<SNMP::VarBind:0x5b069e5b @name=[*******.4.1.**********.1.45.1], @value=\"\">, #<SNMP::VarBind:0xee9a887 @name=[*******.4.1.**********.1.46.1], @value=\"HEARTBEAT\">, #<SNMP::VarBind:0x473c6517 @name=[*******.4.1.**********.1.47.1], @value=\"\">, #<SNMP::VarBind:0x6ac39e39 @name=[*******.4.1.**********.1.48.1], @value=\"\">, #<SNMP::VarBind:0x5d8317c @name=[*******.4.1.**********.1.49.1], @value=\"\">, #<SNMP::VarBind:0x46b60cd0 @name=[*******.4.1.**********.1.50.1], @value=\"\">, #<SNMP::VarBind:0x6206ddcc @name=[*******.4.1.**********.1.51.1], @value=\"\">, #<SNMP::VarBind:0x14c2b5e3 @name=[*******.4.1.**********.1.61.1], @value=\"Metric GUID=3AD66194B63DF31D77F27C961CA8AFF9\">, #<SNMP::VarBind:0x505525d6 @name=[*******.4.1.**********.1.62.1], @value=\"Severity GUID=2DEBE07D9828C5CCE063062AF90A93CF\">, #<SNMP::VarBind:0x26f37380 @name=[*******.4.1.**********.1.63.1], @value=\"Cycle GUID=2DEBE07D9828C5CCE063062AF90A93CF\">, #<SNMP::VarBind:0x724f222f @name=[*******.4.1.**********.1.64.1], @value=\"Collection Name=ME$Heartbeat_OMS\">, #<SNMP::VarBind:0x73a3ec28 @name=[*******.4.1.**********.1.65.1], @value=\"Metric Group=ME$Heartbeat_OMS\">, #<SNMP::VarBind:0xa211c1f @name=[*******.4.1.**********.1.66.1], @value=\"Metric=heartbeat_oms\">, #<SNMP::VarBind:0x37f301a8 @name=[*******.4.1.**********.1.67.1], @value=\"Metric Description=\">, #<SNMP::VarBind:0x3ca61323 @name=[*******.4.1.**********.1.68.1], @value=\"Metric value=1\">, #<SNMP::VarBind:0x50886a33 @name=[*******.4.1.**********.1.69.1], @value=\"Key Value= \">, #<SNMP::VarBind:0x524b45d9 @name=[*******.4.1.**********.1.70.1], @value=\"\">, #<SNMP::VarBind:0x7d9af079 @name=[*******.4.1.**********.1.71.1], @value=\"\">, #<SNMP::VarBind:0x7129cbe2 @name=[*******.4.1.**********.1.72.1], @value=\"\">, #<SNMP::VarBind:0x62e3fe06 @name=[*******.4.1.**********.1.73.1], @value=\"\">, #<SNMP::VarBind:0x6a8e0a0e @name=[*******.4.1.**********.1.74.1], @value=\"\">, #<SNMP::VarBind:0x403d361e @name=[*******.4.1.**********.1.75.1], @value=\"\">, #<SNMP::VarBind:0x7ff49320 @name=[*******.4.1.**********.1.76.1], @value=\"\">, #<SNMP::VarBind:0x21a5116 @name=[*******.4.1.**********.1.77.1], @value=\"\">, #<SNMP::VarBind:0x185ef521 @name=[*******.4.1.**********.1.78.1], @value=\"\">, #<SNMP::VarBind:0x317aadf2 @name=[*******.4.1.**********.1.79.1], @value=\"\">, #<SNMP::VarBind:0x6a4c556e @name=[*******.4.1.**********.1.80.1], @value=\"\">, #<SNMP::VarBind:0x3a0e2bd0 @name=[*******.4.1.**********.1.81.1], @value=\"\">, #<SNMP::VarBind:0x5596364d @name=[*******.4.1.**********.1.82.1], @value=\"\">, #<SNMP::VarBind:0x31670430 @name=[*******.4.1.**********.1.83.1], @value=\"\">, #<SNMP::VarBind:0x3988c057 @name=[*******.4.1.**********.1.84.1], @value=\"Number of keys=0\">, #<SNMP::VarBind:0x72133c1d @name=[*******.4.1.**********.1.85.1], @value=\"\">, #<SNMP::VarBind:0x1653584c @name=[*******.4.1.**********.1.52.1], @value=\"\">], @specific_trap=3, @source_ip=\"************\", @agent_addr=#<SNMP::IpAddress:0x1f1c8832 @value=\"\\n\\xFEHQ\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.**********.1.16.1": "", "SNMPv2-SMI::enterprises.**********.1.73.1": "", "SNMPv2-SMI::enterprises.**********.1.13.1": "<PERSON><PERSON>", "SNMPv2-SMI::enterprises.**********.1.22.1": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=iictyiaplv217.msnet.railb.be&targetType=host", "SNMPv2-SMI::enterprises.**********.1.76.1": "", "SNMPv2-SMI::enterprises.**********.1.80.1": "", "SNMPv2-SMI::enterprises.**********.1.81.1": "", "SNMPv2-SMI::enterprises.**********.1.35.1": "", "SNMPv2-SMI::enterprises.**********.1.9.1": "", "SNMPv2-SMI::enterprises.**********.1.45.1": "", "SNMPv2-SMI::enterprises.**********.1.50.1": "", "SNMPv2-SMI::enterprises.**********.1.6.1": "WARNING", "SNMPv2-SMI::enterprises.**********.1.47.1": "", "SNMPv2-SMI::enterprises.**********.1.63.1": "Cycle GUID=2DEBE07D9828C5CCE063062AF90A93CF", "SNMPv2-SMI::enterprises.**********.1.37.1": "", "SNMPv2-SMI::enterprises.**********.1.33.1": "", "SNMPv2-SMI::enterprises.**********.1.32.1": "", "SNMPv2-SMI::enterprises.**********.1.68.1": "Metric value=1", "SNMPv2-SMI::enterprises.**********.1.25.1": "SYSMAN", "SNMPv2-SMI::enterprises.**********.1.20.1": "", "event.kafka.timestamp": "2025-02-12T05:22:09.025Z", "event.kafka.offset": 66349, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1053-occ-events-prd", "event.kafka.topic": "a1053-occ-events-prd", "event.uuid": "0c9f541d-9d5e-4b0f-9cbe-0f5a96a5f90f", "event.logstash.instance_name": "iictmiapls016", "snmptrap.clearlevel_severity": "Warning", "snmptrap.monitored_object": "Key Value= ", "snmptrap.ci_type": "Host", "snmptrap.additional_info": "ME$Heartbeat_OMS:heartbeat_oms", "snmptrap.host": "iictyiaplv217.msnet.railb.be", "snmptrap.platform": "Operating System=Linux, Platform=x86_64,", "snmptrap.summary": "HEARTBEAT", "snmptrap.ci": "iictyiaplv217.msnet.railb.be", "snmptrap.event_id": "", "snmptrap.url": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2DEBE07D982CC5CCE063062AF90A93CF", "snmptrap.timestamp": "Feb 12, 2025 6:21:33 AM CET", "snmptrap.friendly_metric_type": "Metric=heartbeat_oms", "snmptrap.metric_type": ""}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "OCC_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "OCC Heartbeat Message", "clear_time": null, "severity": 1, "additional_data": {"platform": "ME$Heartbeat_OMS:heartbeat_oms / Operating System=Linux, Platform=x86_64,", "instance": "iictyiaplv217.msnet.railb.be", "version": [], "env": []}}]}}, {"input": {"SNMPv2-SMI::enterprises.**********.1.51.1": "", "SNMPv2-SMI::enterprises.**********.1.40.1": "Oracle Databases - Monitoring OPTIC,rule 121", "SNMPv2-SMI::enterprises.**********.1.83.1": "", "SNMPv2-SMI::enterprises.**********.1.61.1": "Metric GUID=F92BA0EDA96E05789424D7A83625BBE4", "SNMPv2-SMI::enterprises.**********.1.19.1": "None", "SNMPv2-SMI::enterprises.**********.1.43.1": "", "SNMPv2-SMI::enterprises.**********.1.44.1": "", "SNMPv2-SMI::enterprises.**********.1.2.1": "NOTIF_NORMAL", "SNMPv2-SMI::enterprises.**********.1.49.1": "", "SNMPv2-SMI::enterprises.**********.1.71.1": "", "SNMPv2-SMI::enterprises.**********.1.18.1": "New", "SNMPv2-SMI::enterprises.**********.1.38.1": "", "SNMPv2-SMI::enterprises.**********.1.31.1": "", "SNMPv2-SMI::enterprises.**********.1.8.1": "", "SNMPv2-SMI::enterprises.**********.1.74.1": "", "SNMPv2-SMI::enterprises.**********.1.65.1": "Metric Group=Response", "SNMPv2-SMI::enterprises.**********.1.84.1": "Number of keys=0", "SNMPv2-SMI::enterprises.**********.1.42.1": "2DED330F44C23CA3E063062AF90A4106", "SNMPv2-SMI::enterprises.**********.1.27.1": "*********.0", "host": "************", "SNMPv2-SMI::enterprises.**********.1.41.1": "EBL8501", "SNMPv2-SMI::enterprises.**********.1.52.1": "UNKNOWN", "type": "snmp_trap", "SNMPv2-SMI::enterprises.**********.1.85.1": "", "SNMPv2-SMI::enterprises.**********.1.79.1": "", "SNMPv2-SMI::enterprises.**********.1.17.1": "No", "@version": "1", "SNMPv2-SMI::enterprises.**********.1.77.1": "", "SNMPv2-SMI::enterprises.**********.1.36.1": "", "SNMPv2-SMI::enterprises.**********.1.46.1": "The pluggable database is in UNKNOWN state., Incident created by rule (Name = Incident management rule set for all targets, Create incident for critical metric alerts [System generated rule]).", "SNMPv2-SMI::enterprises.**********.1.64.1": "Collection Name=Response", "SNMPv2-SMI::enterprises.**********.1.30.1": "", "SNMPv2-SMI::enterprises.**********.1.72.1": "", "SNMPv2-SMI::enterprises.**********.1.67.1": "Metric Description=", "SNMPv2-SMI::enterprises.**********.1.75.1": "", "SNMPv2-SMI::enterprises.**********.1.29.1": "", "SNMPv2-SMI::enterprises.**********.1.78.1": "", "SNMPv2-SMI::enterprises.**********.1.34.1": "", "SNMPv2-SMI::enterprises.**********.1.7.1": "0", "SNMPv2-SMI::enterprises.**********.1.48.1": "", "SNMPv2-SMI::enterprises.**********.1.62.1": "Severity GUID=2DED330F44BE3CA3E063062AF90A4106", "SNMPv2-SMI::enterprises.**********.1.12.1": "Availability", "SNMPv2-SMI::enterprises.**********.1.82.1": "", "@timestamp": "2025-02-12T06:54:49.708362476Z", "SNMPv2-SMI::enterprises.**********.1.26.1": "", "SNMPv2-SMI::enterprises.**********.1.39.1": "Oracle Databases - Monitoring OPTIC", "SNMPv2-SMI::enterprises.**********.1.70.1": "", "message": "#<SNMP::SNMPv1_Trap:0x50da53e7 @enterprise=[*******.**********.2], @timestamp=#<SNMP::TimeTicks:0x19629421 @value=1469>, @varbind_list=[#<SNMP::VarBind:0x90554be @name=[*******.4.1.**********.1.2.1], @value=\"NOTIF_NORMAL\">, #<SNMP::VarBind:0xb427b12 @name=[*******.4.1.**********.1.3.1], @value=\"The pluggable database is in UNKNOWN state.\">, #<SNMP::VarBind:0x68c25c7 @name=[*******.4.1.**********.1.4.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2DED330F44C23CA3E063062AF90A4106\">, #<SNMP::VarBind:0x275a2b6b @name=[*******.4.1.**********.1.5.1], @value=\"Critical\">, #<SNMP::VarBind:0x4d92b9a @name=[*******.4.1.**********.1.6.1], @value=\"CRITICAL\">, #<SNMP::VarBind:0x1c5b5af8 @name=[*******.4.1.**********.1.7.1], @value=\"0\">, #<SNMP::VarBind:0x7b9ee6a9 @name=[*******.4.1.**********.1.8.1], @value=\"\">, #<SNMP::VarBind:0xd699bc2 @name=[*******.4.1.**********.1.9.1], @value=\"\">, #<SNMP::VarBind:0x4021f40e @name=[*******.4.1.**********.1.10.1], @value=\"Feb 12, 2025 7:54:35 AM CET\">, #<SNMP::VarBind:0x66bea68 @name=[*******.4.1.**********.1.11.1], @value=\"Availability\">, #<SNMP::VarBind:0xa4cffa2 @name=[*******.4.1.**********.1.12.1], @value=\"Availability\">, #<SNMP::VarBind:0x4b3ac2a2 @name=[*******.4.1.**********.1.13.1], @value=\"Metric Alert\">, #<SNMP::VarBind:0x4f4de4f @name=[*******.4.1.**********.1.14.1], @value=\"Response:State\">, #<SNMP::VarBind:0x5d155c13 @name=[*******.4.1.**********.1.15.1], @value=\"754191\">, #<SNMP::VarBind:0x30c909fc @name=[*******.4.1.**********.1.16.1], @value=\"\">, #<SNMP::VarBind:0x2dfbf295 @name=[*******.4.1.**********.1.17.1], @value=\"No\">, #<SNMP::VarBind:0xfdf57c3 @name=[*******.4.1.**********.1.18.1], @value=\"New\">, #<SNMP::VarBind:0x78dd67ef @name=[*******.4.1.**********.1.19.1], @value=\"None\">, #<SNMP::VarBind:0x2317e1 @name=[*******.4.1.**********.1.20.1], @value=\"0\">, #<SNMP::VarBind:0x72be322a @name=[*******.4.1.**********.1.21.1], @value=\"HOCD000P_MZ_HO054900P\">, #<SNMP::VarBind:0x72a12f1 @name=[*******.4.1.**********.1.22.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=HOCD000P_MZ_HO054900P&targetType=oracle_pdb\">, #<SNMP::VarBind:0x14dbffe4 @name=[*******.4.1.**********.1.23.1], @value=\"Pluggable Database\">, #<SNMP::VarBind:0xcdf6e81 @name=[*******.4.1.**********.1.24.1], @value=\"hdirziorlc031.msnet.railb.be\">, #<SNMP::VarBind:0xf732252 @name=[*******.4.1.**********.1.25.1], @value=\"VDW8003\">, #<SNMP::VarBind:0x581914d7 @name=[*******.4.1.**********.1.26.1], @value=\"\">, #<SNMP::VarBind:0x1376e4ef @name=[*******.4.1.**********.1.27.1], @value=\"*********.0\">, #<SNMP::VarBind:0x642a2b66 @name=[*******.4.1.**********.1.28.1], @value=\"Operating System=Linux, Platform=x86_64, Environment=PROD\">, #<SNMP::VarBind:0x725de403 @name=[*******.4.1.**********.1.29.1], @value=\"\">, #<SNMP::VarBind:0x6272e933 @name=[*******.4.1.**********.1.30.1], @value=\"\">, #<SNMP::VarBind:0x4687280e @name=[*******.4.1.**********.1.31.1], @value=\"\">, #<SNMP::VarBind:0x73435aa3 @name=[*******.4.1.**********.1.32.1], @value=\"\">, #<SNMP::VarBind:0x6e23d534 @name=[*******.4.1.**********.1.33.1], @value=\"\">, #<SNMP::VarBind:0x1bed92b2 @name=[*******.4.1.**********.1.34.1], @value=\"\">, #<SNMP::VarBind:0x64d51e99 @name=[*******.4.1.**********.1.35.1], @value=\"\">, #<SNMP::VarBind:0xd2ac6e2 @name=[*******.4.1.**********.1.36.1], @value=\"\">, #<SNMP::VarBind:0x7dd03806 @name=[*******.4.1.**********.1.37.1], @value=\"\">, #<SNMP::VarBind:0xa5f4d0c @name=[*******.4.1.**********.1.38.1], @value=\"\">, #<SNMP::VarBind:0x46b2ab08 @name=[*******.4.1.**********.1.39.1], @value=\"Oracle Databases - Monitoring OPTIC\">, #<SNMP::VarBind:0x1df468ea @name=[*******.4.1.**********.1.40.1], @value=\"Oracle Databases - Monitoring OPTIC,rule 121\">, #<SNMP::VarBind:0x6118735a @name=[*******.4.1.**********.1.41.1], @value=\"EBL8501\">, #<SNMP::VarBind:0x115f7558 @name=[*******.4.1.**********.1.42.1], @value=\"2DED330F44C23CA3E063062AF90A4106\">, #<SNMP::VarBind:0x42666690 @name=[*******.4.1.**********.1.43.1], @value=\"\">, #<SNMP::VarBind:0x7c6be50e @name=[*******.4.1.**********.1.44.1], @value=\"\">, #<SNMP::VarBind:0x5ecbdb7a @name=[*******.4.1.**********.1.45.1], @value=\"\">, #<SNMP::VarBind:0x3454804e @name=[*******.4.1.**********.1.46.1], @value=\"The pluggable database is in UNKNOWN state., Incident created by rule (Name = Incident management rule set for all targets, Create incident for critical metric alerts [System generated rule]).\">, #<SNMP::VarBind:0x457722b2 @name=[*******.4.1.**********.1.47.1], @value=\"\">, #<SNMP::VarBind:0x6697ec87 @name=[*******.4.1.**********.1.48.1], @value=\"\">, #<SNMP::VarBind:0x6607fb24 @name=[*******.4.1.**********.1.49.1], @value=\"\">, #<SNMP::VarBind:0x7641415a @name=[*******.4.1.**********.1.50.1], @value=\"\">, #<SNMP::VarBind:0xa5f8f92 @name=[*******.4.1.**********.1.51.1], @value=\"\">, #<SNMP::VarBind:0x5375b98a @name=[*******.4.1.**********.1.61.1], @value=\"Metric GUID=F92BA0EDA96E05789424D7A83625BBE4\">, #<SNMP::VarBind:0xa72b61b @name=[*******.4.1.**********.1.62.1], @value=\"Severity GUID=2DED330F44BE3CA3E063062AF90A4106\">, #<SNMP::VarBind:0x76c57d9e @name=[*******.4.1.**********.1.63.1], @value=\"Cycle GUID=2DED330F44BE3CA3E063062AF90A4106\">, #<SNMP::VarBind:0x904fcb @name=[*******.4.1.**********.1.64.1], @value=\"Collection Name=Response\">, #<SNMP::VarBind:0x7ed71fba @name=[*******.4.1.**********.1.65.1], @value=\"Metric Group=Response\">, #<SNMP::VarBind:0x610aac15 @name=[*******.4.1.**********.1.66.1], @value=\"Metric=State\">, #<SNMP::VarBind:0x3c7e1427 @name=[*******.4.1.**********.1.67.1], @value=\"Metric Description=\">, #<SNMP::VarBind:0x3e12fa74 @name=[*******.4.1.**********.1.68.1], @value=\"Metric value=UNKNOWN\">, #<SNMP::VarBind:0x787e18c3 @name=[*******.4.1.**********.1.69.1], @value=\"Key Value= \">, #<SNMP::VarBind:0x4ca079dd @name=[*******.4.1.**********.1.70.1], @value=\"\">, #<SNMP::VarBind:0x339342 @name=[*******.4.1.**********.1.71.1], @value=\"\">, #<SNMP::VarBind:0x295b186a @name=[*******.4.1.**********.1.72.1], @value=\"\">, #<SNMP::VarBind:0x216ba582 @name=[*******.4.1.**********.1.73.1], @value=\"\">, #<SNMP::VarBind:0x6eca4ee3 @name=[*******.4.1.**********.1.74.1], @value=\"\">, #<SNMP::VarBind:0x727f69fb @name=[*******.4.1.**********.1.75.1], @value=\"\">, #<SNMP::VarBind:0x316b796 @name=[*******.4.1.**********.1.76.1], @value=\"\">, #<SNMP::VarBind:0x7fad55c3 @name=[*******.4.1.**********.1.77.1], @value=\"\">, #<SNMP::VarBind:0x432cb183 @name=[*******.4.1.**********.1.78.1], @value=\"\">, #<SNMP::VarBind:0x4a9538ac @name=[*******.4.1.**********.1.79.1], @value=\"\">, #<SNMP::VarBind:0x7c098c6f @name=[*******.4.1.**********.1.80.1], @value=\"\">, #<SNMP::VarBind:0x691fdd60 @name=[*******.4.1.**********.1.81.1], @value=\"\">, #<SNMP::VarBind:0x4f99c969 @name=[*******.4.1.**********.1.82.1], @value=\"\">, #<SNMP::VarBind:0x2caad86e @name=[*******.4.1.**********.1.83.1], @value=\"\">, #<SNMP::VarBind:0x1c705360 @name=[*******.4.1.**********.1.84.1], @value=\"Number of keys=0\">, #<SNMP::VarBind:0xb937953 @name=[*******.4.1.**********.1.85.1], @value=\"\">, #<SNMP::VarBind:0xa402bc9 @name=[*******.4.1.**********.1.52.1], @value=\"UNKNOWN\">], @specific_trap=3, @source_ip=\"************\", @agent_addr=#<SNMP::IpAddress:0x70d3eb47 @value=\"\\n\\xFEHR\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.**********.1.16.1": "", "SNMPv2-SMI::enterprises.**********.1.73.1": "", "SNMPv2-SMI::enterprises.**********.1.13.1": "<PERSON><PERSON>", "SNMPv2-SMI::enterprises.**********.1.22.1": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=HOCD000P_MZ_HO054900P&targetType=oracle_pdb", "SNMPv2-SMI::enterprises.**********.1.76.1": "", "SNMPv2-SMI::enterprises.**********.1.80.1": "", "SNMPv2-SMI::enterprises.**********.1.81.1": "", "SNMPv2-SMI::enterprises.**********.1.35.1": "", "SNMPv2-SMI::enterprises.**********.1.9.1": "", "SNMPv2-SMI::enterprises.**********.1.45.1": "", "SNMPv2-SMI::enterprises.**********.1.50.1": "", "SNMPv2-SMI::enterprises.**********.1.6.1": "CRITICAL", "SNMPv2-SMI::enterprises.**********.1.47.1": "", "SNMPv2-SMI::enterprises.**********.1.63.1": "Cycle GUID=2DED330F44BE3CA3E063062AF90A4106", "SNMPv2-SMI::enterprises.**********.1.37.1": "", "SNMPv2-SMI::enterprises.**********.1.33.1": "", "SNMPv2-SMI::enterprises.**********.1.32.1": "", "SNMPv2-SMI::enterprises.**********.1.68.1": "Metric value=UNKNOWN", "SNMPv2-SMI::enterprises.**********.1.25.1": "VDW8003", "SNMPv2-SMI::enterprises.**********.1.20.1": "0", "event.kafka.timestamp": "2025-02-12T06:54:49.809Z", "event.kafka.offset": 66387, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1053-occ-events-prd", "event.kafka.topic": "a1053-occ-events-prd", "event.uuid": "8bf69d20-e736-4540-b07d-f4ef2593a3da", "event.logstash.instance_name": "iictmiapls016", "snmptrap.clearlevel_severity": "Critical", "snmptrap.monitored_object": "Key Value= ", "snmptrap.ci_type": "Pluggable Database", "snmptrap.additional_info": "Response:State", "snmptrap.host": "hdirziorlc031.msnet.railb.be", "snmptrap.platform": "Operating System=Linux, Platform=x86_64, Environment=PROD", "snmptrap.summary": "The pluggable database is in UNKNOWN state.", "snmptrap.ci": "HOCD000P_MZ_HO054900P", "snmptrap.event_id": "754191", "snmptrap.url": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2DED330F44C23CA3E063062AF90A4106", "snmptrap.timestamp": "Feb 12, 2025 7:54:35 AM CET", "snmptrap.friendly_metric_type": "Metric=State", "snmptrap.metric_type": "Availability"}, "output": {"s2110_alarm": [{"ci_id": "HOCD000P_MZ_HO054900P", "metric_name": "Response:State", "metric_type": "/PluggableDatabase/", "event_id": "754191", "actionable": true}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "HOCD000P_MZ_HO054900P", "metric_name": "Response:State", "metric_type": "/PluggableDatabase/", "summary": "The pluggable database is in UNKNOWN state.", "clear_time": null, "severity": 5}]}}, {"input": {"SNMPv2-SMI::enterprises.**********.1.51.1": "", "SNMPv2-SMI::enterprises.**********.1.40.1": "Oracle Databases - Monitoring OPTIC,rule 121", "SNMPv2-SMI::enterprises.**********.1.83.1": "", "SNMPv2-SMI::enterprises.**********.1.61.1": "Metric GUID=F92BA0EDA96E05789424D7A83625BBE4", "SNMPv2-SMI::enterprises.**********.1.19.1": "None", "SNMPv2-SMI::enterprises.**********.1.43.1": "", "SNMPv2-SMI::enterprises.**********.1.44.1": "", "SNMPv2-SMI::enterprises.**********.1.2.1": "NOTIF_NORMAL", "SNMPv2-SMI::enterprises.**********.1.49.1": "", "SNMPv2-SMI::enterprises.**********.1.71.1": "", "SNMPv2-SMI::enterprises.**********.1.18.1": "Closed", "event": {"kafka": {"timestamp": "2025-02-12T06:55:51.305Z", "offset": 66395, "key": null, "partition": 0, "consumer_group": "a1559-logstash-a1053-occ-events-prd", "topic": "a1053-occ-events-prd"}, "uuid": "79b22252-1fc1-4648-a2c8-b402d6cbc5fd", "logstash": {"instance_name": "iictmiapls016"}}, "SNMPv2-SMI::enterprises.**********.1.38.1": "", "SNMPv2-SMI::enterprises.**********.1.74.1": "", "SNMPv2-SMI::enterprises.**********.1.8.1": "", "SNMPv2-SMI::enterprises.**********.1.31.1": "", "SNMPv2-SMI::enterprises.**********.1.65.1": "Metric Group=Response", "SNMPv2-SMI::enterprises.**********.1.84.1": "Number of keys=0", "SNMPv2-SMI::enterprises.**********.1.42.1": "2DED330F44C23CA3E063062AF90A4106", "SNMPv2-SMI::enterprises.**********.1.27.1": "*********.0", "host": "************", "SNMPv2-SMI::enterprises.**********.1.41.1": "EBL8501", "type": "snmp_trap", "SNMPv2-SMI::enterprises.**********.1.52.1": "OPEN", "SNMPv2-SMI::enterprises.**********.1.85.1": "", "SNMPv2-SMI::enterprises.**********.1.79.1": "", "@version": "1", "SNMPv2-SMI::enterprises.**********.1.17.1": "No", "SNMPv2-SMI::enterprises.**********.1.36.1": "", "SNMPv2-SMI::enterprises.**********.1.77.1": "", "SNMPv2-SMI::enterprises.**********.1.46.1": "The pluggable database is in OPEN state.", "SNMPv2-SMI::enterprises.**********.1.64.1": "Collection Name=Response", "SNMPv2-SMI::enterprises.**********.1.30.1": "", "SNMPv2-SMI::enterprises.**********.1.72.1": "", "SNMPv2-SMI::enterprises.**********.1.67.1": "Metric Description=", "SNMPv2-SMI::enterprises.**********.1.75.1": "", "SNMPv2-SMI::enterprises.**********.1.29.1": "", "SNMPv2-SMI::enterprises.**********.1.7.1": "0", "SNMPv2-SMI::enterprises.**********.1.34.1": "", "SNMPv2-SMI::enterprises.**********.1.48.1": "", "SNMPv2-SMI::enterprises.**********.1.78.1": "", "SNMPv2-SMI::enterprises.**********.1.62.1": "Severity GUID=2DED6AC046416807E063062AF90A441F", "SNMPv2-SMI::enterprises.**********.1.12.1": "Availability", "snmptrap": {"clearlevel_severity": "Clear", "monitored_object": "Key Value= ", "ci_type": "Pluggable Database", "additional_info": "Response:State", "host": "hdirziorlc031.msnet.railb.be", "platform": "Operating System=Linux, Platform=x86_64, Environment=PROD", "summary": "The pluggable database is in OPEN state.", "ci": "HOCD000P_MZ_HO054900P", "event_id": "754191", "url": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2DED330F44C23CA3E063062AF90A4106", "timestamp": "Feb 12, 2025 7:55:29 AM CET", "friendly_metric_type": "Metric=State", "metric_type": "Availability"}, "SNMPv2-SMI::enterprises.**********.1.82.1": "", "@timestamp": "2025-02-12T06:55:51.204705365Z", "SNMPv2-SMI::enterprises.**********.1.26.1": "", "SNMPv2-SMI::enterprises.**********.1.39.1": "Oracle Databases - Monitoring OPTIC", "SNMPv2-SMI::enterprises.**********.1.73.1": "", "SNMPv2-SMI::enterprises.**********.1.16.1": "", "message": "#<SNMP::SNMPv1_Trap:0x60f22c34 @enterprise=[*******.**********.2], @timestamp=#<SNMP::TimeTicks:0x49d2e616 @value=7619>, @varbind_list=[#<SNMP::VarBind:0x592536c8 @name=[*******.4.1.**********.1.2.1], @value=\"NOTIF_NORMAL\">, #<SNMP::VarBind:0x28c73254 @name=[*******.4.1.**********.1.3.1], @value=\"The pluggable database is in OPEN state.\">, #<SNMP::VarBind:0x767b930c @name=[*******.4.1.**********.1.4.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2DED330F44C23CA3E063062AF90A4106\">, #<SNMP::VarBind:0x43849944 @name=[*******.4.1.**********.1.5.1], @value=\"Clear\">, #<SNMP::VarBind:0x33e7748a @name=[*******.4.1.**********.1.6.1], @value=\"CLEAR\">, #<SNMP::VarBind:0x2223a530 @name=[*******.4.1.**********.1.7.1], @value=\"0\">, #<SNMP::VarBind:0x8eab18b @name=[*******.4.1.**********.1.8.1], @value=\"\">, #<SNMP::VarBind:0x6fc3476b @name=[*******.4.1.**********.1.9.1], @value=\"\">, #<SNMP::VarBind:0x67082d3d @name=[*******.4.1.**********.1.10.1], @value=\"Feb 12, 2025 7:55:29 AM CET\">, #<SNMP::VarBind:0x583d1ec9 @name=[*******.4.1.**********.1.11.1], @value=\"Availability\">, #<SNMP::VarBind:0x5068aac @name=[*******.4.1.**********.1.12.1], @value=\"Availability\">, #<SNMP::VarBind:0x708b7d78 @name=[*******.4.1.**********.1.13.1], @value=\"Metric Alert\">, #<SNMP::VarBind:0x564cc791 @name=[*******.4.1.**********.1.14.1], @value=\"Response:State\">, #<SNMP::VarBind:0x66b750c7 @name=[*******.4.1.**********.1.15.1], @value=\"754191\">, #<SNMP::VarBind:0x5755e557 @name=[*******.4.1.**********.1.16.1], @value=\"\">, #<SNMP::VarBind:0x7c9e2278 @name=[*******.4.1.**********.1.17.1], @value=\"No\">, #<SNMP::VarBind:0x4cde1235 @name=[*******.4.1.**********.1.18.1], @value=\"Closed\">, #<SNMP::VarBind:0x10334201 @name=[*******.4.1.**********.1.19.1], @value=\"None\">, #<SNMP::VarBind:0x6834720d @name=[*******.4.1.**********.1.20.1], @value=\"0\">, #<SNMP::VarBind:0x65340091 @name=[*******.4.1.**********.1.21.1], @value=\"HOCD000P_MZ_HO054900P\">, #<SNMP::VarBind:0x59c7f9b8 @name=[*******.4.1.**********.1.22.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=HOCD000P_MZ_HO054900P&targetType=oracle_pdb\">, #<SNMP::VarBind:0x53ee8bcb @name=[*******.4.1.**********.1.23.1], @value=\"Pluggable Database\">, #<SNMP::VarBind:0x18012fa1 @name=[*******.4.1.**********.1.24.1], @value=\"hdirziorlc031.msnet.railb.be\">, #<SNMP::VarBind:0x5df484b8 @name=[*******.4.1.**********.1.25.1], @value=\"VDW8003\">, #<SNMP::VarBind:0x3b18bc87 @name=[*******.4.1.**********.1.26.1], @value=\"\">, #<SNMP::VarBind:0x1c0fc8fc @name=[*******.4.1.**********.1.27.1], @value=\"*********.0\">, #<SNMP::VarBind:0xde290fb @name=[*******.4.1.**********.1.28.1], @value=\"Operating System=Linux, Platform=x86_64, Environment=PROD\">, #<SNMP::VarBind:0x74ca14bb @name=[*******.4.1.**********.1.29.1], @value=\"\">, #<SNMP::VarBind:0xa486221 @name=[*******.4.1.**********.1.30.1], @value=\"\">, #<SNMP::VarBind:0x686203bc @name=[*******.4.1.**********.1.31.1], @value=\"\">, #<SNMP::VarBind:0x719e2303 @name=[*******.4.1.**********.1.32.1], @value=\"\">, #<SNMP::VarBind:0x55d59e47 @name=[*******.4.1.**********.1.33.1], @value=\"\">, #<SNMP::VarBind:0x1cc5b7b6 @name=[*******.4.1.**********.1.34.1], @value=\"\">, #<SNMP::VarBind:0x64c23871 @name=[*******.4.1.**********.1.35.1], @value=\"\">, #<SNMP::VarBind:0x644499d1 @name=[*******.4.1.**********.1.36.1], @value=\"\">, #<SNMP::VarBind:0x1d1a04b8 @name=[*******.4.1.**********.1.37.1], @value=\"\">, #<SNMP::VarBind:0x2cd368aa @name=[*******.4.1.**********.1.38.1], @value=\"\">, #<SNMP::VarBind:0x5927dcf1 @name=[*******.4.1.**********.1.39.1], @value=\"Oracle Databases - Monitoring OPTIC\">, #<SNMP::VarBind:0x196d5c15 @name=[*******.4.1.**********.1.40.1], @value=\"Oracle Databases - Monitoring OPTIC,rule 121\">, #<SNMP::VarBind:0x541fa444 @name=[*******.4.1.**********.1.41.1], @value=\"EBL8501\">, #<SNMP::VarBind:0x633e0055 @name=[*******.4.1.**********.1.42.1], @value=\"2DED330F44C23CA3E063062AF90A4106\">, #<SNMP::VarBind:0x4198897 @name=[*******.4.1.**********.1.43.1], @value=\"\">, #<SNMP::VarBind:0x77e7f1f5 @name=[*******.4.1.**********.1.44.1], @value=\"\">, #<SNMP::VarBind:0x5e7346c9 @name=[*******.4.1.**********.1.45.1], @value=\"\">, #<SNMP::VarBind:0x4d5cc9fa @name=[*******.4.1.**********.1.46.1], @value=\"The pluggable database is in OPEN state.\">, #<SNMP::VarBind:0x5c9bdf6 @name=[*******.4.1.**********.1.47.1], @value=\"\">, #<SNMP::VarBind:0x4de98ce3 @name=[*******.4.1.**********.1.48.1], @value=\"\">, #<SNMP::VarBind:0x9e8ce99 @name=[*******.4.1.**********.1.49.1], @value=\"\">, #<SNMP::VarBind:0x22d0c547 @name=[*******.4.1.**********.1.50.1], @value=\"\">, #<SNMP::VarBind:0x6abeeae6 @name=[*******.4.1.**********.1.51.1], @value=\"\">, #<SNMP::VarBind:0x6b31fdc6 @name=[*******.4.1.**********.1.61.1], @value=\"Metric GUID=F92BA0EDA96E05789424D7A83625BBE4\">, #<SNMP::VarBind:0x24e26a3f @name=[*******.4.1.**********.1.62.1], @value=\"Severity GUID=2DED6AC046416807E063062AF90A441F\">, #<SNMP::VarBind:0xbc6919 @name=[*******.4.1.**********.1.63.1], @value=\"Cycle GUID=2DED330F44BE3CA3E063062AF90A4106\">, #<SNMP::VarBind:0x1dc84a32 @name=[*******.4.1.**********.1.64.1], @value=\"Collection Name=Response\">, #<SNMP::VarBind:0x79f35b80 @name=[*******.4.1.**********.1.65.1], @value=\"Metric Group=Response\">, #<SNMP::VarBind:0x4e776513 @name=[*******.4.1.**********.1.66.1], @value=\"Metric=State\">, #<SNMP::VarBind:0x2de02965 @name=[*******.4.1.**********.1.67.1], @value=\"Metric Description=\">, #<SNMP::VarBind:0x72267f31 @name=[*******.4.1.**********.1.68.1], @value=\"Metric value=OPEN\">, #<SNMP::VarBind:0x116a15ae @name=[*******.4.1.**********.1.69.1], @value=\"Key Value= \">, #<SNMP::VarBind:0x6440a56d @name=[*******.4.1.**********.1.70.1], @value=\"\">, #<SNMP::VarBind:0x48471381 @name=[*******.4.1.**********.1.71.1], @value=\"\">, #<SNMP::VarBind:0x959324f @name=[*******.4.1.**********.1.72.1], @value=\"\">, #<SNMP::VarBind:0x485f81af @name=[*******.4.1.**********.1.73.1], @value=\"\">, #<SNMP::VarBind:0x29d52e04 @name=[*******.4.1.**********.1.74.1], @value=\"\">, #<SNMP::VarBind:0x597efba4 @name=[*******.4.1.**********.1.75.1], @value=\"\">, #<SNMP::VarBind:0x19f5618e @name=[*******.4.1.**********.1.76.1], @value=\"\">, #<SNMP::VarBind:0x2d13d9e6 @name=[*******.4.1.**********.1.77.1], @value=\"\">, #<SNMP::VarBind:0x2db6774e @name=[*******.4.1.**********.1.78.1], @value=\"\">, #<SNMP::VarBind:0x33b0a87 @name=[*******.4.1.**********.1.79.1], @value=\"\">, #<SNMP::VarBind:0x31f0e27f @name=[*******.4.1.**********.1.80.1], @value=\"\">, #<SNMP::VarBind:0x29fd14e @name=[*******.4.1.**********.1.81.1], @value=\"\">, #<SNMP::VarBind:0x1c0dca54 @name=[*******.4.1.**********.1.82.1], @value=\"\">, #<SNMP::VarBind:0x47b99460 @name=[*******.4.1.**********.1.83.1], @value=\"\">, #<SNMP::VarBind:0x71ec75f2 @name=[*******.4.1.**********.1.84.1], @value=\"Number of keys=0\">, #<SNMP::VarBind:0xd85cf20 @name=[*******.4.1.**********.1.85.1], @value=\"\">, #<SNMP::VarBind:0x7fa8fbae @name=[*******.4.1.**********.1.52.1], @value=\"OPEN\">], @specific_trap=3, @source_ip=\"************\", @agent_addr=#<SNMP::IpAddress:0x74b97aaf @value=\"\\n\\xFEHR\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.**********.1.70.1": "", "SNMPv2-SMI::enterprises.**********.1.13.1": "<PERSON><PERSON>", "SNMPv2-SMI::enterprises.**********.1.22.1": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=HOCD000P_MZ_HO054900P&targetType=oracle_pdb", "SNMPv2-SMI::enterprises.**********.1.76.1": "", "SNMPv2-SMI::enterprises.**********.1.80.1": "", "SNMPv2-SMI::enterprises.**********.1.81.1": "", "SNMPv2-SMI::enterprises.**********.1.35.1": "", "SNMPv2-SMI::enterprises.**********.1.45.1": "", "SNMPv2-SMI::enterprises.**********.1.9.1": "", "SNMPv2-SMI::enterprises.**********.1.50.1": "", "SNMPv2-SMI::enterprises.**********.1.6.1": "CLEAR", "SNMPv2-SMI::enterprises.**********.1.47.1": "", "SNMPv2-SMI::enterprises.**********.1.63.1": "Cycle GUID=2DED330F44BE3CA3E063062AF90A4106", "SNMPv2-SMI::enterprises.**********.1.37.1": "", "SNMPv2-SMI::enterprises.**********.1.33.1": "", "SNMPv2-SMI::enterprises.**********.1.32.1": "", "SNMPv2-SMI::enterprises.**********.1.68.1": "Metric value=OPEN", "SNMPv2-SMI::enterprises.**********.1.25.1": "VDW8003", "SNMPv2-SMI::enterprises.**********.1.20.1": "0"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "HOCD000P_MZ_HO054900P", "metric_name": "Response:State", "metric_type": "/PluggableDatabase/", "summary": "The pluggable database is in OPEN state.", "clear_time": "2025-02-12 06:55:29", "severity": 1}]}}, {"input": {"SNMPv2-SMI::enterprises.**********.1.51.1": "", "SNMPv2-SMI::enterprises.**********.1.40.1": "Oracle Databases - Monitoring OPTIC,rule 121", "SNMPv2-SMI::enterprises.**********.1.83.1": "", "SNMPv2-SMI::enterprises.**********.1.61.1": "Metric GUID=7A7112936F8C50204817DC97EC00725B", "SNMPv2-SMI::enterprises.**********.1.19.1": "", "SNMPv2-SMI::enterprises.**********.1.43.1": "", "SNMPv2-SMI::enterprises.**********.1.44.1": "", "SNMPv2-SMI::enterprises.**********.1.2.1": "NOTIF_NORMAL", "SNMPv2-SMI::enterprises.**********.1.49.1": "", "SNMPv2-SMI::enterprises.**********.1.71.1": "", "SNMPv2-SMI::enterprises.**********.1.18.1": "", "SNMPv2-SMI::enterprises.**********.1.38.1": "", "SNMPv2-SMI::enterprises.**********.1.31.1": "", "SNMPv2-SMI::enterprises.**********.1.8.1": "", "SNMPv2-SMI::enterprises.**********.1.74.1": "", "SNMPv2-SMI::enterprises.**********.1.65.1": "Metric Group=Database Job Status", "SNMPv2-SMI::enterprises.**********.1.84.1": "Number of keys=0", "SNMPv2-SMI::enterprises.**********.1.42.1": "2DE9B07E8B935620E063062AF90AE16F", "SNMPv2-SMI::enterprises.**********.1.27.1": "*********.0", "host": "************", "SNMPv2-SMI::enterprises.**********.1.41.1": "EBL8501", "SNMPv2-SMI::enterprises.**********.1.52.1": "", "type": "snmp_trap", "SNMPv2-SMI::enterprises.**********.1.85.1": "", "SNMPv2-SMI::enterprises.**********.1.79.1": "", "SNMPv2-SMI::enterprises.**********.1.17.1": "No", "@version": "1", "SNMPv2-SMI::enterprises.**********.1.77.1": "", "SNMPv2-SMI::enterprises.**********.1.36.1": "", "SNMPv2-SMI::enterprises.**********.1.46.1": "CLEARED - <PERSON><PERSON> for failed is cleared", "SNMPv2-SMI::enterprises.**********.1.64.1": "Collection Name=dbjob_status", "SNMPv2-SMI::enterprises.**********.1.30.1": "", "SNMPv2-SMI::enterprises.**********.1.72.1": "", "SNMPv2-SMI::enterprises.**********.1.67.1": "Metric Description=", "SNMPv2-SMI::enterprises.**********.1.75.1": "", "SNMPv2-SMI::enterprises.**********.1.29.1": "", "SNMPv2-SMI::enterprises.**********.1.78.1": "", "SNMPv2-SMI::enterprises.**********.1.34.1": "", "SNMPv2-SMI::enterprises.**********.1.7.1": "0", "SNMPv2-SMI::enterprises.**********.1.48.1": "", "SNMPv2-SMI::enterprises.**********.1.62.1": "Severity GUID=2DF33EBC4F1DC869E063062AF90A0FE6", "SNMPv2-SMI::enterprises.**********.1.12.1": "Error", "SNMPv2-SMI::enterprises.**********.1.82.1": "", "@timestamp": "2025-02-12T14:02:52.885763416Z", "SNMPv2-SMI::enterprises.**********.1.26.1": "", "SNMPv2-SMI::enterprises.**********.1.39.1": "Oracle Databases - Monitoring OPTIC", "SNMPv2-SMI::enterprises.**********.1.70.1": "", "message": "#<SNMP::SNMPv1_Trap:0x70f4ce75 @enterprise=[*******.**********.2], @timestamp=#<SNMP::TimeTicks:0x7ba32b @value=27387>, @varbind_list=[#<SNMP::VarBind:0x1de9d76e @name=[*******.4.1.**********.1.2.1], @value=\"NOTIF_NORMAL\">, #<SNMP::VarBind:0x4bb835c0 @name=[*******.4.1.**********.1.3.1], @value=\"CLEARED - Alert for failed is cleared\">, #<SNMP::VarBind:0x1687b567 @name=[*******.4.1.**********.1.4.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2DE9B07E8B935620E063062AF90AE16F\">, #<SNMP::VarBind:0xf43986b @name=[*******.4.1.**********.1.5.1], @value=\"Clear\">, #<SNMP::VarBind:0x5c426c4a @name=[*******.4.1.**********.1.6.1], @value=\"CLEAR\">, #<SNMP::VarBind:0x565e4437 @name=[*******.4.1.**********.1.7.1], @value=\"0\">, #<SNMP::VarBind:0x7dd9ef16 @name=[*******.4.1.**********.1.8.1], @value=\"\">, #<SNMP::VarBind:0x6e4ab006 @name=[*******.4.1.**********.1.9.1], @value=\"\">, #<SNMP::VarBind:0x2125b1b9 @name=[*******.4.1.**********.1.10.1], @value=\"Feb 12, 2025 3:02:19 PM CET\">, #<SNMP::VarBind:0x5fe2656 @name=[*******.4.1.**********.1.11.1], @value=\"Error\">, #<SNMP::VarBind:0x376dd8a0 @name=[*******.4.1.**********.1.12.1], @value=\"Error\">, #<SNMP::VarBind:0xc1c94cb @name=[*******.4.1.**********.1.13.1], @value=\"Metric Alert\">, #<SNMP::VarBind:0x385488 @name=[*******.4.1.**********.1.14.1], @value=\"dbjob_status:failed\">, #<SNMP::VarBind:0x74000e4f @name=[*******.4.1.**********.1.15.1], @value=\"\">, #<SNMP::VarBind:0x2df160b7 @name=[*******.4.1.**********.1.16.1], @value=\"\">, #<SNMP::VarBind:0x4523030e @name=[*******.4.1.**********.1.17.1], @value=\"No\">, #<SNMP::VarBind:0x77d9336 @name=[*******.4.1.**********.1.18.1], @value=\"\">, #<SNMP::VarBind:0x73fb97e0 @name=[*******.4.1.**********.1.19.1], @value=\"\">, #<SNMP::VarBind:0x5529a5f0 @name=[*******.4.1.**********.1.20.1], @value=\"\">, #<SNMP::VarBind:0x8cff728 @name=[*******.4.1.**********.1.21.1], @value=\"IOCD025P_MZ_IO010200P\">, #<SNMP::VarBind:0x63c03124 @name=[*******.4.1.**********.1.22.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=IOCD025P_MZ_IO010200P&targetType=oracle_pdb\">, #<SNMP::VarBind:0x4cabfe2b @name=[*******.4.1.**********.1.23.1], @value=\"Pluggable Database\">, #<SNMP::VarBind:0x5435c46b @name=[*******.4.1.**********.1.24.1], @value=\"idirziorlc261.msnet.railb.be\">, #<SNMP::VarBind:0x23340f72 @name=[*******.4.1.**********.1.25.1], @value=\"JJB8400\">, #<SNMP::VarBind:0x419f5321 @name=[*******.4.1.**********.1.26.1], @value=\"\">, #<SNMP::VarBind:0x5256b79f @name=[*******.4.1.**********.1.27.1], @value=\"*********.0\">, #<SNMP::VarBind:0x28ba3722 @name=[*******.4.1.**********.1.28.1], @value=\"Operating System=Linux, Platform=x86_64, Environment=PROD\">, #<SNMP::VarBind:0x2b76f2d8 @name=[*******.4.1.**********.1.29.1], @value=\"\">, #<SNMP::VarBind:0x104baacc @name=[*******.4.1.**********.1.30.1], @value=\"\">, #<SNMP::VarBind:0x77460d15 @name=[*******.4.1.**********.1.31.1], @value=\"\">, #<SNMP::VarBind:0xe3c37f8 @name=[*******.4.1.**********.1.32.1], @value=\"\">, #<SNMP::VarBind:0x1240f4d5 @name=[*******.4.1.**********.1.33.1], @value=\"\">, #<SNMP::VarBind:0x5f10219b @name=[*******.4.1.**********.1.34.1], @value=\"\">, #<SNMP::VarBind:0x18f9a1c9 @name=[*******.4.1.**********.1.35.1], @value=\"\">, #<SNMP::VarBind:0x7715c6d9 @name=[*******.4.1.**********.1.36.1], @value=\"\">, #<SNMP::VarBind:0x62e662b2 @name=[*******.4.1.**********.1.37.1], @value=\"\">, #<SNMP::VarBind:0x3c249b8c @name=[*******.4.1.**********.1.38.1], @value=\"\">, #<SNMP::VarBind:0x69868228 @name=[*******.4.1.**********.1.39.1], @value=\"Oracle Databases - Monitoring OPTIC\">, #<SNMP::VarBind:0x30fca2cf @name=[*******.4.1.**********.1.40.1], @value=\"Oracle Databases - Monitoring OPTIC,rule 121\">, #<SNMP::VarBind:0x61dea590 @name=[*******.4.1.**********.1.41.1], @value=\"EBL8501\">, #<SNMP::VarBind:0x793ebadc @name=[*******.4.1.**********.1.42.1], @value=\"2DE9B07E8B935620E063062AF90AE16F\">, #<SNMP::VarBind:0x24f4f011 @name=[*******.4.1.**********.1.43.1], @value=\"\">, #<SNMP::VarBind:0x71cbd49a @name=[*******.4.1.**********.1.44.1], @value=\"\">, #<SNMP::VarBind:0x652d0d16 @name=[*******.4.1.**********.1.45.1], @value=\"\">, #<SNMP::VarBind:0x69c9b335 @name=[*******.4.1.**********.1.46.1], @value=\"CLEARED - Alert for failed is cleared\">, #<SNMP::VarBind:0x6abeb265 @name=[*******.4.1.**********.1.47.1], @value=\"\">, #<SNMP::VarBind:0x45fed4ac @name=[*******.4.1.**********.1.48.1], @value=\"\">, #<SNMP::VarBind:0x48b624b8 @name=[*******.4.1.**********.1.49.1], @value=\"\">, #<SNMP::VarBind:0x6cc26680 @name=[*******.4.1.**********.1.50.1], @value=\"\">, #<SNMP::VarBind:0x7370dce7 @name=[*******.4.1.**********.1.51.1], @value=\"\">, #<SNMP::VarBind:0x40982d91 @name=[*******.4.1.**********.1.61.1], @value=\"Metric GUID=7A7112936F8C50204817DC97EC00725B\">, #<SNMP::VarBind:0x39f252de @name=[*******.4.1.**********.1.62.1], @value=\"Severity GUID=2DF33EBC4F1DC869E063062AF90A0FE6\">, #<SNMP::VarBind:0x467bd506 @name=[*******.4.1.**********.1.63.1], @value=\"Cycle GUID=2DE9B07E8B8F5620E063062AF90AE16F\">, #<SNMP::VarBind:0x3399beca @name=[*******.4.1.**********.1.64.1], @value=\"Collection Name=dbjob_status\">, #<SNMP::VarBind:0x32ecf98d @name=[*******.4.1.**********.1.65.1], @value=\"Metric Group=Database Job Status\">, #<SNMP::VarBind:0x19a339ac @name=[*******.4.1.**********.1.66.1], @value=\"Metric=Failed Job Count\">, #<SNMP::VarBind:0x1680c7b @name=[*******.4.1.**********.1.67.1], @value=\"Metric Description=\">, #<SNMP::VarBind:0x7f798d72 @name=[*******.4.1.**********.1.68.1], @value=\"Metric value=\">, #<SNMP::VarBind:0x2a0c3381 @name=[*******.4.1.**********.1.69.1], @value=\"Key Value= \">, #<SNMP::VarBind:0x2a620135 @name=[*******.4.1.**********.1.70.1], @value=\"\">, #<SNMP::VarBind:0x6b28dbd6 @name=[*******.4.1.**********.1.71.1], @value=\"\">, #<SNMP::VarBind:0x5889d9dc @name=[*******.4.1.**********.1.72.1], @value=\"\">, #<SNMP::VarBind:0x13526977 @name=[*******.4.1.**********.1.73.1], @value=\"\">, #<SNMP::VarBind:0x2903b981 @name=[*******.4.1.**********.1.74.1], @value=\"\">, #<SNMP::VarBind:0x4d8311c @name=[*******.4.1.**********.1.75.1], @value=\"\">, #<SNMP::VarBind:0x12095f62 @name=[*******.4.1.**********.1.76.1], @value=\"\">, #<SNMP::VarBind:0x12109585 @name=[*******.4.1.**********.1.77.1], @value=\"\">, #<SNMP::VarBind:0x266be9f7 @name=[*******.4.1.**********.1.78.1], @value=\"\">, #<SNMP::VarBind:0x637e7cf7 @name=[*******.4.1.**********.1.79.1], @value=\"\">, #<SNMP::VarBind:0x3bd4ddb5 @name=[*******.4.1.**********.1.80.1], @value=\"\">, #<SNMP::VarBind:0x2dfecbf7 @name=[*******.4.1.**********.1.81.1], @value=\"\">, #<SNMP::VarBind:0x5423bbee @name=[*******.4.1.**********.1.82.1], @value=\"\">, #<SNMP::VarBind:0x44aafc59 @name=[*******.4.1.**********.1.83.1], @value=\"\">, #<SNMP::VarBind:0x598ea80c @name=[*******.4.1.**********.1.84.1], @value=\"Number of keys=0\">, #<SNMP::VarBind:0x2f461b9 @name=[*******.4.1.**********.1.85.1], @value=\"\">, #<SNMP::VarBind:0x4bc156c2 @name=[*******.4.1.**********.1.52.1], @value=\"\">], @specific_trap=3, @source_ip=\"************\", @agent_addr=#<SNMP::IpAddress:0x581a33d7 @value=\"\\n\\xFEHR\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.**********.1.16.1": "", "SNMPv2-SMI::enterprises.**********.1.73.1": "", "SNMPv2-SMI::enterprises.**********.1.13.1": "<PERSON><PERSON>", "SNMPv2-SMI::enterprises.**********.1.22.1": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=IOCD025P_MZ_IO010200P&targetType=oracle_pdb", "SNMPv2-SMI::enterprises.**********.1.76.1": "", "SNMPv2-SMI::enterprises.**********.1.80.1": "", "SNMPv2-SMI::enterprises.**********.1.81.1": "", "SNMPv2-SMI::enterprises.**********.1.35.1": "", "SNMPv2-SMI::enterprises.**********.1.9.1": "", "SNMPv2-SMI::enterprises.**********.1.45.1": "", "SNMPv2-SMI::enterprises.**********.1.50.1": "", "SNMPv2-SMI::enterprises.**********.1.6.1": "CLEAR", "SNMPv2-SMI::enterprises.**********.1.47.1": "", "SNMPv2-SMI::enterprises.**********.1.63.1": "Cycle GUID=2DE9B07E8B8F5620E063062AF90AE16F", "SNMPv2-SMI::enterprises.**********.1.37.1": "", "SNMPv2-SMI::enterprises.**********.1.33.1": "", "SNMPv2-SMI::enterprises.**********.1.32.1": "", "SNMPv2-SMI::enterprises.**********.1.68.1": "Metric value=", "SNMPv2-SMI::enterprises.**********.1.25.1": "JJB8400", "SNMPv2-SMI::enterprises.**********.1.20.1": "", "event.kafka.timestamp": "2025-02-12T14:02:52.989Z", "event.kafka.offset": 66574, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1053-occ-events-prd", "event.kafka.topic": "a1053-occ-events-prd", "event.uuid": "9eb50554-cf10-442d-914b-afecf5a4c9a2", "event.logstash.instance_name": "iictmiapls016", "snmptrap.clearlevel_severity": "Clear", "snmptrap.monitored_object": "Key Value= ", "snmptrap.ci_type": "Pluggable Database", "snmptrap.additional_info": "dbjob_status:failed", "snmptrap.host": "idirziorlc261.msnet.railb.be", "snmptrap.platform": "Operating System=Linux, Platform=x86_64, Environment=PROD", "snmptrap.summary": "CLEARED - <PERSON><PERSON> for failed is cleared", "snmptrap.ci": "IOCD025P_MZ_IO010200P", "snmptrap.event_id": "", "snmptrap.url": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2DE9B07E8B935620E063062AF90AE16F", "snmptrap.timestamp": "Feb 12, 2025 3:02:19 PM CET", "snmptrap.friendly_metric_type": "Metric=Failed Job Count", "snmptrap.metric_type": "Error"}, "output": {}, "comment": "Record added for code coverage, there is no problem before this clear, so nothing expected in db."}, {"input": {"SNMPv2-SMI::enterprises.**********.1.51.1": "Neither Cause Nor Symptom", "SNMPv2-SMI::enterprises.**********.1.40.1": "MySQL,rule 809", "SNMPv2-SMI::enterprises.**********.1.83.1": "", "SNMPv2-SMI::enterprises.**********.1.61.1": "Availability status=Down", "SNMPv2-SMI::enterprises.**********.1.19.1": "None", "SNMPv2-SMI::enterprises.**********.1.43.1": "", "SNMPv2-SMI::enterprises.**********.1.44.1": "", "SNMPv2-SMI::enterprises.**********.1.2.1": "NOTIF_NORMAL", "SNMPv2-SMI::enterprises.**********.1.49.1": "", "SNMPv2-SMI::enterprises.**********.1.71.1": "", "SNMPv2-SMI::enterprises.**********.1.18.1": "New", "SNMPv2-SMI::enterprises.**********.1.38.1": "", "SNMPv2-SMI::enterprises.**********.1.31.1": "", "SNMPv2-SMI::enterprises.**********.1.8.1": "", "SNMPv2-SMI::enterprises.**********.1.74.1": "", "SNMPv2-SMI::enterprises.**********.1.65.1": "Response metric GUID=BE8CCF1905BBCFBBE7E66AF94E882299", "SNMPv2-SMI::enterprises.**********.1.84.1": "", "SNMPv2-SMI::enterprises.**********.1.42.1": "2E523073D803152DE063012AF90A9610", "SNMPv2-SMI::enterprises.**********.1.27.1": "10.11.6-MariaDB-log", "host": "************", "SNMPv2-SMI::enterprises.**********.1.41.1": "EBL8501", "SNMPv2-SMI::enterprises.**********.1.52.1": "DOWN", "type": "snmp_trap", "SNMPv2-SMI::enterprises.**********.1.85.1": "", "SNMPv2-SMI::enterprises.**********.1.79.1": "", "SNMPv2-SMI::enterprises.**********.1.17.1": "No", "@version": "1", "SNMPv2-SMI::enterprises.**********.1.77.1": "", "SNMPv2-SMI::enterprises.**********.1.36.1": "", "SNMPv2-SMI::enterprises.**********.1.46.1": "The MySQL target is down, Incident created by rule (Name = Incident management rule set for all targets, Incident creation rule for a Target Down availability status [System generated rule]).", "SNMPv2-SMI::enterprises.**********.1.64.1": "Transition Severity=25", "SNMPv2-SMI::enterprises.**********.1.30.1": "", "SNMPv2-SMI::enterprises.**********.1.72.1": "", "SNMPv2-SMI::enterprises.**********.1.67.1": "", "SNMPv2-SMI::enterprises.**********.1.75.1": "", "SNMPv2-SMI::enterprises.**********.1.29.1": "", "SNMPv2-SMI::enterprises.**********.1.78.1": "", "SNMPv2-SMI::enterprises.**********.1.34.1": "", "SNMPv2-SMI::enterprises.**********.1.7.1": "0", "SNMPv2-SMI::enterprises.**********.1.48.1": "", "SNMPv2-SMI::enterprises.**********.1.62.1": "Severity GUID=2E523073D800152DE063012AF90A9610", "SNMPv2-SMI::enterprises.**********.1.12.1": "Availability", "SNMPv2-SMI::enterprises.**********.1.82.1": "", "@timestamp": "2025-02-17T07:08:51.031787768Z", "SNMPv2-SMI::enterprises.**********.1.26.1": "", "SNMPv2-SMI::enterprises.**********.1.39.1": "MySQL", "SNMPv2-SMI::enterprises.**********.1.70.1": "", "message": "#<SNMP::SNMPv1_Trap:0x1177643 @enterprise=[*******.**********.2], @timestamp=#<SNMP::TimeTicks:0x1a671482 @value=3102>, @varbind_list=[#<SNMP::VarBind:0x7fcc5497 @name=[*******.4.1.**********.1.2.1], @value=\"NOTIF_NORMAL\">, #<SNMP::VarBind:0x45d39043 @name=[*******.4.1.**********.1.3.1], @value=\"The MySQL target is down\">, #<SNMP::VarBind:0x67324f7f @name=[*******.4.1.**********.1.4.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2E523073D803152DE063012AF90A9610\">, #<SNMP::VarBind:0xce14ce1 @name=[*******.4.1.**********.1.5.1], @value=\"Fatal\">, #<SNMP::VarBind:0x1194d906 @name=[*******.4.1.**********.1.6.1], @value=\"FATAL\">, #<SNMP::VarBind:0x489c12ff @name=[*******.4.1.**********.1.7.1], @value=\"0\">, #<SNMP::VarBind:0x3da0f524 @name=[*******.4.1.**********.1.8.1], @value=\"\">, #<SNMP::VarBind:0x3ba02035 @name=[*******.4.1.**********.1.9.1], @value=\"\">, #<SNMP::VarBind:0xccb92ae @name=[*******.4.1.**********.1.10.1], @value=\"Feb 17, 2025 8:08:18 AM CET\">, #<SNMP::VarBind:0x24e87222 @name=[*******.4.1.**********.1.11.1], @value=\"Availability\">, #<SNMP::VarBind:0x1e5baa4e @name=[*******.4.1.**********.1.12.1], @value=\"Availability\">, #<SNMP::VarBind:0x24c09038 @name=[*******.4.1.**********.1.13.1], @value=\"Target Availability\">, #<SNMP::VarBind:0x74ce38ec @name=[*******.4.1.**********.1.14.1], @value=\"Status\">, #<SNMP::VarBind:0x13d03d35 @name=[*******.4.1.**********.1.15.1], @value=\"754723\">, #<SNMP::VarBind:0x509afcdb @name=[*******.4.1.**********.1.16.1], @value=\"\">, #<SNMP::VarBind:0x707bf7a0 @name=[*******.4.1.**********.1.17.1], @value=\"No\">, #<SNMP::VarBind:0x75f7a81b @name=[*******.4.1.**********.1.18.1], @value=\"New\">, #<SNMP::VarBind:0x67dc1a05 @name=[*******.4.1.**********.1.19.1], @value=\"None\">, #<SNMP::VarBind:0x60a75e01 @name=[*******.4.1.**********.1.20.1], @value=\"0\">, #<SNMP::VarBind:0x4fce7342 @name=[*******.4.1.**********.1.21.1], @value=\"IM95130D\">, #<SNMP::VarBind:0x731bdd0 @name=[*******.4.1.**********.1.22.1], @value=\"https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=IM95130D&targetType=oracle_omys_database\">, #<SNMP::VarBind:0x3a8301 @name=[*******.4.1.**********.1.23.1], @value=\"MySQL Database\">, #<SNMP::VarBind:0x5ab78251 @name=[*******.4.1.**********.1.24.1], @value=\"iictziaplv217.msnet.railb.be\">, #<SNMP::VarBind:0x66dfae26 @name=[*******.4.1.**********.1.25.1], @value=\"VDW8003\">, #<SNMP::VarBind:0x6c8a5010 @name=[*******.4.1.**********.1.26.1], @value=\"\">, #<SNMP::VarBind:0x387cf40b @name=[*******.4.1.**********.1.27.1], @value=\"10.11.6-MariaDB-log\">, #<SNMP::VarBind:0x5ebf4c5f @name=[*******.4.1.**********.1.28.1], @value=\"Operating System=Linux, Platform=x86_64,\">, #<SNMP::VarBind:0x450a6730 @name=[*******.4.1.**********.1.29.1], @value=\"\">, #<SNMP::VarBind:0x7b3ea551 @name=[*******.4.1.**********.1.30.1], @value=\"\">, #<SNMP::VarBind:0x24bdca91 @name=[*******.4.1.**********.1.31.1], @value=\"\">, #<SNMP::VarBind:0x44b7ed @name=[*******.4.1.**********.1.32.1], @value=\"\">, #<SNMP::VarBind:0x16e17332 @name=[*******.4.1.**********.1.33.1], @value=\"\">, #<SNMP::VarBind:0x18fb4892 @name=[*******.4.1.**********.1.34.1], @value=\"\">, #<SNMP::VarBind:0x5258e05e @name=[*******.4.1.**********.1.35.1], @value=\"\">, #<SNMP::VarBind:0x7706ce27 @name=[*******.4.1.**********.1.36.1], @value=\"\">, #<SNMP::VarBind:0x6a634b16 @name=[*******.4.1.**********.1.37.1], @value=\"\">, #<SNMP::VarBind:0x281e3110 @name=[*******.4.1.**********.1.38.1], @value=\"\">, #<SNMP::VarBind:0x3dd17e5d @name=[*******.4.1.**********.1.39.1], @value=\"MySQL\">, #<SNMP::VarBind:0x7c67e836 @name=[*******.4.1.**********.1.40.1], @value=\"MySQL,rule 809\">, #<SNMP::VarBind:0xcac2757 @name=[*******.4.1.**********.1.41.1], @value=\"EBL8501\">, #<SNMP::VarBind:0x55e31163 @name=[*******.4.1.**********.1.42.1], @value=\"2E523073D803152DE063012AF90A9610\">, #<SNMP::VarBind:0x63f6ae14 @name=[*******.4.1.**********.1.43.1], @value=\"\">, #<SNMP::VarBind:0x2093dc04 @name=[*******.4.1.**********.1.44.1], @value=\"\">, #<SNMP::VarBind:0x4de8d1de @name=[*******.4.1.**********.1.45.1], @value=\"\">, #<SNMP::VarBind:0xcda909 @name=[*******.4.1.**********.1.46.1], @value=\"The MySQL target is down, Incident created by rule (Name = Incident management rule set for all targets, Incident creation rule for a Target Down availability status [System generated rule]).\">, #<SNMP::VarBind:0x569d310a @name=[*******.4.1.**********.1.47.1], @value=\"\">, #<SNMP::VarBind:0x6850b421 @name=[*******.4.1.**********.1.48.1], @value=\"\">, #<SNMP::VarBind:0x63bdeed4 @name=[*******.4.1.**********.1.49.1], @value=\"\">, #<SNMP::VarBind:0xed5864b @name=[*******.4.1.**********.1.50.1], @value=\"\">, #<SNMP::VarBind:0x317fe3fa @name=[*******.4.1.**********.1.51.1], @value=\"Neither Cause Nor Symptom\">, #<SNMP::VarBind:0x5ce3a848 @name=[*******.4.1.**********.1.61.1], @value=\"Availability status=Down\">, #<SNMP::VarBind:0x50a4264e @name=[*******.4.1.**********.1.62.1], @value=\"Severity GUID=2E523073D800152DE063012AF90A9610\">, #<SNMP::VarBind:0x726105cf @name=[*******.4.1.**********.1.63.1], @value=\"Availability Sub-status=99\">, #<SNMP::VarBind:0x3ccf484a @name=[*******.4.1.**********.1.64.1], @value=\"Transition Severity=25\">, #<SNMP::VarBind:0x7d2f0673 @name=[*******.4.1.**********.1.65.1], @value=\"Response metric GUID=BE8CCF1905BBCFBBE7E66AF94E882299\">, #<SNMP::VarBind:0xc48c9ce @name=[*******.4.1.**********.1.66.1], @value=\"Severity GUID of the first severity in the availability cycle=2CC53BB9697D83EBE063062AF90A982C\">, #<SNMP::VarBind:0x76fdeaf5 @name=[*******.4.1.**********.1.67.1], @value=\"\">, #<SNMP::VarBind:0x30367178 @name=[*******.4.1.**********.1.68.1], @value=\"\">, #<SNMP::VarBind:0x352ff593 @name=[*******.4.1.**********.1.69.1], @value=\"\">, #<SNMP::VarBind:0x7fe34c41 @name=[*******.4.1.**********.1.70.1], @value=\"\">, #<SNMP::VarBind:0x66d0bb0a @name=[*******.4.1.**********.1.71.1], @value=\"\">, #<SNMP::VarBind:0x65eebb19 @name=[*******.4.1.**********.1.72.1], @value=\"\">, #<SNMP::VarBind:0x2f27495a @name=[*******.4.1.**********.1.73.1], @value=\"\">, #<SNMP::VarBind:0x4a4399bb @name=[*******.4.1.**********.1.74.1], @value=\"\">, #<SNMP::VarBind:0x29a87312 @name=[*******.4.1.**********.1.75.1], @value=\"\">, #<SNMP::VarBind:0x390e234d @name=[*******.4.1.**********.1.76.1], @value=\"\">, #<SNMP::VarBind:0x2cf6d0af @name=[*******.4.1.**********.1.77.1], @value=\"\">, #<SNMP::VarBind:0x7a2e0acb @name=[*******.4.1.**********.1.78.1], @value=\"\">, #<SNMP::VarBind:0x10f4c67f @name=[*******.4.1.**********.1.79.1], @value=\"\">, #<SNMP::VarBind:0x58a89b67 @name=[*******.4.1.**********.1.80.1], @value=\"\">, #<SNMP::VarBind:0x42c14b75 @name=[*******.4.1.**********.1.81.1], @value=\"\">, #<SNMP::VarBind:0x483369b4 @name=[*******.4.1.**********.1.82.1], @value=\"\">, #<SNMP::VarBind:0x7e4289f3 @name=[*******.4.1.**********.1.83.1], @value=\"\">, #<SNMP::VarBind:0x622d56ff @name=[*******.4.1.**********.1.84.1], @value=\"\">, #<SNMP::VarBind:0x2aff232c @name=[*******.4.1.**********.1.85.1], @value=\"\">, #<SNMP::VarBind:0x79d2910e @name=[*******.4.1.**********.1.52.1], @value=\"DOWN\">], @specific_trap=3, @source_ip=\"************\", @agent_addr=#<SNMP::IpAddress:0x13b582d6 @value=\"\\n\\xFEHR\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.**********.1.16.1": "", "SNMPv2-SMI::enterprises.**********.1.73.1": "", "SNMPv2-SMI::enterprises.**********.1.13.1": "Target Availability", "SNMPv2-SMI::enterprises.**********.1.22.1": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=TARGET_HOMEPAGE&targetName=IM95130D&targetType=oracle_omys_database", "SNMPv2-SMI::enterprises.**********.1.76.1": "", "SNMPv2-SMI::enterprises.**********.1.80.1": "", "SNMPv2-SMI::enterprises.**********.1.81.1": "", "SNMPv2-SMI::enterprises.**********.1.35.1": "", "SNMPv2-SMI::enterprises.**********.1.9.1": "", "SNMPv2-SMI::enterprises.**********.1.45.1": "", "SNMPv2-SMI::enterprises.**********.1.50.1": "", "SNMPv2-SMI::enterprises.**********.1.6.1": "FATAL", "SNMPv2-SMI::enterprises.**********.1.47.1": "", "SNMPv2-SMI::enterprises.**********.1.63.1": "Availability Sub-status=99", "SNMPv2-SMI::enterprises.**********.1.37.1": "", "SNMPv2-SMI::enterprises.**********.1.33.1": "", "SNMPv2-SMI::enterprises.**********.1.32.1": "", "SNMPv2-SMI::enterprises.**********.1.68.1": "", "SNMPv2-SMI::enterprises.**********.1.25.1": "VDW8003", "SNMPv2-SMI::enterprises.**********.1.20.1": "0", "event.kafka.timestamp": "2025-02-17T07:08:51.132Z", "event.kafka.offset": 69290, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1053-occ-events-prd", "event.kafka.topic": "a1053-occ-events-prd", "event.uuid": "1377786c-d01f-4c69-95c7-ca14b2e8733d", "event.logstash.instance_name": "iictmiapls016", "snmptrap.clearlevel_severity": "Fatal", "snmptrap.monitored_object": "", "snmptrap.ci_type": "MySQL Database", "snmptrap.additional_info": "Status", "snmptrap.host": "iictziaplv217.msnet.railb.be", "snmptrap.platform": "Operating System=Linux, Platform=x86_64,", "snmptrap.summary": "The MySQL target is down", "snmptrap.ci": "IM95130D", "snmptrap.event_id": "754723", "snmptrap.url": "https://cloudcontrol13c.msnet.railb.be:443/em/redirect?pageType=sdk-core-event-console-detailEvent&issueID=2E523073D803152DE063012AF90A9610", "snmptrap.timestamp": "Feb 17, 2025 8:08:18 AM CET", "snmptrap.friendly_metric_type": "Severity GUID of the first severity in the availability cycle=2CC53BB9697D83EBE063062AF90A982C", "snmptrap.metric_type": "Availability"}, "output": {"s2110_alarm": [{"ci_id": "IM95130D", "metric_name": "Status", "metric_type": "*", "event_id": "754723", "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "IM95130D", "metric_name": "Status", "metric_type": "*", "summary": "Alarm not part of phase 1 or scope: The MySQL target is down", "clear_time": null, "severity": 5}]}}]}