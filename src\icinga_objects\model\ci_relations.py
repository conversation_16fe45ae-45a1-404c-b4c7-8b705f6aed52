"""Module to provide the UCMDB objects."""

import pandas as pd
from sqlalchemy.orm import Session

from icinga_objects.config import config
from icinga_objects.ucmdb_repository import UcmdbRepository
from olympus_common import enums, icinga
from olympus_common.utils import parallelize_process

client = icinga.IcingaClient()


class CIRelations:
    """Represent CIDependencies to manage Icinga Services."""

    def __init__(self, session: Session) -> None:
        self.icinga_object = enums.IcingaObjectType.DEPENDENCY
        self.ucmdb_repository = UcmdbRepository(session)

    def get_relations_with_floc_id(self) -> pd.DataFrame:
        """Get all types of CI  with all relations for each CI."""
        ci_relations = self.ucmdb_repository.get_ci_dependencies()
        df_source_destination = ci_relations[["source_floc_id", "destination_floc_id"]]
        name_series = df_source_destination.apply(self._dependency_name, axis=1)
        df_source_destination.insert(0, "object_type", self.icinga_object)
        df_source_destination.insert(1, "name", name_series)
        return df_source_destination

    def sync_with_icinga(self) -> None:
        """Add relations or dependencies to icinga."""
        df_all_relations = self.get_relations_with_floc_id()
        if not df_all_relations.empty:
            existing_dependencies = self._get_dependencies()
            df_all_relations = df_all_relations[~df_all_relations["name"].isin(existing_dependencies)]
            data = df_all_relations.to_dict(orient="records")
            parallelize_process(data, self._add_dependency, config.thread_number)

    @staticmethod
    def _dependency_name(row) -> str:
        """Define the dependency name."""
        return f"{row.destination_floc_id}!{row.source_floc_id}"

    @staticmethod
    def _add_dependency(object_type, name, source, child) -> None:
        """Add the dependency object to Icinga."""
        client.add(object_type=object_type, name=name, attrs={"parent_host_name": source, "child_host_name": child})

    @staticmethod
    def _get_dependencies() -> list:
        """Get all existing dependencies."""
        responses = client.get_all(object_type=enums.IcingaObjectType.DEPENDENCY)
        dependencies = [response["name"] for response in responses]
        return dependencies
