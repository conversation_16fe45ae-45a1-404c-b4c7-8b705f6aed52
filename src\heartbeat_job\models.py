"""Models and dataclasses used for heartbeats."""

from dataclasses import dataclass
from datetime import datetime
from typing import Protocol


class THasKeyAttributes(Protocol):
    """Protocol that represents an object that has the required attributes for key creation."""

    agent_id: int
    ci_id: str
    metric_name: str
    metric_type: str


@dataclass
class HeartbeatData:
    """Represent heartbeat information."""

    agent_id: int
    ci_id: str
    metric_name: str
    metric_type: str
    heartbeat_type: str
    last_time: datetime | None
    period_seconds: int
    idle_time_seconds: int
    period_threshold: datetime | None
    idle_time_threshold: datetime | None
    agent_name: str | None
    action_class: str | None
