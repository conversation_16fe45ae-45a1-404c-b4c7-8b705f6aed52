kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-sbom-tools-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/a2110_olympus/run.py
  DEBUG: "0"
  LOGS_FOLDER: "/data/logs"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  APPLICATION_NAME: "#{applicationName}#"
  ENVIRONMENT_NAME: "#{environmentName}#"
  RELEASE_NAME: "#{releaseName}#"
  EXTERNAL_SCOPE: "#{ucmdbScope}#"
  EXTERNAL_TOKEN_URL: "#{ucmdbTokenUrl}#"
  EXTERNAL_URL: "#{ucmdbEndpoint}#"
  EXTERNAL_TOKEN_EXPIRATION: "3600"
  OLYMPUS_AGENT_ID: "0"
