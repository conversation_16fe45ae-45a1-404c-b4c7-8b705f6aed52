"""DD module for mon-certificates."""

import logging

import pandas as pd
from sqlalchemy.orm import Session

from mon_certificates import static
from mon_certificates.config import config
from mon_certificates.enums import CertificateType, ExpirySeverities, TalosContacts
from mon_certificates.utils import alarming, common, nacadca, syslog, talos, ucmdb
from olympus_common import db
from olympus_common import pd as c_pd


def run(data: list[dict], session: Session) -> pd.DataFrame:
    """Run the core of the project.

    Parameters
    ----------
    data : list[dict]
        The list of the certificates from ElasticSearch.
    session : Session
        The session to the backend DB.

    Returns
    -------
    pd.DataFrame
        The list of the Alarms for the Olympus backend.
    """
    # Get checkpoint and write the new one
    run_number = common.run_number_checkpoint_process()

    # Fetch the certificates that need to be excluded from the monitoring
    df_exclusion = pd.read_excel(config.exclusion_path)
    if config.debug:
        c_pd.tabulate_and_to_excel(df_exclusion, config.logger_config.logs_folder, name="exclusion_certificates")

    df_alarms = pd.DataFrame()
    for cert_type in CertificateType:
        temp_df_alarms = dd(run_number, cert_type, data, df_exclusion, session)
        df_alarms = pd.concat([df_alarms, temp_df_alarms])

    df_alarms = df_alarms.replace({pd.NA: None})
    # Send a syslog heartbeat for each run if everything went well.
    syslog.heartbeat()

    return df_alarms


def dd(
    run_number: int,
    certificate_type: CertificateType,
    data: list[dict],
    df_exclusion: pd.DataFrame,
    session: Session,
) -> pd.DataFrame:
    """Run the main functionality of this dd (concerning nacadca)."""
    # Transform the data into a dataframe
    df = pd.json_normalize(data)
    df = df.fillna("N/A")

    if config.debug:
        c_pd.df_to_excel(df, config.logger_config.logs_folder, name=f"{certificate_type}_raw")

    # Rename columns and filter to keep the useful ones
    match certificate_type:
        case CertificateType.NACADCA.value:
            renaming_dict = static.NACADCA_COLUMNS_RENAMING_DICT
        case CertificateType.TALOS.value:
            renaming_dict = static.TALOS_COLUMNS_RENAMING_DICT
    df = c_pd.clean_raw_dataframe(df, renaming_dict)

    # Filter for the given issuer
    df = df[df["issuer"] == certificate_type.value]

    # Keep only the latest expiry date for each certificates
    df = df[df.groupby("common_name")["not_after"].transform("max") == df["not_after"]]
    df["expiry_days"] = df["expiry_days"].fillna(-1).astype(int)

    # Exclude certificates for which no monitoring is required
    df = df.merge(
        right=df_exclusion, how="outer", left_on="common_name", right_on="x509.subject.common_name", indicator=True
    )
    df = df.loc[df["_merge"] == "left_only"]

    if config.debug:
        c_pd.df_to_excel(df, config.logger_config.logs_folder, name=f"{certificate_type}_after_exclusion")

    # Check for duplicates
    df_duplicate_common_name = df.loc[df.duplicated(subset="common_name", keep=False), :]
    logging.info(
        f"Number of duplicates certificates for {certificate_type.value} = {df_duplicate_common_name.shape[0]}"
    )

    match certificate_type:
        case CertificateType.NACADCA.value:
            # Transform the contact columns to contains only email addresses
            df = nacadca.manage_contact(df)
            # Report organizational units with missing contact
            nacadca.reporting_for_missing_emails(df)
            # Create the group set for the for loop
            group_set = set(df["organizational_unit"].tolist())

        case CertificateType.TALOS.value:
            # Create the group set for the for loop
            group_set = set(df["application_code"].tolist())
            # Enrichment to add the contacts from uCMDB
            url = f"requestFor=Application&identifiers={';'.join(group_set).upper()}&detail=D&contacts=Yes"
            applications_details = ucmdb.get_data_from_ucmdb(url)
            for contact in TalosContacts:
                df[contact.value] = df.apply(
                    lambda row, contact=contact: talos.extract_email(
                        applications_details, row["application_code"], static.TALOS_CONTACT_COLUMNS[contact]
                    ),
                    axis=1,
                )

    # Create an empty dataframe to store all the data in order to create the alarms
    df_alarms = pd.DataFrame(columns=db.Alarm.__table__.columns.keys())

    # Get the agent ID
    agent_id = db.Agent.get_agent_id_from_name(static.AGENT_NAME, session)

    # Fetch the existing alarms from the backend DB
    existing_alerted_cis = alarming.get_existing_alerted_cis(session, agent_id)
    existing_cleared_cis = alarming.get_existing_cleared_cis(session, agent_id)

    for severity in ExpirySeverities:
        for group in group_set:
            # Define df_email_* to permit the email code deduplication
            df_email_2 = pd.DataFrame()
            df_email = pd.DataFrame()
            if severity == ExpirySeverities.CRITICAL.value and common.check_frequency(severity, run_number):
                df_email, recipients, cc_recipients = common.prepare_mailing(certificate_type, severity, group, df)
                cc_recipients.update(config.default_cc_receivers)
                subject = f"Critical - Certificates expiration in less than {static.CRITICAL_LIMIT} days"
                days_left = static.CRITICAL_LIMIT
            elif severity == ExpirySeverities.EMERGENCY.value and common.check_frequency(severity, run_number):
                df_email, recipients, cc_recipients = common.prepare_mailing(certificate_type, severity, group, df)
                cc_recipients.update(config.default_cc_receivers)
                cc_recipients.update(config.icc_receivers)
                subject = f"Emergency - Certificates expiration in less than {static.EMERGENCY_LIMIT} days"
                days_left = static.EMERGENCY_LIMIT

                temp_df_alarms = alarming.create_alarms(certificate_type, df_email, existing_alerted_cis, agent_id)
                df_alarms = pd.concat([df_alarms, temp_df_alarms])
            elif severity == ExpirySeverities.WARNING.value and common.check_frequency(severity, run_number):
                df_email, recipients, cc_recipients = common.prepare_mailing(certificate_type, severity, group, df)
                subject = f"Warning - Certificates expiration in less than {static.WARNING_LIMIT} days"
                days_left = static.WARNING_LIMIT

                if certificate_type == CertificateType.NACADCA.value:
                    df_email_2, recipients_2, cc_recipients_2 = common.prepare_mailing(
                        certificate_type, ExpirySeverities.INFO, group, df
                    )
                    recipients.update(recipients_2)
                    cc_recipients.update(cc_recipients_2)
                    if df_email_2.shape[0] > 0 and df_email.shape[0] > 0:
                        subject = f"warning - Certificates expiration in less than {static.INFORMATIONAL_LIMIT} days"
                    elif df_email_2.shape[0] > 0:
                        subject = f"info - Certificates expiration in less than {static.INFORMATIONAL_LIMIT} days"
                    days_left_2 = static.INFORMATIONAL_LIMIT
                cc_recipients.update(config.default_cc_receivers)

            if config.debug:
                recipients = config.notification_emails
                cc_recipients = config.notification_emails
            if not df_email_2.empty:
                common.email_for_certificates(
                    group_name=group,
                    cert_type=certificate_type,
                    recipients=recipients,
                    cc_recipients=cc_recipients,
                    df=df_email,
                    days_left=days_left,
                    subject=subject,
                    df_2=df_email_2,
                    days_left_2=days_left_2,
                )
            elif not df_email.empty:
                common.email_for_certificates(
                    group,
                    certificate_type,
                    recipients,
                    cc_recipients,
                    subject,
                    df_email,
                    days_left,
                )

    # Add the clears if they are into the dataframe with the alarms.
    df_alarms = pd.concat(
        [
            df_alarms,
            pd.DataFrame(
                alarming.create_clears(df_alarms, "ci_id", existing_alerted_cis, existing_cleared_cis, agent_id)
            ),
        ]
    )

    # Write the alarms into the DB or into an Excel file (if DEBUG = True)
    if config.debug:
        c_pd.df_to_excel(df, config.logger_config.logs_folder, name=f"{certificate_type}_alarms")

    return df_alarms
