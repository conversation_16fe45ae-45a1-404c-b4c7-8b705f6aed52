"""OID mapping for OCC."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for OCC."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "snmptrap.additional_info": "additional_info",
            "snmptrap.ci": "ci",
            "snmptrap.ci_type": "ci_type",
            "snmptrap.clearlevel_severity": "clearlevel_severity",
            "snmptrap.event_id": "event_id",
            "snmptrap.friendly_metric_type": "friendly_metric_type",
            "snmptrap.host": "host",
            "snmptrap.metric_type": "metric_type",
            "snmptrap.monitored_object": "monitored_object",
            "snmptrap.platform": "event_platform",
            "snmptrap.summary": "event_summary",
            "snmptrap.timestamp": "timestamp",
            "snmptrap.url": "url",
        }
