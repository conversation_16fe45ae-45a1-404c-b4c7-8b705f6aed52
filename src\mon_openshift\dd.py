"""Details Design module for mon-openshift."""

import logging
from datetime import datetime

import pandas as pd

from mon_openshift import utils
from olympus_common import enums
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Run the Openshift Details Design."""
    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    # Intermediate values
    df["identifier"] = df.apply(_identifier, axis=1)

    # Hardcoded values
    df["agent_id"] = agent_id
    df["metric_type"] = _metric_type()
    df["manager"] = _manager()
    df["action_class"] = _scope()
    df["handle_time"] = olympus_utils.now_naive()

    # Computed values
    df["actionable"] = df.apply(_actionable_alarm, axis=1)
    df["ci_id"] = df.apply(_ci_id, axis=1)
    df["clear_type"] = df.apply(_clear_type, axis=1)
    df["metric_name"] = df.apply(_metric_name, axis=1)
    df["node"] = df.apply(_node, axis=1)
    df["node_alias"] = df.apply(_node_alias, axis=1)
    df["raise_time"] = df.apply(_raise_time, axis=1)
    df["clear_time"] = df.apply(_clear_time, axis=1)
    df["severity"] = df.apply(_severity, axis=1)
    df["summary"] = df.apply(_summary, axis=1)
    df["event_type"] = df.apply(_clear_level, axis=1)
    df["top_level"] = df.apply(_top_level, axis=1)

    return df


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm(row: pd.Series) -> bool | None:
    """Return the actionable alarm status for the event."""
    if utils.is_heartbeat(row):
        # Is set as non-actionable in OPTIC but for our logic should be set as actionable.
        return True

    match _clear_level(row):
        case enums.AlarmType.RESOLUTION.value:
            return None  # enrichment needed
        case enums.AlarmType.PROBLEM.value:
            return None  # enrichment needed
        case _:
            # This shouldn't occur and it should be set to NON_ACTIONABLE to avoid being shown in the UI as an error
            logging.warning(f"Unexpected AlarmType received {_clear_level(row)}.")
            return False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _agent() -> str:
    """Return the agent for the DD."""
    return "OpenShift"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Return the ci_id related to the event."""
    return row["openshiftCluster2"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime | None:
    """Return the clear time of the event.

    Notes
    -----
    This is equal to 0 for non clear event.
    """
    if _clear_level(row) == enums.AlarmType.RESOLUTION.value:
        # Field Timestamp format is 2022-02-18T11:11:54.111Z
        return olympus_utils.parse_datetime(row["alertEnd"])
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type(row: pd.Series) -> str:
    """Return the clear type of the event."""
    return enums.ClearType.MANUALLY.value if utils.is_heartbeat(row) else enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_id(row: pd.Series) -> str:
    """Return the event ID of the event."""
    return row["alertId"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _identifier(row: pd.Series) -> str:
    """Return the identifier of the event."""
    return f"{_clear_level(row)} {_agent()} {_node(row)} {_event_id(row)}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the manager related to the DD."""
    return "mon-openshift"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type() -> str:
    """Return the metric used in this DD."""
    return "/OpenShift/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Return the metric name related to the event."""
    return row["alertName"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Return the node related to the event."""
    identifier: str = row["identifier"]
    return identifier.split(".", maxsplit=1)[0]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str:
    """Return the node alias related to the event."""
    return _node(row)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime:
    """Return the raise time of the event."""
    if _clear_level(row) == enums.AlarmType.PROBLEM.value:
        # Field Timestamp format is 2022-02-18T11:11:54.111Z
        return olympus_utils.parse_datetime(row["alertStart"])
    elif _clear_level(row) == enums.AlarmType.HEARTBEAT.value:
        return olympus_utils.parse_datetime(row["messageTimestamp"])
    else:
        return olympus_utils.parse_datetime(row["alertEnd"])


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the scope related to this EMS."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Return the severity related to the event.

    Notes
    -----
    This function will log as warning all non expected used values.
    """
    if _clear_level(row) == enums.AlarmType.RESOLUTION.value:
        return enums.Severity.INDETERMINATE.value

    match str(row["alertSeverity"]).upper():
        case "INFO":
            return enums.Severity.INDETERMINATE.value
        case "WARNING":
            return enums.Severity.WARNING.value
        case "ERROR":
            return enums.Severity.CRITICAL.value
        case _:
            logging.warning(f"Unexpected alertSeverity received {row['alertSeverity']}.")
            return enums.Severity.INDETERMINATE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Return the summary of the event."""
    return "OpenShift Heartbeat Message" if utils.is_heartbeat(row) else row["alertMessage"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level(row: pd.Series) -> str:
    """Return the application code related to the event."""
    return row["applicationCode"]


# TODO: The clear alarms received from OpenShift might not contain enough information to use the generic_clear.
# Therefore clear by eventId should be configured for Openshift alarms.
# This should be used to clear OpenShift alarms based on the unique alert id (1.3.6.1.4.1.44645.324.1.2.8),
# which is the common field between problem and clear alarms.@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_level(row: pd.Series) -> str:
    """Return the AlarmType related to the event.

    Notes
    -----
    This function will log as warning all non expected used values.
    """
    if utils.is_heartbeat(row):
        return enums.AlarmType.HEARTBEAT.value

    match str(row["alertStatus"]).lower():
        case "firing":
            return enums.AlarmType.PROBLEM.value
        case "resolved":
            return enums.AlarmType.RESOLUTION.value
        case _:
            # This shouldn't occur and it should be set to RESOLUTION to avoid being shown in the UI as an error.
            logging.warning(f"Unexpected alertStatus received {row['alertStatus']}.")
            return enums.AlarmType.RESOLUTION.value
