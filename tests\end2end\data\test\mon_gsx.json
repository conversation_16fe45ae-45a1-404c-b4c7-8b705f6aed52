{"extra_env": {"OLYMPUS_AGENT_ID": "8"}, "agent_ids": [8], "data": [{"input": {"@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x2912e4f2 @enterprise=[*******.4.1.9617], @timestamp=#<SNMP::TimeTicks:0x393dc51d @value=32785123>, @varbind_list=[#<SNMP::VarBind:0x4f269692 @name=[*******.4.1.9617.1.1.1.1.0], @value=\"10/02/2025 06:06:13 - System Performance: IICTCIAPWV638.msnet.railb.be (IICTCIAPWV638) System Performance detected (Reminder)\">, #<SNMP::VarBind:0x712dcef0 @name=[*******.4.1.9617.1.1.1.2.0], @value=\"IICTCIAPWV293\">, #<SNMP::VarBind:0x266fe8e5 @name=[*******.4.1.9617.1.1.1.3.0], @value=\"Windows Server\">, #<SNMP::VarBind:0xfc6c8b4 @name=[*******.4.1.9617.1.1.1.4.0], @value=\"System Performance\">, #<SNMP::VarBind:0x74af4dd8 @name=[*******.4.1.9617.1.1.1.5.0], @value=\"IICTCIAPWV638.msnet.railb.be\">, #<SNMP::VarBind:0x6b6a57f3 @name=[*******.4.1.9617.1.1.1.6.0], @value=\"\">, #<SNMP::VarBind:0x329ba2bb @name=[*******.4.1.9617.1.1.1.7.0], @value=\"Down\">, #<SNMP::VarBind:0xba14f34 @name=[*******.4.1.9617.1.1.1.8.0], @value=#<SNMP::Integer:0x56e1215 @value=0>>, #<SNMP::VarBind:0x3c021884 @name=[*******.4.1.9617.1.1.1.9.0], @value=\"638747643730000000\">, #<SNMP::VarBind:0x5363ebc5 @name=[*******.4.1.9617.1.1.1.10.0], @value=\"5d7887e3-8a70-4b4a-8c4a-8dafd7e4e4c7\">, #<SNMP::VarBind:0x523ac672 @name=[*******.4.1.9617.1.1.1.11.0], @value=#<SNMP::Integer:0x2731889a @value=7>>, #<SNMP::VarBind:0xbdb62c0 @name=[*******.4.1.9617.1.1.1.12.0], @value=#<SNMP::Integer:0x472a8dbd @value=1>>, #<SNMP::VarBind:0x43a7da7c @name=[*******.4.1.9617.1.********], @value=#<SNMP::Integer:0x68f9efca @value=0>>, #<SNMP::VarBind:0x38645d77 @name=[*******.4.1.9617.********.0], @value=#<SNMP::Integer:0x2b3068a3 @value=0>>, #<SNMP::VarBind:0x56b93383 @name=[*******.4.1.9617.********.0], @value=#<SNMP::Integer:0x71190121 @value=0>>, #<SNMP::VarBind:0x6b0c6590 @name=[*******.4.1.9617.********.0], @value=#<SNMP::Integer:0x43517d22 @value=0>>, #<SNMP::VarBind:0x5ceb6be0 @name=[*******.4.1.9617.********.0], @value=\"10/02/2025 06:06:13\">], @specific_trap=0, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x65427955 @value=\"\\n\\xFE@s\">, @generic_trap=6>", "host": "***********", "type": "snmp_trap", "@timestamp": "2025-02-10T05:06:37.815047555Z", "snmptrap.resource": "IICTCIAPWV638.msnet.railb.be", "snmptrap.SCOMSeverity": "0", "snmptrap.date": "638747643730000000", "snmptrap.alertType": "System Performance", "snmptrap.alertID": "5d7887e3-8a70-4b4a-8c4a-8dafd7e4e4c7", "snmptrap.StatisticsReset": "0", "snmptrap.Reminder": "1", "snmptrap.subResource": "", "snmptrap.status": "Down", "snmptrap.AlertDate": "10/02/2025 06:06:13", "snmptrap.EntityID": "7", "snmptrap.className": "Windows Server", "snmptrap.Test": "0", "snmptrap.trapMessage": "10/02/2025 06:06:13 - System Performance: IICTCIAPWV638.msnet.railb.be (IICTCIAPWV638) System Performance detected (Reminder)", "snmptrap.severity": "0", "snmptrap.gsxStationName": "IICTCIAPWV293", "snmptrap.Escalation": "0", "event.kafka.timestamp": "2025-02-10T05:06:37.915Z", "event.kafka.offset": 35643, "event.kafka.key": null, "event.kafka.topic": "a1635-exchange_2016-gsx_events-acc", "event.kafka.partition": 2, "event.kafka.consumer_group": "a1559-logstash-a1635-exchange_2016-gsx_events-acc", "event.logstash.instance_name": "iictniapls016", "event.uuid": "248ff4d4-f912-4ee9-8862-05df514d76fe"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "GSX_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "GSX_Monitor  Heartbeat Message", "clear_time": null, "severity": 1, "additional_data": {"trap_message": "10/02/2025 06:06:13 - System Performance: IICTCIAPWV638.msnet.railb.be (IICTCIAPWV638) System Performance detected (Reminder)"}}]}}, {"comment": "This is a test trap message, there is no problem that occurs for this, so no record in db is expected", "input": {"@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x6f4f236a @enterprise=[*******.4.1.9617], @timestamp=#<SNMP::TimeTicks:0x2dfd27a4 @value=44367762>, @varbind_list=[#<SNMP::VarBind:0x723a4624 @name=[*******.4.1.9617.1.1.1.1.0], @value=\"Test Trap Message - Test (SNMP Trap)\">, #<SNMP::VarBind:0x64f86f7a @name=[*******.4.1.9617.1.1.1.2.0], @value=\"IICTCIAPWV293\">, #<SNMP::VarBind:0x207cb446 @name=[*******.4.1.9617.1.1.1.3.0], @value=\"Domino\">, #<SNMP::VarBind:0x4d972834 @name=[*******.4.1.9617.1.1.1.4.0], @value=\"\">, #<SNMP::VarBind:0x6d3c5cc4 @name=[*******.4.1.9617.1.1.1.5.0], @value=\"TEST\">, #<SNMP::VarBind:0x4a986da6 @name=[*******.4.1.9617.1.1.1.6.0], @value=\"\">, #<SNMP::VarBind:0xf033e37 @name=[*******.4.1.9617.1.1.1.7.0], @value=\"n/a\">, #<SNMP::VarBind:0x7ba1616b @name=[*******.4.1.9617.1.1.1.8.0], @value=#<SNMP::Integer:0x74f4c8a7 @value=0>>, #<SNMP::VarBind:0x7d887ec2 @name=[*******.4.1.9617.1.1.1.9.0], @value=\"638748802000000000\">, #<SNMP::VarBind:0x6b6c05c2 @name=[*******.4.1.9617.1.1.1.10.0], @value=\"e5bc6e6c-9640-4936-bb73-261f16a86b84\">, #<SNMP::VarBind:0x1bb69bfd @name=[*******.4.1.9617.1.1.1.11.0], @value=#<SNMP::Integer:0x4955d9dc @value=0>>, #<SNMP::VarBind:0x7715c05a @name=[*******.4.1.9617.1.1.1.12.0], @value=#<SNMP::Integer:0x7c5c996f @value=0>>, #<SNMP::VarBind:0x539a8c6a @name=[*******.4.1.9617.1.********], @value=#<SNMP::Integer:0x34e634fc @value=0>>, #<SNMP::VarBind:0x6d28352a @name=[*******.4.1.9617.********.0], @value=#<SNMP::Integer:0x75275e01 @value=1>>, #<SNMP::VarBind:0x72d97513 @name=[*******.4.1.9617.********.0], @value=#<SNMP::Integer:0x779dee60 @value=0>>, #<SNMP::VarBind:0x5bb004dc @name=[*******.4.1.9617.********.0], @value=#<SNMP::Integer:0x14289efd @value=0>>, #<SNMP::VarBind:0x2de01086 @name=[*******.4.1.9617.********.0], @value=\"11/02/2025 14:16:40\">], @specific_trap=0, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x359615b4 @value=\"\\n\\xFE@s\">, @generic_trap=6>", "host": "***********", "type": "snmp_trap", "@timestamp": "2025-02-11T13:17:06.220997147Z", "snmptrap.resource": "TEST", "snmptrap.SCOMSeverity": "0", "snmptrap.date": "638748802000000000", "snmptrap.alertType": "", "snmptrap.alertID": "e5bc6e6c-9640-4936-bb73-261f16a86b84", "snmptrap.StatisticsReset": "0", "snmptrap.Reminder": "0", "snmptrap.subResource": "", "snmptrap.status": "n/a", "snmptrap.AlertDate": "11/02/2025 14:16:40", "snmptrap.EntityID": "0", "snmptrap.className": "Domino", "snmptrap.Test": "1", "snmptrap.trapMessage": "Test Trap Message - Test (SNMP Trap)", "snmptrap.severity": "0", "snmptrap.gsxStationName": "IICTCIAPWV293", "snmptrap.Escalation": "0", "event.kafka.timestamp": "2025-02-11T13:17:06.322Z", "event.kafka.offset": 35795, "event.kafka.key": null, "event.kafka.topic": "a1635-exchange_2016-gsx_events-acc", "event.kafka.partition": 2, "event.kafka.consumer_group": "a1559-logstash-a1635-exchange_2016-gsx_events-acc", "event.logstash.instance_name": "iictniapls016", "event.uuid": "8512594e-88b6-4471-a748-fb7bd6efe24f"}, "output": {}}]}