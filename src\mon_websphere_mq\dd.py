"""Details design implementation for mon-websphere-mq."""

from datetime import datetime, timezone

import pandas as pd

from mon_websphere_mq.utils import convert_to_severity
from olympus_common import enums
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def actionable_alarm(row: pd.Series) -> bool | None:
    """Return the actionable alarm value."""
    # check later on if the alarm is on info and contains heartbeat and wrongly formatted -> False
    if _is_heartbeat(row) or (
        severity(row) == enums.Severity.INDETERMINATE.value and event_type(row) != enums.AlarmType.RESOLUTION.value
    ):
        return False
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_type() -> str:
    """Return the clear type for the WebSphere MQ."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def action_class() -> str:
    """Return the action class for the WebSphere MQ."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def node(row: pd.Series) -> str:
    """Return the node of the event."""
    return row["hostname"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def node_alias(row: pd.Series) -> str:
    """Return the node alias of the event."""
    return node(row)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def event_type(row: pd.Series) -> str:
    """Return the event type of the event."""
    if _is_heartbeat(row):
        return enums.AlarmType.HEARTBEAT.value
    summary = row["summary"]
    if summary.upper() == "OK":
        return enums.AlarmType.RESOLUTION.value
    else:
        return enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def delay() -> int:
    """Return the delay in seconds for the event."""
    return 0


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def manager() -> str:
    """Return the manager."""
    return "mon-websphere-mq"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def ci_id(row: pd.Series) -> str:
    """Return the ci_id of the event."""
    return row["ci_id"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def metric_type(row: pd.Series) -> str:
    """Return the metric name of the event."""
    if _is_heartbeat(row):
        return "/ApplicationEvent/"
    ci = ci_id(row)
    if ci.startswith("MQ_"):
        return "/MQ_QueueAvailability/"
    elif ci.startswith("VERNEMQ_"):
        return "/VerneMQAvailibility/"
    elif ci.startswith("PROXY_"):
        return "/NginxAvailability/"
    else:
        return "/MQ_ManagerAvailability/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def metric_name(row: pd.Series) -> str:
    """Return the metric type of the event."""
    result = row["metric_name"]
    if result == "mqhb":
        result = "Heartbeat"
    return result


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def severity(row: pd.Series) -> int:
    """Return the severity of the event."""
    if _is_heartbeat(row):
        return enums.Severity.INDETERMINATE.value
    severity = row["syslogseverity"]
    return convert_to_severity(severity)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def wake_up_time(row: pd.Series) -> datetime:
    """Return the wake up time of the event."""
    return raise_time(row)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def raise_time(row: pd.Series) -> datetime:
    """Return the raise time of the event."""
    timestamp: str = row["event_timestamp"]
    timestamp_tz = datetime.strptime(timestamp, "%Y-%m-%dT%H:%M:%S.%f%z")
    return timestamp_tz.astimezone(timezone.utc).replace(tzinfo=None)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def summary(row: pd.Series) -> str:
    """Return the summary of the event."""
    if _is_heartbeat(row):
        return "Websphere MQ HeartBeat Alarm"
    return row["message"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _is_heartbeat(row: pd.Series) -> bool:
    return row["metric_name"] == "Heartbeat"


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Apply all transformations to the data and return it.

    The logic here is outlined in websphere-mq-dd.
    """
    return transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Apply all transformations to the data and return it."""
    # Harcoded values
    df["agent_id"] = agent_id
    df["manager"] = manager()
    df["action_class"] = action_class()
    df["clear_type"] = clear_type()
    df["delay"] = delay()
    df["handle_time"] = olympus_utils.now_naive()

    # Applied values
    df["metric_name"] = df.apply(metric_name, axis=1)  # we start with metricname because it identifies heartbeats
    df["node"] = df.apply(node, axis=1)
    df["node_alias"] = df.apply(node_alias, axis=1)
    df["summary"] = df.apply(summary, axis=1)
    df["severity"] = df.apply(severity, axis=1)
    df["event_type"] = df.apply(event_type, axis=1)  # event_type is dependant on summary
    df["raise_time"] = df.apply(raise_time, axis=1)
    df["wake_up_time"] = df.apply(wake_up_time, axis=1)
    df["actionable_alarm"] = df.apply(actionable_alarm, axis=1)
    df["ci_id"] = df.apply(ci_id, axis=1)
    df["metric_type"] = df.apply(metric_type, axis=1)

    return df
