"""Utilities module for the optic_matcher."""

from datetime import timedelta

import pandas as pd

from olympus_common.db import Alarm
from optic_matcher import patterns
from optic_matcher.db import OpticAlarm


def optic_pre_processing(alarm: OpticAlarm) -> OpticAlarm:
    """Pre-process an Optic alarm before the matching."""
    if alarm.agent == "CA_Spectrum":
        alarm.ci_id = _ca_spectrum_remove_trailing_char(alarm.ci_id)

    elif alarm.agent == "StruxureWare":
        if alarm.ci_id:
            alarm.ci_id = alarm.ci_id.upper()

    elif alarm.agent == "Zabbix":
        if alarm.monitoredelementname:
            alarm.monitoredelementname = alarm.monitoredelementname.upper()
        if alarm.monitoredelementname is None and alarm.metrics is None:
            alarm.monitoredelementname = "NA"
            alarm.metrics = "NA"
    return alarm


def olympus_pre_processing(olympus_alarm: Alarm) -> dict:
    """Pre-process an Olympus alarm before the matching."""
    alarm = olympus_alarm.asdict()

    if alarm["agent"] and alarm["agent"]["name"] == "BigData":
        if alarm["first_raise_time"]:
            alarm["first_raise_time"] = alarm["first_raise_time"].replace(microsecond=0)

    elif alarm["agent"] and alarm["agent"]["name"] == "C-NMS":
        if alarm["first_raise_time"]:
            alarm["first_raise_time"] = alarm["first_raise_time"].replace(microsecond=0)

    elif alarm["agent"] and alarm["agent"]["name"] == "GSX_Monitor":
        if alarm["first_raise_time"]:
            alarm["first_raise_time"] = alarm["first_raise_time"].replace(microsecond=0)

    elif alarm["agent"] and alarm["agent"]["name"] == "PEM":
        if alarm["first_raise_time"]:
            alarm["first_raise_time"] = alarm["first_raise_time"].replace(microsecond=0) + timedelta(hours=1)

    elif alarm["agent"] and alarm["agent"]["name"] == "Stonebranch":
        if alarm["first_raise_time"]:
            alarm["first_raise_time"] = alarm["first_raise_time"].replace(microsecond=0)

    elif alarm["agent"] and alarm["agent"]["name"] == "StruxureWare":
        if alarm["metric_name"] == "COMMUNICATION" and alarm["first_raise_time"]:
            # Communication loss alarms are sent with a delay of 10 minutes to Optic and 1 minute to Olympus.
            alarm["first_raise_time"] = alarm["first_raise_time"] + timedelta(minutes=9)

    elif alarm["agent"] and alarm["agent"]["name"] == "Zabbix":
        if alarm["metric_name"]:
            alarm["metric_name"] = alarm["metric_name"].upper()
        if alarm["metric_type"] in ("n/a", "NA", "N/A"):
            alarm["metric_type"] = "NA"
        if alarm["metric_name"] in ("n/a", "NA", "N/A"):
            alarm["metric_name"] = "NA"

    return alarm


def loose_matching(joint_df: pd.DataFrame) -> pd.DataFrame:
    """Perform a loose matching on the yet unmatched rows of the dataframe."""
    agent_list = ["C-NMS", "GSX_Monitor"]

    loosely_matched_df = pd.DataFrame()

    for agent in agent_list:
        # Selecting the unmatched rows corresponding to the agent.
        olympus_alarms_df = joint_df[(joint_df["agent_name"] == agent) & (joint_df["exists"] == "right_only")]
        optic_alarms_df = joint_df[(joint_df["agent"] == agent) & (joint_df["exists"] == "left_only")]

        olympus_alarms_df = olympus_alarms_df.sort_values(by=["raise_time"])

        for _, olympus_alarm in olympus_alarms_df.iterrows():
            if optic_id := _search_for_optic_match(olympus_alarm, optic_alarms_df):
                # Mark the Optic alarm as matched.
                optic_alarms_df.loc[optic_alarms_df["id"] == optic_id, "alarm_id"] = olympus_alarm["alarm_id"]

                olympus_alarm["id"] = optic_id
                temp_df = pd.DataFrame([olympus_alarm])
                loosely_matched_df = pd.concat([loosely_matched_df, temp_df], axis=0)

    return loosely_matched_df


def _ca_spectrum_remove_trailing_char(ci_id: str | None) -> str | None:
    """Remove the trailing character of a CA-Spectrum CI ID."""
    if not ci_id:
        return None
    if patterns.PATTERN_CA_SPECTRUM_ENDS_WITH_V_M.search(ci_id):
        return ci_id.removesuffix("v-m")
    elif patterns.PATTERN_CA_SPECTRUM_ENDS_WITH_IOV.search(ci_id):
        return ci_id[:-1]
    else:
        return ci_id


def _search_for_optic_match(olympus_alarm: pd.Series, optic_alarms_df: pd.DataFrame) -> int | None:
    """Search for an Optic alarm matching the Olympus alarm."""
    matching_optic_alarms: pd.DataFrame = optic_alarms_df[
        (optic_alarms_df["ci_id"] == olympus_alarm["ci_id"])
        & (optic_alarms_df["monitoredelementname"] == olympus_alarm["metric_name"])
        & (optic_alarms_df["metrics"] == olympus_alarm["metric_type"])
        & (optic_alarms_df["alarm_id"].isnull())
    ]

    if matching_optic_alarms.empty:
        return None

    matching_optic_alarms = matching_optic_alarms.sort_values(by=["raisetime"])

    for _, optic_alarm in matching_optic_alarms.iterrows():
        datediff: timedelta = olympus_alarm["raise_time"] - optic_alarm["raisetime"]
        if datediff.days <= 1:
            # A match has been found. Add it to the output DF.
            return optic_alarm["id"]

    return None
