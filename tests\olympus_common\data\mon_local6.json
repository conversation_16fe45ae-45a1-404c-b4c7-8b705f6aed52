{"data": [{"input": {"severity": "err", "syslog_timestamp": "Jul  5 07:14:14", "tag": "A1617-ACC", "environment": "ACC", "@timestamp": "2024-07-05T05:14:14.000Z", "@version": "1", "hostname": "SYSLOGMONGW", "metric": "MissingTimeStamp", "channel": "OPTIC_syslog-gw", "message": " A1617-alert: Missing Timestamp. Format is 'Channel CI-ID ENV Metric Timestamp : summary'  --- original message: TIME=Jul  5 07:14:14 FROM=iictyiaplv167 FACILITY=local6 SEVERITY=info MSG= [ID 15423 local6.info] HA HA_A1183_TPS_BUILD3 DEV a1183_BUILD3_TPS_GLOBAL_AHS_STATUS.Switch 1970/01/01 01:00:00 (2024/07/05 07:14:14) : 801363 : This action has state INIT", "ci_id": "SYSLOG_GATEWAY_ACC", "agent.id": "1d3fac61-44e2-49df-aeef-5f45132ee9c0", "agent.type": "filebeat", "agent.version": "7.5.2", "agent.ephemeral_id": "f65ec884-fc68-4492-9242-d7fbd7648d54", "agent.hostname": "mon-xa01-lp", "event.uuid": "4cb7c1ff-c07a-442c-ae11-656dc22a11d8", "event.created": "2024-07-05T05:14:19.553Z", "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.timestamp": "2024-07-05T05:14:19.553Z", "event.kafka.key": null, "event.kafka.offset": 47234389, "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "host.name": "mon-xa01-lp", "log.original": "Jul  5 07:14:14 SYSLOGMONGW A1617-ACC: [ID - local6.err] OPTIC_syslog-gw SYSLOG_GATEWAY_ACC ACC MissingTimeStamp 2024/07/05 07:14:14 :  A1617-alert: Missing Timestamp. Format is 'Channel CI-ID ENV Metric Timestamp : summary'  --- original message: TIME=Jul  5 07:14:14 FROM=iictyiaplv167 FACILITY=local6 SEVERITY=info MSG= [ID 15423 local6.info] HA HA_A1183_TPS_BUILD3 DEV a1183_BUILD3_TPS_GLOBAL_AHS_STATUS.Switch 1970/01/01 01:00:00 (2024/07/05 07:14:14) : 801363 : This action has state INIT", "log.offset": 69400167, "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log", "input.type": "log", "ecs.version": "1.1.0"}, "output": {"ci_id": "SYSLOG_GATEWAY_ACC", "manager": "mon-local6", "handle_time": "2024-01-01 12:00:01", "actionable": null, "event_type": "problem", "clear_type": "automatic", "additional_data": null, "metric_name": "MissingTimeStamp", "metric_type": "/SysLogEvent/", "node": "SYSLOGMONGW", "node_alias": "SYSLOGMONGW", "platform": "ACC", "raise_time": "2024-07-05 05:14:14", "clear_time": null, "wake_up_time": "2024-07-05 05:14:14", "action_class": "IT", "severity": 4, "summary": " A1617-alert: Missing Timestamp. Format is 'Channel CI-ID ENV Metric Timestamp : summary'  --- original message: TIME=Jul  5 07:14:14 FROM=iictyiaplv167 FACILITY=local6 SEVERITY=info MSG= [ID 15423 local6.info] HA HA_A1183_TPS_BUILD3 DEV a1183_BUILD3_TPS_GLOBAL_AHS_STATUS.Switch 1970/01/01 01:00:00 (2024/07/05 07:14:14) : 801363 : This action has state INIT", "top_level": null}}, {"input": {"severity": "err", "syslog_timestamp": "Jul  5 07:14:53", "tag": "A1617-ACC-Mailgw", "environment": "ACC", "@timestamp": "2024-07-05T05:14:53.000Z", "@version": "1", "hostname": "mail-monitoring-gw", "metric": "heartbeat", "channel": "OPTIC_python", "message": "heart is beating : email gateway OK - Checked 4853 times - running for 6 days, 19 hours, 6 minutes, 10 seconds", "ci_id": "mailgw_hb", "agent.id": "1d3fac61-44e2-49df-aeef-5f45132ee9c0", "agent.type": "filebeat", "agent.version": "7.5.2", "agent.ephemeral_id": "f65ec884-fc68-4492-9242-d7fbd7648d54", "agent.hostname": "mon-xa01-lp", "event.uuid": "15715c57-40d7-44ca-98c3-1614a918b3f8", "event.created": "2024-07-05T05:14:59.589Z", "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.timestamp": "2024-07-05T05:14:59.589Z", "event.kafka.key": null, "event.kafka.offset": 47234392, "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "host.name": "mon-xa01-lp", "log.original": "Jul  5 07:14:53 mail-monitoring-gw A1617-ACC-Mailgw: [ID - local6.err] OPTIC_python mailgw_hb ACC heartbeat 2024/07/05 07:14:53 : heart is beating : email gateway OK - Checked 4853 times - running for 6 days, 19 hours, 6 minutes, 10 seconds", "log.offset": 69403626, "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log", "input.type": "log", "ecs.version": "1.1.0"}, "output": {"ci_id": "a1617-mail_gw-prod", "manager": "mon-local6", "handle_time": "2024-01-01 12:00:01", "actionable": false, "event_type": "heartbeat", "clear_type": "automatic", "additional_data": null, "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "node": "mail-monitoring-gw", "node_alias": "mail-monitoring-gw", "platform": "ACC", "raise_time": "2024-07-05 05:14:53", "clear_time": null, "wake_up_time": "2024-07-05 05:14:53", "action_class": "IT", "severity": 1, "summary": "HeartBeat Alarm", "top_level": null}}, {"input": {"severity": "err", "syslog_timestamp": "Jul  5 07:14:55", "tag": "A1617-ACC-SyslogHB", "environment": "PROD", "@timestamp": "2024-07-05T05:14:55.000Z", "@version": "1", "hostname": "mon-xa01-lp.msnet.railb.be", "metric": "Heartbeat", "channel": "OPTIC_python", "message": "Heart is beating for Syslog GW PROD_DRP", "ci_id": "SYSLOG_GW_PRODMON_DRP", "agent.id": "1d3fac61-44e2-49df-aeef-5f45132ee9c0", "agent.type": "filebeat", "agent.version": "7.5.2", "agent.ephemeral_id": "f65ec884-fc68-4492-9242-d7fbd7648d54", "agent.hostname": "mon-xa01-lp", "event.uuid": "78c8c9cb-d745-43dd-b61f-abf026a5d6ac", "event.created": "2024-07-05T05:14:59.589Z", "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.timestamp": "2024-07-05T05:14:59.589Z", "event.kafka.key": null, "event.kafka.offset": 47231755, "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "host.name": "mon-xa01-lp", "log.original": "Jul  5 07:14:55 mon-xa01-lp.msnet.railb.be A1617-ACC-SyslogHB: [ID - local6.err] OPTIC_python SYSLOG_GW_PRODMON_DRP PROD Heartbeat 2024/07/05 07:14:55 : Heart is beating for Syslog GW PROD_DRP", "log.offset": 69404023, "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log", "input.type": "log", "ecs.version": "1.1.0"}, "output": {"ci_id": "SYSLOG_GW_PRODMON_DRP", "manager": "mon-local6", "handle_time": "2024-01-01 12:00:01", "actionable": false, "event_type": "heartbeat", "clear_type": "automatic", "additional_data": null, "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "node": "mon-xa01-lp.msnet.railb.be", "node_alias": "mon-xa01-lp.msnet.railb.be", "platform": "PROD", "raise_time": "2024-07-05 05:14:55", "clear_time": null, "wake_up_time": "2024-07-05 05:14:55", "action_class": "IT", "severity": 1, "summary": "HeartBeat Alarm", "top_level": null}}, {"input": {"severity": "err", "syslog_timestamp": "Jul  5 07:14:55", "tag": "A1617-ACC-SNMP_HB", "environment": "ACC", "@timestamp": "2024-07-05T05:14:55.000Z", "@version": "1", "hostname": "mon-xa01-lp.msnet.railb.be", "metric": "Heartbeat", "channel": "OPTIC_SNMP", "message": "Heart is beating", "ci_id": "SNMP_GW_DEV", "agent.id": "1d3fac61-44e2-49df-aeef-5f45132ee9c0", "agent.type": "filebeat", "agent.version": "7.5.2", "agent.ephemeral_id": "f65ec884-fc68-4492-9242-d7fbd7648d54", "agent.hostname": "mon-xa01-lp", "event.uuid": "f62d1e47-476f-431a-920e-701a90e0f875", "event.created": "2024-07-05T05:14:59.589Z", "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.timestamp": "2024-07-05T05:14:59.589Z", "event.kafka.key": null, "event.kafka.offset": 47234393, "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "host.name": "mon-xa01-lp", "log.original": "Jul  5 07:14:55 mon-xa01-lp.msnet.railb.be A1617-ACC-SNMP_HB: [ID - local6.err] OPTIC_SNMP SNMP_GW_DEV ACC Heartbeat 2024/07/05 07:14:55 : Heart is beating", "log.offset": 69404216, "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log", "input.type": "log", "ecs.version": "1.1.0"}, "output": {"ci_id": "SNMP_GW_DEV", "manager": "mon-local6", "handle_time": "2024-01-01 12:00:01", "actionable": false, "event_type": "heartbeat", "clear_type": "automatic", "additional_data": null, "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "node": "mon-xa01-lp.msnet.railb.be", "node_alias": "mon-xa01-lp.msnet.railb.be", "platform": "ACC", "raise_time": "2024-07-05 05:14:55", "clear_time": null, "wake_up_time": "2024-07-05 05:14:55", "action_class": "IT", "severity": 1, "summary": "HeartBeat Alarm", "top_level": null}}]}