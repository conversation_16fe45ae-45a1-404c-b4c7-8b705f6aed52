kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-archiving-job-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/a2110_olympus/run.py
  ARCHIVING_WEEKS: "#{archivingWeeks}#"
  DB_HOST: "#{databaseHost}#"
  DB_NAME: "#{databaseName}#"
  DB_PORT: "#{databasePort}#"
  DB_SCHEMA: "#{databaseSchema}#"
  DB_USER: "#{databaseUser}#"
  DEBUG: "0"
  LOGS_FOLDER: "/data/logs"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  OLYMPUS_AGENT_ID: "0"
