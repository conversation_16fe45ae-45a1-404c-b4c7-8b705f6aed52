apiVersion: build.openshift.io/v1
kind: BuildConfig
metadata:
  name: a2110-olympus-build-config
  namespace: a2110-olympus-monitoring
spec:
  failedBuildsHistoryLimit: 2
  successfulBuildsHistoryLimit: 2
  output:
    to:
      kind: DockerImage
      name: "artifactory.msnet.railb.be/a2110-docker/a2110-olympus-image:#{releaseName}#"
    pushSecret:
      name: artifactory-docker-registry
  runPolicy: Serial
  source:
    type: Git
    git:
      uri: "https://dev.azure.com/INFRABEL/a2110-olympus/_git/a2110-olympus"
      ref: "#{sourceBranch}#"
    sourceSecret:
      name: tokensecret
  strategy:
    type: Docker
    dockerStrategy:
      env:
        - name: POETRY_CACHE_DIR
          value: /tmp/.cache
        - name: PYTHONWARNINGS
          value: once
        - name: PIP_INDEX_URL
          value: "http://artifactory.msnet.railb.be:8081/artifactory/api/pypi/pypi-remote/simple"
        - name: PIP_TRUSTED_HOST
          value: artifactory.msnet.railb.be
      dockerfilePath: .openshift/a2110_olympus/Dockerfile
